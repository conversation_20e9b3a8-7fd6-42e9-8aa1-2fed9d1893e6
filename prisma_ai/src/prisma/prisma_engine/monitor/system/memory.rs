// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/memory.rs
// =================================================================================================
// Purpose: Monitors memory resources and provides detailed memory metrics. This file contains the
// logic for collecting memory-specific information such as total memory, available memory, used memory,
// swap usage, and memory fragmentation. It provides this information to the system_info module for
// system score calculation.
//
// Integration:
// - Internal Dependencies:
//   - system/mod.rs: Exports this module
//   - system/system_info.rs: Uses this module for memory monitoring
//   - system/types.rs: Uses types defined in this module
//
// - External Dependencies:
//   - sysinfo: For cross-platform memory information collection
//   - tokio: For async runtime and synchronization primitives
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - Linux: Full support for all memory metrics including detailed swap information
// - macOS: Support for most memory metrics, limited swap details
// =================================================================================================

use async_trait::async_trait;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time;
use tracing::{debug, error, info, warn};
use sysinfo::System;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{ResourceType, ResourceUsage};
use super::traits::{ResourceMonitor, MemoryMonitoring};
use super::types::{MemoryMetrics, SystemMonitorConfig};

/// Memory monitor that collects and provides memory metrics
#[derive(Debug)]
pub struct MemoryMonitor {
    /// Configuration for the memory monitor
    config: SystemMonitorConfig,
    /// System information from sysinfo
    system: Arc<RwLock<System>>,
    /// Cached memory metrics
    metrics: Arc<RwLock<MemoryMetrics>>,
    /// Background monitoring task
    monitoring_task: Option<JoinHandle<()>>,
}

impl MemoryMonitor {
    /// Create a new memory monitor
    pub fn new(config: SystemMonitorConfig) -> Self {
        info!("Creating MemoryMonitor with poll interval: {}ms", config.poll_interval_ms);

        // Initialize system with memory information
        let mut system = System::new();

        // Collect initial metrics immediately to avoid returning zeros
        system.refresh_memory();
        let initial_metrics = Self::collect_metrics_sync(&mut system);

        info!("Initial memory metrics: total={}MB, available={}MB, used={}MB",
            initial_metrics.total_bytes / 1024 / 1024,
            initial_metrics.available_bytes / 1024 / 1024,
            initial_metrics.used_bytes / 1024 / 1024);

        MemoryMonitor {
            config,
            system: Arc::new(RwLock::new(system)),
            metrics: Arc::new(RwLock::new(initial_metrics)),
            monitoring_task: None,
        }
    }

    /// Collect memory metrics from the system (synchronous version)
    fn collect_metrics_sync(system: &mut System) -> MemoryMetrics {
        // Get memory information from sysinfo
        let total_bytes = system.total_memory();
        let used_bytes = system.used_memory();
        let free_bytes = system.free_memory();
        let available_bytes = system.available_memory();

        // Calculate memory usage percentage
        let usage_percent = if total_bytes > 0 {
            (used_bytes as f64 / total_bytes as f64) * 100.0
        } else {
            0.0
        };

        // Get swap information
        let swap_total_bytes = system.total_swap();
        let swap_used_bytes = system.used_swap();

        // Calculate swap usage percentage
        let swap_usage_percent = if swap_total_bytes > 0 {
            (swap_used_bytes as f64 / swap_total_bytes as f64) * 100.0
        } else {
            0.0
        };

        MemoryMetrics {
            total_bytes,
            available_bytes,
            used_bytes,
            free_bytes,
            usage_percent,
            swap_total_bytes,
            swap_used_bytes,
            swap_usage_percent,
            timestamp: SystemTime::now(),
        }
    }

    /// Collect memory metrics from the system (async version)
    async fn collect_metrics(system: &mut System) -> MemoryMetrics {
        // Refresh memory information
        system.refresh_memory();
        Self::collect_metrics_sync(system)
    }

    /// Background monitoring loop
    async fn monitoring_loop(
        system: Arc<RwLock<System>>,
        metrics: Arc<RwLock<MemoryMetrics>>,
        poll_interval: Duration,
    ) {
        info!("Starting memory monitoring loop with interval {:?}", poll_interval);
        let mut interval = time::interval(poll_interval);

        loop {
            interval.tick().await;

            // Collect new metrics
            let new_metrics = {
                let mut sys = system.write().await;
                Self::collect_metrics(&mut sys).await
            };

            // Update cached metrics
            {
                let mut metrics_guard = metrics.write().await;
                *metrics_guard = new_metrics.clone();
            }

            debug!(
                "Memory metrics updated: total={}MB, used={}MB ({}%), swap={}MB ({}%)",
                new_metrics.total_bytes / 1024 / 1024,
                new_metrics.used_bytes / 1024 / 1024,
                new_metrics.usage_percent,
                new_metrics.swap_total_bytes / 1024 / 1024,
                new_metrics.swap_usage_percent,
            );
        }
    }
}

#[async_trait]
impl ResourceMonitor for MemoryMonitor {
    async fn start(&mut self) -> PrismaResult<()> {
        if self.monitoring_task.is_some() {
            warn!("Memory monitoring task is already running");
            return Ok(());
        }

        info!("Starting memory monitoring task");

        let system_clone = Arc::clone(&self.system);
        let metrics_clone = Arc::clone(&self.metrics);
        let poll_interval = Duration::from_millis(self.config.poll_interval_ms);

        let handle = tokio::spawn(async move {
            Self::monitoring_loop(system_clone, metrics_clone, poll_interval).await;
        });

        self.monitoring_task = Some(handle);
        Ok(())
    }

    async fn stop(&mut self) -> PrismaResult<()> {
        info!("Stopping memory monitoring task");

        if let Some(handle) = self.monitoring_task.take() {
            handle.abort();
            match handle.await {
                Ok(_) => info!("Memory monitoring task stopped gracefully"),
                Err(e) if e.is_cancelled() => info!("Memory monitoring task cancelled as expected"),
                Err(e) => error!("Error waiting for memory monitoring task to stop: {:?}", e),
            }
        } else {
            warn!("Memory monitoring task was not running");
        }

        Ok(())
    }

    fn get_resource_type(&self) -> ResourceType {
        ResourceType::Memory
    }

    async fn get_availability(&self) -> PrismaResult<ResourceUsage> {
        let metrics = self.metrics.read().await;
        Ok(ResourceUsage(100.0 - metrics.usage_percent))
    }

    fn get_poll_interval(&self) -> Duration {
        Duration::from_millis(self.config.poll_interval_ms)
    }

    fn set_poll_interval(&mut self, interval: Duration) {
        self.config.poll_interval_ms = interval.as_millis() as u64;
    }
}

#[async_trait]
impl MemoryMonitoring for MemoryMonitor {
    async fn get_memory_metrics(&self) -> PrismaResult<MemoryMetrics> {
        Ok(self.metrics.read().await.clone())
    }

    async fn get_total_memory(&self) -> PrismaResult<u64> {
        Ok(self.metrics.read().await.total_bytes)
    }

    async fn get_available_memory(&self) -> PrismaResult<u64> {
        Ok(self.metrics.read().await.available_bytes)
    }

    async fn get_memory_usage(&self) -> PrismaResult<f64> {
        Ok(self.metrics.read().await.usage_percent)
    }

    async fn get_total_swap(&self) -> PrismaResult<u64> {
        Ok(self.metrics.read().await.swap_total_bytes)
    }

    async fn get_used_swap(&self) -> PrismaResult<u64> {
        Ok(self.metrics.read().await.swap_used_bytes)
    }
}

impl Drop for MemoryMonitor {
    fn drop(&mut self) {
        if let Some(handle) = self.monitoring_task.take() {
            info!("MemoryMonitor dropped, aborting monitoring task");
            handle.abort();
        }
    }
}
