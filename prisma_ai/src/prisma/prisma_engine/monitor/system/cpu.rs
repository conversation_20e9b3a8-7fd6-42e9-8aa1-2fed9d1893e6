// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/cpu.rs
// =================================================================================================
// Purpose: Monitors CPU resources and provides detailed CPU metrics. This file contains the logic
// for collecting CPU-specific information such as usage, load averages, core counts, frequencies,
// and thermal data. It provides this information to the system_info module for system score calculation.
//
// Integration:
// - Internal Dependencies:
//   - system/mod.rs: Exports this module
//   - system/system_info.rs: Uses this module for CPU monitoring
//   - system/types.rs: Uses types defined in this module
//
// - External Dependencies:
//   - sysinfo: For cross-platform CPU information collection
//   - tokio: For async runtime and synchronization primitives
//   - tracing: For logging and instrumentation
//
// Platform Considerations:
// - Linux: Full support for all CPU metrics including thermal data
// - macOS: Support for most CPU metrics, limited thermal data
// =================================================================================================

use async_trait::async_trait;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time;
use tracing::{debug, error, info, warn};
use sysinfo::System;

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{ResourceType, ResourceUsage};
use super::traits::{ResourceMonitor, CpuMonitoring};
use super::types::{CpuMetrics, SystemMonitorConfig};

/// CPU monitor that collects and provides CPU metrics
#[derive(Debug)]
pub struct CpuMonitor {
    /// Configuration for the CPU monitor
    config: SystemMonitorConfig,
    /// System information from sysinfo
    system: Arc<RwLock<System>>,
    /// Cached CPU metrics
    metrics: Arc<RwLock<CpuMetrics>>,
    /// Background monitoring task
    monitoring_task: Option<JoinHandle<()>>,
}

impl CpuMonitor {
    /// Create a new CPU monitor
    pub fn new(config: SystemMonitorConfig) -> Self {
        info!("Creating CpuMonitor with poll interval: {}ms", config.poll_interval_ms);

        // Initialize system with CPU information
        let mut system = System::new();
        system.refresh_cpu();

        // Initialize CPU metrics with default values
        let metrics = CpuMetrics {
            usage_percent: 0.0,
            core_usage_percent: Vec::new(),
            physical_cores: num_cpus::get_physical(),
            logical_cores: num_cpus::get(),
            frequency_mhz: None,
            load_average: None,
            temperature_celsius: None,
            timestamp: SystemTime::now(),
        };

        CpuMonitor {
            config,
            system: Arc::new(RwLock::new(system)),
            metrics: Arc::new(RwLock::new(metrics)),
            monitoring_task: None,
        }
    }

    /// Collect CPU metrics from the system
    async fn collect_metrics(system: &mut System) -> CpuMetrics {
        // Refresh CPU information
        system.refresh_cpu();

        // Get global CPU usage
        let usage_percent = system.global_cpu_info().cpu_usage() as f64;

        // Get per-core usage
        let core_usage_percent = system.cpus()
            .iter()
            .map(|cpu| cpu.cpu_usage() as f64)
            .collect();

        // Get CPU frequency if available
        let frequency_mhz = Some(system.global_cpu_info().frequency() as f64);

        // Get load average if available
        let load_average = if cfg!(target_os = "linux") || cfg!(target_os = "macos") {
            // Use the static method instead of the instance method
            let load_avg = sysinfo::System::load_average();
            Some((load_avg.one, load_avg.five, load_avg.fifteen))
        } else {
            None
        };

        // Temperature monitoring - enhanced for Linux
        let temperature_celsius = if cfg!(target_os = "linux") {
            // On Linux, we could potentially get temperature data
            // For now, we'll leave it as None but this is where Linux-specific
            // temperature collection could be added in the future
            None
        } else {
            // macOS and other platforms
            None
        };

        CpuMetrics {
            usage_percent,
            core_usage_percent,
            physical_cores: num_cpus::get_physical(),
            logical_cores: num_cpus::get(),
            frequency_mhz,
            load_average,
            temperature_celsius,
            timestamp: SystemTime::now(),
        }
    }

    /// Background monitoring loop
    async fn monitoring_loop(
        system: Arc<RwLock<System>>,
        metrics: Arc<RwLock<CpuMetrics>>,
        poll_interval: Duration,
    ) {
        info!("Starting CPU monitoring loop with interval {:?}", poll_interval);
        let mut interval = time::interval(poll_interval);

        loop {
            interval.tick().await;

            // Collect new metrics
            let new_metrics = {
                let mut sys = system.write().await;
                Self::collect_metrics(&mut sys).await
            };

            // Update cached metrics
            {
                let mut metrics_guard = metrics.write().await;
                *metrics_guard = new_metrics.clone();
            }

            debug!(
                "CPU metrics updated: usage={}%, cores={}",
                new_metrics.usage_percent,
                new_metrics.logical_cores,
            );
        }
    }
}

#[async_trait]
impl ResourceMonitor for CpuMonitor {
    async fn start(&mut self) -> PrismaResult<()> {
        if self.monitoring_task.is_some() {
            warn!("CPU monitoring task is already running");
            return Ok(());
        }

        info!("Starting CPU monitoring task");

        let system_clone = Arc::clone(&self.system);
        let metrics_clone = Arc::clone(&self.metrics);
        let poll_interval = Duration::from_millis(self.config.poll_interval_ms);

        let handle = tokio::spawn(async move {
            Self::monitoring_loop(system_clone, metrics_clone, poll_interval).await;
        });

        self.monitoring_task = Some(handle);
        Ok(())
    }

    async fn stop(&mut self) -> PrismaResult<()> {
        info!("Stopping CPU monitoring task");

        if let Some(handle) = self.monitoring_task.take() {
            handle.abort();
            match handle.await {
                Ok(_) => info!("CPU monitoring task stopped gracefully"),
                Err(e) if e.is_cancelled() => info!("CPU monitoring task cancelled as expected"),
                Err(e) => error!("Error waiting for CPU monitoring task to stop: {:?}", e),
            }
        } else {
            warn!("CPU monitoring task was not running");
        }

        Ok(())
    }

    fn get_resource_type(&self) -> ResourceType {
        ResourceType::CPU
    }

    async fn get_availability(&self) -> PrismaResult<ResourceUsage> {
        let metrics = self.metrics.read().await;
        Ok(ResourceUsage(100.0 - metrics.usage_percent))
    }

    fn get_poll_interval(&self) -> Duration {
        Duration::from_millis(self.config.poll_interval_ms)
    }

    fn set_poll_interval(&mut self, interval: Duration) {
        self.config.poll_interval_ms = interval.as_millis() as u64;
    }
}

#[async_trait]
impl CpuMonitoring for CpuMonitor {
    async fn get_cpu_metrics(&self) -> PrismaResult<CpuMetrics> {
        Ok(self.metrics.read().await.clone())
    }

    async fn get_physical_core_count(&self) -> PrismaResult<usize> {
        Ok(self.metrics.read().await.physical_cores)
    }

    async fn get_logical_core_count(&self) -> PrismaResult<usize> {
        Ok(self.metrics.read().await.logical_cores)
    }

    async fn get_cpu_usage(&self) -> PrismaResult<f64> {
        Ok(self.metrics.read().await.usage_percent)
    }

    async fn get_load_average(&self) -> PrismaResult<Option<(f64, f64, f64)>> {
        Ok(self.metrics.read().await.load_average)
    }
}

impl Drop for CpuMonitor {
    fn drop(&mut self) {
        if let Some(handle) = self.monitoring_task.take() {
            info!("CpuMonitor dropped, aborting monitoring task");
            handle.abort();
        }
    }
}
