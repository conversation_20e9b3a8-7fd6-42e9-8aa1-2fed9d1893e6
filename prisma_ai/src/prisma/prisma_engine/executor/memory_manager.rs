// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/memory_manager.rs
// =================================================================================================
// Purpose: Provides memory management services for the executor system. This includes allocating
// and retrieving memory for tasks, managing memory lifecycle, and implementing memory eviction
// strategies. It also handles long-term memory (LTM) integration through the storage system.
//
// Integration:
// - `executor.rs`: Uses MemoryManager to allocate and retrieve memory for tasks
// - `cache_manager.rs`: Coordinates with CacheManager for memory-aware caching
// - `context_manager.rs`: Coordinates with ContextManager for memory-aware contexts
// - Storage Service: Interfaces with SurrealDB for persistent LTM storage
// - Embedding Service: Coordinates with embedding models for semantic storage
// =================================================================================================

use std::any::Any;
use std::collections::{HashMap, VecDeque};
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tracing::{debug, error, info, warn};

use crate::err::{GenericError, PrismaResult};
use crate::prisma::prisma_engine::types::TaskId;
use crate::storage::SurrealDbConnection;
use crate::storage::traits::DataStore;
use chrono;

/// Configuration for the MemoryManager
#[derive(Debug, Clone)]
pub struct MemoryManagerConfig {
    /// Maximum size of the memory in bytes
    pub max_memory_size_bytes: usize,

    /// Maximum number of items to keep in memory
    pub max_memory_items: usize,

    /// Time-to-live for memory items in seconds
    pub memory_ttl_seconds: u64,

    /// Whether to enable memory pooling
    pub enable_memory_pooling: bool,

    /// Maximum size of the memory pool
    pub max_pool_size: usize,

    /// Whether to enable long-term memory persistence
    pub enable_ltm: bool,

    /// Whether to enable semantic search
    pub enable_semantic_search: bool,
}

impl Default for MemoryManagerConfig {
    fn default() -> Self {
        Self {
            max_memory_size_bytes: 1024 * 1024 * 1024, // 1 GB
            max_memory_items: 100000,
            memory_ttl_seconds: 3600 * 24, // 24 hours
            enable_memory_pooling: true,
            max_pool_size: 1000,
            enable_ltm: true,
            enable_semantic_search: true,
        }
    }
}

/// Metadata for a memory item
#[derive(Debug, Clone)]
struct MemoryItemMetadata {
    /// Size of the item in bytes (approximate)
    size_bytes: usize,

    /// When the item was created
    created_at: Instant,

    /// When the item was last accessed
    last_accessed: Instant,

    /// Number of times the item has been accessed
    access_count: usize,

    /// Whether the item is pinned (cannot be evicted)
    pinned: bool,

    /// Task ID associated with this memory item
    task_id: Option<TaskId>,
}

/// A structured container for different types of memory
#[derive(Debug)]
struct MemoryContainer {
    /// Short-term memory items (recent events, observations, etc.)
    short_term: Vec<Box<dyn Any + Send + Sync>>,

    /// Working memory (key-value store for active processing)
    working: HashMap<String, Box<dyn Any + Send + Sync>>,

    /// Current context (if any)
    context: Option<Box<dyn Any + Send + Sync>>,

    /// Additional metadata about the memory
    metadata: HashMap<String, String>,
}

/// A memory item
struct MemoryItem {
    /// The stored value
    value: Box<dyn Any + Send + Sync>,

    /// Metadata for the memory item
    metadata: MemoryItemMetadata,
}

/// Eviction strategy for the memory
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum EvictionStrategy {
    /// Least recently used
    LRU,

    /// Least frequently used
    LFU,

    /// First in, first out
    FIFO,
}

/// Statistics for the MemoryManager
#[derive(Debug, Clone)]
pub struct MemoryManagerStats {
    /// Current size of the memory in bytes
    pub cache_size_bytes: usize,

    /// Number of items in memory
    pub cache_items: usize,

    /// Number of memory hits
    pub cache_hits: usize,

    /// Number of memory misses
    pub cache_misses: usize,

    /// Number of memory evictions
    pub cache_evictions: usize,

    /// Number of items in the memory pool
    pub pool_items: usize,

    /// Number of pool hits
    pub pool_hits: usize,

    /// Number of pool misses
    pub pool_misses: usize,

    /// Whether long-term memory is enabled
    pub ltm_enabled: bool,

    /// Whether semantic search is enabled
    pub semantic_search_enabled: bool,
}

/// Provides memory management services for the executor system
pub struct MemoryManager {
    /// Configuration for the MemoryManager
    config: MemoryManagerConfig,

    /// The memory itself
    memory: RwLock<HashMap<String, MemoryItem>>,

    /// Access order for LRU eviction
    access_order: RwLock<VecDeque<String>>,

    /// Creation order for FIFO eviction
    creation_order: RwLock<VecDeque<String>>,

    /// Current size of the memory in bytes
    memory_size_bytes: RwLock<usize>,

    /// Statistics for the MemoryManager
    stats: RwLock<MemoryManagerStats>,

    /// Memory pool for reusing memory allocations
    memory_pool: RwLock<Vec<Box<dyn Any + Send + Sync>>>,

    /// Current eviction strategy
    eviction_strategy: RwLock<EvictionStrategy>,

    /// Connection to the storage system for LTM
    storage: Option<Arc<SurrealDbConnection>>,
}

impl MemoryManager {
    /// Creates a new MemoryManager with the given configuration
    pub fn new(config: MemoryManagerConfig, storage: Option<Arc<SurrealDbConnection>>) -> Self {
        info!("Creating new MemoryManager with config: {:?}", config);

        // Store config values before moving config
        let enable_ltm = config.enable_ltm;
        let enable_semantic_search = config.enable_semantic_search;

        Self {
            config,
            memory: RwLock::new(HashMap::new()),
            access_order: RwLock::new(VecDeque::new()),
            creation_order: RwLock::new(VecDeque::new()),
            memory_size_bytes: RwLock::new(0),
            stats: RwLock::new(MemoryManagerStats {
                cache_size_bytes: 0,
                cache_items: 0,
                cache_hits: 0,
                cache_misses: 0,
                cache_evictions: 0,
                pool_items: 0,
                pool_hits: 0,
                pool_misses: 0,
                ltm_enabled: enable_ltm,
                semantic_search_enabled: enable_semantic_search,
            }),
            memory_pool: RwLock::new(Vec::new()),
            eviction_strategy: RwLock::new(EvictionStrategy::LRU),
            storage,
        }
    }

    /// Creates a new MemoryManager with default configuration
    pub fn default() -> Self {
        Self::new(MemoryManagerConfig::default(), None)
    }

    /// Allocates memory for a task
    pub fn allocate_memory(&self, task_id: TaskId, size_bytes: usize) -> PrismaResult<String> {
        let memory_key = format!("memory_{}", task_id);
        debug!("Allocating {} bytes for task {} with key {}", size_bytes, task_id, memory_key);

        // Check if we can reuse memory from the pool
        if self.config.enable_memory_pooling {
            let mut pool = self.memory_pool.write().unwrap();
            if !pool.is_empty() {
                // Pop memory from the pool
                pool.pop();

                // Update stats
                let mut stats = self.stats.write().unwrap();
                stats.pool_items = pool.len();
                stats.pool_hits += 1;

                debug!("Reused memory from pool for task {}", task_id);
                return Ok(memory_key);
            } else {
                // Update stats
                let mut stats = self.stats.write().unwrap();
                stats.pool_misses += 1;
            }
        }

        // Check if we need to evict items to make room
        self.ensure_capacity(size_bytes)?;

        // Create a proper memory item with a structured memory container
        // This allows for storing different types of memory (short-term, working, etc.)
        let memory_container = MemoryContainer {
            short_term: Vec::new(),
            working: HashMap::new(),
            context: None,
            metadata: HashMap::new(),
        };

        let memory_item = MemoryItem {
            value: Box::new(memory_container),
            metadata: MemoryItemMetadata {
                size_bytes,
                created_at: Instant::now(),
                last_accessed: Instant::now(),
                access_count: 0,
                pinned: false,
                task_id: Some(task_id),
            },
        };

        // Store the memory item
        {
            let mut memory = self.memory.write().unwrap();
            memory.insert(memory_key.clone(), memory_item);

            // Update creation order
            let mut creation_order = self.creation_order.write().unwrap();
            creation_order.push_back(memory_key.clone());

            // Update access order
            let mut access_order = self.access_order.write().unwrap();
            access_order.push_back(memory_key.clone());

            // Update memory size
            let mut memory_size_bytes = self.memory_size_bytes.write().unwrap();
            *memory_size_bytes += size_bytes;

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_items = memory.len();
            stats.cache_size_bytes = *memory_size_bytes;
        }

        debug!("Allocated memory for task {} with key {}", task_id, memory_key);
        Ok(memory_key)
    }

    /// Stores a value in memory
    pub fn store<T: Any + Send + Sync>(&self, key: &str, value: T) -> PrismaResult<()> {
        debug!("Storing value for key: {}", key);

        // Get the size of the value (approximate)
        let size_bytes = std::mem::size_of_val(&value);

        // Check if we need to evict items to make room
        self.ensure_capacity(size_bytes)?;

        // Create a memory item
        let memory_item = MemoryItem {
            value: Box::new(value),
            metadata: MemoryItemMetadata {
                size_bytes,
                created_at: Instant::now(),
                last_accessed: Instant::now(),
                access_count: 0,
                pinned: false,
                task_id: None,
            },
        };

        // Store the memory item
        {
            let mut memory = self.memory.write().unwrap();

            // Check if the key already exists before inserting
            let key_exists = memory.contains_key(key);
            let existing_size = if key_exists {
                memory.get(key).map(|item| item.metadata.size_bytes).unwrap_or(0)
            } else {
                0
            };

            memory.insert(key.to_string(), memory_item);

            // Update creation order
            let mut creation_order = self.creation_order.write().unwrap();
            if !creation_order.contains(&key.to_string()) {
                creation_order.push_back(key.to_string());
            }

            // Update access order
            let mut access_order = self.access_order.write().unwrap();
            if let Some(index) = access_order.iter().position(|k| k == key) {
                access_order.remove(index);
            }
            access_order.push_back(key.to_string());

            // Update memory size
            let mut memory_size_bytes = self.memory_size_bytes.write().unwrap();
            if key_exists {
                // Replace existing item: subtract old size, add new size
                *memory_size_bytes = memory_size_bytes.saturating_sub(existing_size);
                *memory_size_bytes += size_bytes;
            } else {
                // New item: just add the size
                *memory_size_bytes += size_bytes;
            }

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_items = memory.len();
            stats.cache_size_bytes = *memory_size_bytes;
        }

        debug!("Stored value for key: {}", key);
        Ok(())
    }

    /// Stores a value in memory with a task ID
    pub fn store_with_task_id<T: Any + Send + Sync>(
        &self,
        key: &str,
        value: T,
        task_id: TaskId,
    ) -> PrismaResult<()> {
        debug!("Storing value for key: {} with task ID: {}", key, task_id);

        // Get the size of the value (approximate)
        let size_bytes = std::mem::size_of_val(&value);

        // Check if we need to evict items to make room
        self.ensure_capacity(size_bytes)?;

        // Create a memory item
        let memory_item = MemoryItem {
            value: Box::new(value),
            metadata: MemoryItemMetadata {
                size_bytes,
                created_at: Instant::now(),
                last_accessed: Instant::now(),
                access_count: 0,
                pinned: false,
                task_id: Some(task_id),
            },
        };

        // Store the memory item
        {
            let mut memory = self.memory.write().unwrap();

            // Check if the key already exists before inserting
            let key_exists = memory.contains_key(key);
            let existing_size = if key_exists {
                memory.get(key).map(|item| item.metadata.size_bytes).unwrap_or(0)
            } else {
                0
            };

            memory.insert(key.to_string(), memory_item);

            // Update creation order
            let mut creation_order = self.creation_order.write().unwrap();
            if !creation_order.contains(&key.to_string()) {
                creation_order.push_back(key.to_string());
            }

            // Update access order
            let mut access_order = self.access_order.write().unwrap();
            if let Some(index) = access_order.iter().position(|k| k == key) {
                access_order.remove(index);
            }
            access_order.push_back(key.to_string());

            // Update memory size
            let mut memory_size_bytes = self.memory_size_bytes.write().unwrap();
            if key_exists {
                // Replace existing item: subtract old size, add new size
                *memory_size_bytes = memory_size_bytes.saturating_sub(existing_size);
                *memory_size_bytes += size_bytes;
            } else {
                // New item: just add the size
                *memory_size_bytes += size_bytes;
            }

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_items = memory.len();
            stats.cache_size_bytes = *memory_size_bytes;
        }

        debug!("Stored value for key: {} with task ID: {}", key, task_id);
        Ok(())
    }

    /// Retrieves a value from memory
    pub fn retrieve<T: Any + Send + Sync + Clone>(&self, key: &str) -> PrismaResult<Option<T>> {
        debug!("Retrieving value for key: {}", key);

        // Check if the key exists in memory
        let mut memory = self.memory.write().unwrap();
        if let Some(mut item) = memory.remove(key) {
            // Update access metadata
            item.metadata.last_accessed = Instant::now();
            item.metadata.access_count += 1;

            // Update access order
            let mut access_order = self.access_order.write().unwrap();
            if let Some(index) = access_order.iter().position(|k| k == key) {
                access_order.remove(index);
            }
            access_order.push_back(key.to_string());

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_hits += 1;

            // Check if the value is of the expected type
            let value_ref = &item.value;
            if let Some(typed_value) = value_ref.downcast_ref::<T>() {
                // Clone the value for returning
                let result_value = typed_value.clone();

                // Put the item back in memory
                memory.insert(key.to_string(), item);

                debug!("Memory hit for key: {}", key);
                return Ok(Some(result_value));
            } else {
                // Type mismatch, put the original item back
                memory.insert(key.to_string(), item);

                error!("Type mismatch for memory key: {}", key);
                return Err(GenericError::from(format!(
                    "Type mismatch for memory key: {}",
                    key
                )));
            }
        } else {
            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_misses += 1;

            debug!("Memory miss for key: {}", key);
            Ok(None)
        }
    }

    /// Pins a memory item so it cannot be evicted
    pub fn pin(&self, key: &str) -> PrismaResult<()> {
        debug!("Pinning memory item: {}", key);

        let mut memory = self.memory.write().unwrap();
        if let Some(mut item) = memory.remove(key) {
            item.metadata.pinned = true;
            memory.insert(key.to_string(), item);
            debug!("Pinned memory item: {}", key);
            Ok(())
        } else {
            error!("Memory item not found: {}", key);
            Err(GenericError::from(format!("Memory item not found: {}", key)).into())
        }
    }

    /// Unpins a memory item so it can be evicted
    pub fn unpin(&self, key: &str) -> PrismaResult<()> {
        debug!("Unpinning memory item: {}", key);

        let mut memory = self.memory.write().unwrap();
        if let Some(mut item) = memory.remove(key) {
            item.metadata.pinned = false;
            memory.insert(key.to_string(), item);
            debug!("Unpinned memory item: {}", key);
            Ok(())
        } else {
            error!("Memory item not found: {}", key);
            Err(GenericError::from(format!("Memory item not found: {}", key)).into())
        }
    }

    /// Evicts a memory item
    pub fn evict(&self, key: &str) -> PrismaResult<bool> {
        debug!("Evicting memory item: {}", key);

        let mut memory = self.memory.write().unwrap();
        if let Some(item) = memory.remove(key) {
            // Update memory size
            let mut memory_size_bytes = self.memory_size_bytes.write().unwrap();
            *memory_size_bytes = memory_size_bytes.saturating_sub(item.metadata.size_bytes);

            // Update creation order
            let mut creation_order = self.creation_order.write().unwrap();
            if let Some(index) = creation_order.iter().position(|k| k == key) {
                creation_order.remove(index);
            }

            // Update access order
            let mut access_order = self.access_order.write().unwrap();
            if let Some(index) = access_order.iter().position(|k| k == key) {
                access_order.remove(index);
            }

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_items = memory.len();
            stats.cache_size_bytes = *memory_size_bytes;
            stats.cache_evictions += 1;

            // Add to pool if enabled
            if self.config.enable_memory_pooling {
                let mut pool = self.memory_pool.write().unwrap();
                if pool.len() < self.config.max_pool_size {
                    pool.push(item.value);
                    stats.pool_items = pool.len();
                }
            }

            debug!("Evicted memory item: {}", key);
            Ok(true)
        } else {
            debug!("Memory item not found: {}", key);
            Ok(false)
        }
    }

    /// Clears all memory for a task
    pub fn clear_task_memory(&self, task_id: TaskId) -> PrismaResult<()> {
        debug!("Clearing memory for task {}", task_id);

        let mut memory = self.memory.write().unwrap();
        let mut keys_to_remove = Vec::new();

        // Find all memory items for this task
        for (key, item) in memory.iter() {
            if let Some(item_task_id) = item.metadata.task_id {
                if item_task_id == task_id {
                    keys_to_remove.push(key.clone());
                }
            }
        }

        // Remove the items
        let mut total_size = 0;
        for key in &keys_to_remove {
            if let Some(item) = memory.remove(key) {
                total_size += item.metadata.size_bytes;

                // Update creation order
                let mut creation_order = self.creation_order.write().unwrap();
                if let Some(index) = creation_order.iter().position(|k| k == key) {
                    creation_order.remove(index);
                }

                // Update access order
                let mut access_order = self.access_order.write().unwrap();
                if let Some(index) = access_order.iter().position(|k| k == key) {
                    access_order.remove(index);
                }

                // Add to pool if enabled
                if self.config.enable_memory_pooling {
                    let mut pool = self.memory_pool.write().unwrap();
                    if pool.len() < self.config.max_pool_size {
                        pool.push(item.value);
                    }
                }
            }
        }

        // Update memory size
        {
            let mut memory_size_bytes = self.memory_size_bytes.write().unwrap();
            *memory_size_bytes = memory_size_bytes.saturating_sub(total_size);

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_items = memory.len();
            stats.cache_size_bytes = *memory_size_bytes;
            stats.cache_evictions += keys_to_remove.len();
            stats.pool_items = self.memory_pool.read().unwrap().len();
        }

        debug!("Cleared {} memory items for task {}", keys_to_remove.len(), task_id);
        Ok(())
    }

    /// Sets the eviction strategy
    pub fn set_eviction_strategy(&self, strategy: EvictionStrategy) {
        info!("Setting eviction strategy to {:?}", strategy);
        let mut eviction_strategy = self.eviction_strategy.write().unwrap();
        *eviction_strategy = strategy;
    }

    /// Gets the current eviction strategy
    pub fn get_eviction_strategy(&self) -> EvictionStrategy {
        *self.eviction_strategy.read().unwrap()
    }

    /// Gets the current memory statistics
    pub fn get_stats(&self) -> MemoryManagerStats {
        self.stats.read().unwrap().clone()
    }

    /// Stores a value in long-term memory
    pub async fn store_ltm<T: serde::Serialize + Send + Sync + 'static>(
        &self,
        key: &str,
        value: T,
        embedding: Option<Vec<f32>>,
    ) -> PrismaResult<()> {
        if !self.config.enable_ltm {
            debug!("LTM disabled, not storing value for key: {}", key);
            return Ok(());
        }

        debug!("Storing value in LTM for key: {}", key);

        // Check if we have a storage connection
        let storage = match &self.storage {
            Some(storage) => storage,
            None => {
                error!("No storage connection available for LTM");
                return Err(GenericError::from("No storage connection available for LTM").into());
            }
        };

        // Serialize the value to JSON
        let value_json = match serde_json::to_value(&value) {
            Ok(json) => json,
            Err(e) => {
                error!("Failed to serialize value for LTM: {}", e);
                return Err(GenericError::from(format!("Failed to serialize value for LTM: {}", e)).into());
            }
        };

        // Create a record for storage
        let mut record = serde_json::Map::new();
        record.insert("key".to_string(), serde_json::Value::String(key.to_string()));
        record.insert("value".to_string(), value_json);
        record.insert("created_at".to_string(), serde_json::Value::String(chrono::Utc::now().to_rfc3339()));

        // Add embedding if provided
        if let Some(ref embedding_vec) = embedding {
            let embedding_json = serde_json::to_value(embedding_vec).unwrap_or_else(|_| serde_json::Value::Null);
            record.insert("embedding".to_string(), embedding_json);
        }

        // Store in SurrealDB
        let query = "CREATE memory SET key = $key, value = $value, created_at = $created_at";

        if let Some(ref embedding_vec) = embedding {
            // If embedding is provided, use a query that includes it
            let query_with_embedding = "CREATE memory SET key = $key, value = $value, created_at = $created_at, embedding = $embedding";

            // Convert params to the expected format for SurrealDB
            let params: Vec<(&str, &str)> = vec![
                ("key", key),
                // We can't directly use these complex values with the query method
                // In a real implementation, we would need to use a different approach
            ];

            match storage.query::<serde_json::Value>(query_with_embedding, &params).await {
                Ok(_) => {
                    debug!("Stored value with embedding in LTM for key: {}", key);
                    Ok(())
                }
                Err(e) => {
                    error!("Failed to store value with embedding in LTM: {}", e);
                    Err(e.into())
                }
            }
        } else {
            // If no embedding, use the simpler query
            // Convert params to the expected format for SurrealDB
            let params: Vec<(&str, &str)> = vec![
                ("key", key),
                // We can't directly use these complex values with the query method
                // In a real implementation, we would need to use a different approach
            ];

            match storage.query::<serde_json::Value>(query, &params).await {
                Ok(_) => {
                    debug!("Stored value in LTM for key: {}", key);
                    Ok(())
                }
                Err(e) => {
                    error!("Failed to store value in LTM: {}", e);
                    Err(e.into())
                }
            }
        }
    }

    /// Retrieves a value from long-term memory by exact key match
    pub async fn retrieve_ltm<T: serde::de::DeserializeOwned + Send + Sync>(
        &self,
        key: &str,
    ) -> PrismaResult<Option<T>> {
        if !self.config.enable_ltm {
            debug!("LTM disabled, not retrieving value for key: {}", key);
            return Ok(None);
        }

        debug!("Retrieving value from LTM for key: {}", key);

        // Check if we have a storage connection
        let storage = match &self.storage {
            Some(storage) => storage,
            None => {
                error!("No storage connection available for LTM");
                return Err(GenericError::from("No storage connection available for LTM").into());
            }
        };

        // Query SurrealDB for the record
        let query = "SELECT value FROM memory WHERE key = $key LIMIT 1";

        // Convert params to the expected format for SurrealDB
        let params: Vec<(&str, &str)> = vec![
            ("key", key),
        ];

        match storage.query::<serde_json::Value>(query, &params).await {
            Ok(result_vec) => {
                // Check if we got any results
                if result_vec.is_empty() {
                    debug!("No value found in LTM for key: {}", key);
                    return Ok(None);
                }

                // Get the first result
                let first_result = match result_vec.first() {
                    Some(result) => result,
                    None => {
                        error!("Unexpected result format from SurrealDB");
                        return Err(GenericError::from("Unexpected result format from SurrealDB").into());
                    }
                };

                // Get the value field
                let value = match first_result.get("value") {
                    Some(value) => value,
                    None => {
                        error!("No value field in result from SurrealDB");
                        return Err(GenericError::from("No value field in result from SurrealDB").into());
                    }
                };

                // Deserialize the value
                match serde_json::from_value::<T>(value.clone()) {
                    Ok(typed_value) => {
                        debug!("Retrieved value from LTM for key: {}", key);
                        Ok(Some(typed_value))
                    }
                    Err(e) => {
                        error!("Failed to deserialize value from LTM: {}", e);
                        Err(GenericError::from(format!("Failed to deserialize value from LTM: {}", e)).into())
                    }
                }
            }
            Err(e) => {
                error!("Failed to retrieve value from LTM: {}", e);
                Err(e.into())
            }
        }
    }

    /// Search for values in long-term memory by semantic similarity
    pub async fn search_ltm<T: serde::de::DeserializeOwned + Send + Sync>(
        &self,
        query_embedding: &[f32],
        limit: usize,
    ) -> PrismaResult<Vec<(T, f32)>> {
        if !self.config.enable_ltm || !self.config.enable_semantic_search {
            debug!("LTM or semantic search disabled, not searching");
            return Ok(Vec::new());
        }

        debug!("Searching LTM with embedding of length {}", query_embedding.len());

        // Check if we have a storage connection
        let storage = match &self.storage {
            Some(storage) => storage,
            None => {
                error!("No storage connection available for LTM");
                return Err(GenericError::from("No storage connection available for LTM").into());
            }
        };

        // Convert query embedding to JSON
        let query_embedding_json = match serde_json::to_value(query_embedding) {
            Ok(json) => json,
            Err(e) => {
                error!("Failed to serialize query embedding: {}", e);
                return Err(GenericError::from(format!("Failed to serialize query embedding: {}", e)).into());
            }
        };

        // Query SurrealDB for similar embeddings using vector similarity search
        let query = "SELECT value, vector::similarity::cosine(embedding, $query_embedding) as similarity \
                     FROM memory \
                     WHERE embedding IS NOT NULL \
                     ORDER BY similarity DESC \
                     LIMIT $limit";

        // Convert params to the expected format for SurrealDB
        // Note: This is a simplified approach. In a real implementation, we would need to handle
        // complex parameters like embeddings differently.
        let limit_str = limit.to_string();
        let params: Vec<(&str, &str)> = vec![
            ("limit", &limit_str),
            // We can't directly use the embedding with the query method
            // In a real implementation, we would need to use a different approach
        ];

        match storage.query::<serde_json::Value>(query, &params).await {
            Ok(result_vec) => {
                // Process each result
                let mut results = Vec::new();
                for item in &result_vec {
                    // Get the value field
                    let value = match item.get("value") {
                        Some(value) => value,
                        None => {
                            warn!("No value field in result from SurrealDB, skipping");
                            continue;
                        }
                    };

                    // Get the similarity score
                    let similarity = match item.get("similarity") {
                        Some(similarity) => match similarity.as_f64() {
                            Some(score) => score as f32,
                            None => {
                                warn!("Invalid similarity score in result from SurrealDB, skipping");
                                continue;
                            }
                        },
                        None => {
                            warn!("No similarity field in result from SurrealDB, skipping");
                            continue;
                        }
                    };

                    // Deserialize the value
                    match serde_json::from_value::<T>(value.clone()) {
                        Ok(typed_value) => {
                            results.push((typed_value, similarity));
                        }
                        Err(e) => {
                            warn!("Failed to deserialize value from LTM: {}, skipping", e);
                            continue;
                        }
                    }
                }

                debug!("Found {} similar items in LTM", results.len());
                Ok(results)
            }
            Err(e) => {
                error!("Failed to search LTM: {}", e);
                Err(e.into())
            }
        }
    }

    /// Cleans up expired memory items
    pub fn cleanup_expired(&self) -> PrismaResult<usize> {
        debug!("Cleaning up expired memory items");

        let mut memory = self.memory.write().unwrap();
        let mut keys_to_remove = Vec::new();
        let now = Instant::now();
        let ttl = Duration::from_secs(self.config.memory_ttl_seconds);

        // Find all expired memory items
        for (key, item) in memory.iter() {
            if !item.metadata.pinned && now.duration_since(item.metadata.created_at) > ttl {
                keys_to_remove.push(key.clone());
            }
        }

        // Remove the items
        let mut total_size = 0;
        for key in &keys_to_remove {
            if let Some(item) = memory.remove(key) {
                total_size += item.metadata.size_bytes;

                // Update creation order
                let mut creation_order = self.creation_order.write().unwrap();
                if let Some(index) = creation_order.iter().position(|k| k == key) {
                    creation_order.remove(index);
                }

                // Update access order
                let mut access_order = self.access_order.write().unwrap();
                if let Some(index) = access_order.iter().position(|k| k == key) {
                    access_order.remove(index);
                }

                // Add to pool if enabled
                if self.config.enable_memory_pooling {
                    let mut pool = self.memory_pool.write().unwrap();
                    if pool.len() < self.config.max_pool_size {
                        pool.push(item.value);
                    }
                }
            }
        }

        // Update memory size
        {
            let mut memory_size_bytes = self.memory_size_bytes.write().unwrap();
            *memory_size_bytes -= total_size;

            // Update stats
            let mut stats = self.stats.write().unwrap();
            stats.cache_items = memory.len();
            stats.cache_size_bytes = *memory_size_bytes;
            stats.cache_evictions += keys_to_remove.len();
            stats.pool_items = self.memory_pool.read().unwrap().len();
        }

        debug!("Cleaned up {} expired memory items", keys_to_remove.len());
        Ok(keys_to_remove.len())
    }

    /// Ensures that the memory has enough capacity for a new item
    fn ensure_capacity(&self, size_bytes: usize) -> PrismaResult<()> {
        let current_size = *self.memory_size_bytes.read().unwrap();
        let current_items = self.memory.read().unwrap().len();

        // Check if we need to evict items
        if current_size + size_bytes > self.config.max_memory_size_bytes
            || current_items >= self.config.max_memory_items
        {
            debug!(
                "Memory capacity exceeded, evicting items (size: {} + {} > {}, items: {} >= {})",
                current_size,
                size_bytes,
                self.config.max_memory_size_bytes,
                current_items,
                self.config.max_memory_items
            );

            // Evict items until we have enough space
            let mut evicted_size = 0;
            let mut evicted_count = 0;

            while (current_size.saturating_sub(evicted_size) + size_bytes > self.config.max_memory_size_bytes
                || current_items.saturating_sub(evicted_count) >= self.config.max_memory_items)
                && evicted_count < current_items
            {
                // Choose an item to evict based on the strategy
                if let Some(key) = self.choose_item_to_evict()? {
                    if let Some(item) = self.memory.write().unwrap().remove(&key) {
                        // Update eviction stats
                        evicted_size += item.metadata.size_bytes;
                        evicted_count += 1;

                        // Update creation order
                        let mut creation_order = self.creation_order.write().unwrap();
                        if let Some(index) = creation_order.iter().position(|k| k == &key) {
                            creation_order.remove(index);
                        }

                        // Update access order
                        let mut access_order = self.access_order.write().unwrap();
                        if let Some(index) = access_order.iter().position(|k| k == &key) {
                            access_order.remove(index);
                        }

                        // Add to pool if enabled
                        if self.config.enable_memory_pooling {
                            let mut pool = self.memory_pool.write().unwrap();
                            if pool.len() < self.config.max_pool_size {
                                pool.push(item.value);
                            }
                        }

                        debug!("Evicted memory item: {}", key);
                    }
                } else {
                    // No more items to evict
                    break;
                }
            }

            // Update memory size
            {
                let mut memory_size_bytes = self.memory_size_bytes.write().unwrap();
                *memory_size_bytes = memory_size_bytes.saturating_sub(evicted_size);

                // Update stats
                let mut stats = self.stats.write().unwrap();
                stats.cache_items = self.memory.read().unwrap().len();
                stats.cache_size_bytes = *memory_size_bytes;
                stats.cache_evictions += evicted_count;
                stats.pool_items = self.memory_pool.read().unwrap().len();
            }

            debug!("Evicted {} items ({} bytes)", evicted_count, evicted_size);
        }

        Ok(())
    }

    /// Chooses an item to evict based on the current eviction strategy
    fn choose_item_to_evict(&self) -> PrismaResult<Option<String>> {
        let strategy = *self.eviction_strategy.read().unwrap();

        match strategy {
            EvictionStrategy::LRU => {
                // Choose the least recently used item
                let access_order = self.access_order.read().unwrap();
                let memory = self.memory.read().unwrap();

                for key in access_order.iter() {
                    if let Some(item) = memory.get(key) {
                        if !item.metadata.pinned {
                            return Ok(Some(key.clone()));
                        }
                    }
                }

                Ok(None)
            }
            EvictionStrategy::LFU => {
                // Choose the least frequently used item
                let memory = self.memory.read().unwrap();
                let mut min_access_count = usize::MAX;
                let mut min_key = None;

                for (key, item) in memory.iter() {
                    if !item.metadata.pinned && item.metadata.access_count < min_access_count {
                        min_access_count = item.metadata.access_count;
                        min_key = Some(key.clone());
                    }
                }

                Ok(min_key)
            }
            EvictionStrategy::FIFO => {
                // Choose the oldest item
                let creation_order = self.creation_order.read().unwrap();
                let memory = self.memory.read().unwrap();

                for key in creation_order.iter() {
                    if let Some(item) = memory.get(key) {
                        if !item.metadata.pinned {
                            return Ok(Some(key.clone()));
                        }
                    }
                }

                Ok(None)
            }
        }
    }
}
