// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/system_scores/generics.rs
// =================================================================================================
// Purpose: Provides generic utilities and helper functions for SystemScore-related operations.
// This file contains reusable components that can be used across different system scoring
// mechanisms to avoid code duplication and ensure consistent behavior.
//
// Integration:
// - Internal Dependencies:
//   - system_scores/mod.rs: Exports this module
//   - system_scores/logic.rs: May use these utilities for score evaluation
//   - system_scores/cpu_score.rs: May use these utilities for CPU scoring
//   - system_scores/memory_score.rs: May use these utilities for memory scoring
//   - system_scores/disk_score.rs: May use these utilities for disk scoring
//   - system_scores/network_score.rs: May use these utilities for network scoring
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses SystemScore, ResourceType, and ResourceUsage types
//   - std::collections: For data structures used in generic operations
//
// Platform Considerations:
// - This module is platform-independent as it provides generic utilities
// =================================================================================================

use std::collections::{HashMap, VecDeque};

use crate::prisma::prisma_engine::types::{ResourceType, ResourceUsage, SystemScore};
use super::types::{ResourceScore, ResourceTrend, DetailedSystemScore};

/// Normalize a value to a range between 0.0 and 100.0
pub fn normalize_score(value: f64, min: f64, max: f64) -> f64 {
    if max == min {
        return 50.0; // Default to middle value if range is zero
    }

    let normalized = ((value - min) / (max - min)) * 100.0;
    normalized.max(0.0).min(100.0)
}

/// Apply weights to a map of resource scores
pub fn apply_weights(
    scores: &HashMap<ResourceType, ResourceUsage>,
    weights: &HashMap<ResourceType, f64>
) -> HashMap<ResourceType, ResourceUsage> {
    let mut weighted_scores = HashMap::new();

    for (resource_type, score) in scores {
        let weight = weights.get(resource_type).copied().unwrap_or(1.0);
        weighted_scores.insert(*resource_type, ResourceUsage(score.0 * weight));
    }

    weighted_scores
}

/// Combine multiple resource scores into a single score
pub fn combine_scores(scores: &HashMap<ResourceType, ResourceUsage>) -> f64 {
    if scores.is_empty() {
        return 100.0; // Default to full availability if no scores
    }

    let total_score: f64 = scores.values().map(|s| s.0).sum();
    let count = scores.len() as f64;

    total_score / count
}

/// Calculate the difference between two scores
pub fn score_difference(
    old_score: &HashMap<ResourceType, ResourceUsage>,
    new_score: &HashMap<ResourceType, ResourceUsage>
) -> HashMap<ResourceType, f64> {
    let mut differences = HashMap::new();

    for (resource_type, new_value) in new_score {
        if let Some(old_value) = old_score.get(resource_type) {
            differences.insert(*resource_type, new_value.0 - old_value.0);
        } else {
            differences.insert(*resource_type, 0.0);
        }
    }

    differences
}

/// Determine the trend of a resource based on historical data
pub fn determine_trend(historical_values: &[f64]) -> ResourceTrend {
    if historical_values.len() < 3 {
        return ResourceTrend::Unknown;
    }

    // Simple linear regression to determine trend
    let n = historical_values.len() as f64;
    let indices: Vec<f64> = (0..historical_values.len()).map(|i| i as f64).collect();

    let sum_x: f64 = indices.iter().sum();
    let sum_y: f64 = historical_values.iter().sum();
    let sum_xy: f64 = indices.iter().zip(historical_values.iter())
        .map(|(x, y)| x * y)
        .sum();
    let sum_xx: f64 = indices.iter().map(|x| x * x).sum();

    let slope = (n * sum_xy - sum_x * sum_y) / (n * sum_xx - sum_x * sum_x);

    // Determine trend based on slope
    if slope.abs() < 0.1 {
        ResourceTrend::Stable
    } else if slope > 0.0 {
        ResourceTrend::Increasing
    } else {
        ResourceTrend::Decreasing
    }
}

/// Create a SystemScore from a map of resource availabilities
pub fn create_system_score(availabilities: HashMap<ResourceType, ResourceUsage>) -> SystemScore {
    SystemScore { availability: availabilities }
}

/// A cache for storing historical system scores
pub struct ScoreCache {
    /// Maximum number of scores to keep
    capacity: usize,
    /// Historical scores
    scores: VecDeque<DetailedSystemScore>,
}

impl ScoreCache {
    /// Create a new score cache with the given capacity
    pub fn new(capacity: usize) -> Self {
        ScoreCache {
            capacity,
            scores: VecDeque::with_capacity(capacity),
        }
    }

    /// Add a score to the cache
    pub fn add_score(&mut self, score: DetailedSystemScore) {
        if self.scores.len() >= self.capacity {
            self.scores.pop_front();
        }
        self.scores.push_back(score);
    }

    /// Get all scores in the cache
    pub fn get_scores(&self) -> Vec<DetailedSystemScore> {
        self.scores.iter().cloned().collect()
    }

    /// Get the most recent score
    pub fn get_latest_score(&self) -> Option<DetailedSystemScore> {
        self.scores.back().cloned()
    }

    /// Get historical scores for a specific resource
    pub fn get_resource_scores(&self, resource_type: ResourceType) -> Vec<ResourceScore> {
        self.scores
            .iter()
            .filter_map(|score| score.resource_scores.get(&resource_type).cloned())
            .collect()
    }

    /// Clear all scores from the cache
    pub fn clear(&mut self) {
        self.scores.clear();
    }

    /// Get the capacity of the cache
    pub fn capacity(&self) -> usize {
        self.capacity
    }

    /// Set the capacity of the cache
    pub fn set_capacity(&mut self, capacity: usize) {
        self.capacity = capacity;
        while self.scores.len() > capacity {
            self.scores.pop_front();
        }
    }
}

/// A moving average calculator for smoothing resource usage values
#[derive(Debug)]
pub struct MovingAverage {
    /// Window size for the moving average
    window_size: usize,
    /// Values in the window
    values: VecDeque<f64>,
    /// Current sum of values
    sum: f64,
}

impl MovingAverage {
    /// Create a new moving average calculator with the given window size
    pub fn new(window_size: usize) -> Self {
        MovingAverage {
            window_size,
            values: VecDeque::with_capacity(window_size),
            sum: 0.0,
        }
    }

    /// Add a value to the moving average
    pub fn add_value(&mut self, value: f64) -> f64 {
        // Add the new value
        self.values.push_back(value);
        self.sum += value;

        // Remove the oldest value if we've exceeded the window size
        if self.values.len() > self.window_size {
            if let Some(old_value) = self.values.pop_front() {
                self.sum -= old_value;
            }
        }

        // Return the current average
        self.average()
    }

    /// Get the current average
    pub fn average(&self) -> f64 {
        if self.values.is_empty() {
            0.0
        } else {
            self.sum / self.values.len() as f64
        }
    }

    /// Get the values in the window
    pub fn values(&self) -> Vec<f64> {
        self.values.iter().copied().collect()
    }

    /// Clear all values
    pub fn clear(&mut self) {
        self.values.clear();
        self.sum = 0.0;
    }

    /// Get the window size
    pub fn window_size(&self) -> usize {
        self.window_size
    }

    /// Set the window size
    pub fn set_window_size(&mut self, window_size: usize) {
        self.window_size = window_size;
        while self.values.len() > window_size {
            if let Some(old_value) = self.values.pop_front() {
                self.sum -= old_value;
            }
        }
    }
}