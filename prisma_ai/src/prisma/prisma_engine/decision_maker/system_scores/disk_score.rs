// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/system_scores/disk_score.rs
// =================================================================================================
// Purpose: Implements disk-specific scoring logic for evaluating system resource availability.
// This file contains functions and structures to analyze disk metrics and determine appropriate
// execution strategies based on current disk conditions, helping the decision maker optimize
// task execution for I/O-bound operations.
//
// Integration:
// - Internal Dependencies:
//   - system_scores/mod.rs: Exports this module
//   - system_scores/logic.rs: Uses disk scoring in overall evaluation
//   - system_scores/types.rs: Uses types defined for scoring
//   - system_scores/traits.rs: Implements traits defined for scoring
//   - system_scores/generics.rs: Uses generic utilities
//   - system_scores/system_scores.rs: Integrates disk scoring into overall system scoring
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses SystemScore, ResourceType, ResourceUsage types
//   - prisma_engine/monitor/system/disk.rs: Receives disk metrics from monitor module
//   - sysinfo: May use directly for disk information
//
// Platform Considerations:
// - This module interprets platform-specific disk metrics but presents them in a
//   platform-independent way through the SystemScore abstraction
// - Different storage technologies (SSD, HDD, NVMe) may have different performance characteristics
// =================================================================================================

use async_trait::async_trait;
use std::time::SystemTime;
use tracing::{debug, warn};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{ResourceType, ResourceUsage};
use crate::prisma::prisma_engine::monitor::system::types::{DiskMetrics, SystemMetrics};

use super::traits::ResourceScoreCalculator;
use super::types::{
    ResourceScore, ResourceTrend, AvailabilityLevel, ResourceThreshold,
    ResourceMetrics
};
use super::generics::{determine_trend, MovingAverage, normalize_score};

/// Calculator for disk resource scores
#[derive(Debug)]
pub struct DiskScoreCalculator {
    /// Threshold configuration for disk scoring
    threshold: ResourceThreshold,
    /// Weight of disk in the overall system score
    weight: f64,
    /// Moving average for smoothing disk I/O rates
    io_average: MovingAverage,
    /// Moving average for smoothing disk space usage
    space_average: MovingAverage,
    /// Maximum expected I/O rate in bytes per second
    max_io_rate: f64,
}

impl DiskScoreCalculator {
    /// Create a new disk score calculator with default settings
    pub fn new() -> Self {
        DiskScoreCalculator {
            threshold: ResourceThreshold {
                resource_type: ResourceType::DiskIO,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            },
            weight: 0.8,
            io_average: MovingAverage::new(5),
            space_average: MovingAverage::new(5),
            // Default max I/O rate: 500 MB/s (typical for SSDs)
            max_io_rate: 500.0 * 1024.0 * 1024.0,
        }
    }

    /// Create a new disk score calculator with custom settings
    pub fn with_config(threshold: ResourceThreshold, weight: f64, max_io_rate: f64) -> Self {
        DiskScoreCalculator {
            threshold,
            weight,
            io_average: MovingAverage::new(5),
            space_average: MovingAverage::new(5),
            max_io_rate,
        }
    }

    /// Calculate disk availability based on disk metrics
    fn calculate_availability(&self, disk_metrics: &DiskMetrics) -> ResourceUsage {
        // Calculate I/O availability based on current I/O rates
        let io_availability = self.calculate_io_availability(disk_metrics);

        // Calculate space availability based on free space
        let space_availability = self.calculate_space_availability(disk_metrics);

        // Combine I/O and space availability, giving more weight to I/O
        // I/O is typically more important for performance than free space
        let combined_availability = (io_availability * 0.7) + (space_availability * 0.3);

        ResourceUsage(combined_availability)
    }

    /// Calculate I/O availability based on current I/O rates
    fn calculate_io_availability(&self, disk_metrics: &DiskMetrics) -> f64 {
        // Get total I/O rate from throughput
        let total_io_rate = disk_metrics.total_throughput_bytes_per_sec.unwrap_or(0.0);

        // Get smoothed I/O rate from moving average or use current value
        let smoothed_io_rate = self.io_average.values().last()
            .copied()
            .unwrap_or(total_io_rate);

        // Normalize I/O rate to a percentage of max I/O rate
        // Higher I/O rate means lower availability
        let io_usage_percent = normalize_score(smoothed_io_rate, 0.0, self.max_io_rate);

        // Calculate I/O availability (100 - usage)
        let io_availability = 100.0 - io_usage_percent;

        // Check for high disk activity based on IOPS instead of io_time
        let has_high_activity = disk_metrics.total_iops.unwrap_or(0.0) > 1000.0; // 1000 IOPS is considered high

        if has_high_activity {
            // Apply I/O activity penalty
            return io_availability * 0.7;
        }

        io_availability
    }

    /// Calculate space availability based on free space
    fn calculate_space_availability(&self, disk_metrics: &DiskMetrics) -> f64 {
        // Calculate average space usage across all disks
        let mut total_space = 0u64;
        let mut available_space = 0u64;

        for disk in disk_metrics.disks.values() {
            total_space += disk.total_bytes;
            available_space += disk.available_bytes;
        }

        // Calculate space usage percentage
        let space_usage_percent = if total_space > 0 {
            100.0 - ((available_space as f64 / total_space as f64) * 100.0)
        } else {
            0.0
        };

        // Get smoothed space usage from moving average or use current value
        let smoothed_space_usage = self.space_average.values().last()
            .copied()
            .unwrap_or(space_usage_percent);

        // Calculate space availability (100 - usage)
        let space_availability = 100.0 - smoothed_space_usage;

        // If any disk is critically low on space (less than 10%), reduce availability
        let has_critical_space = disk_metrics.disks.values()
            .any(|disk| {
                let free_percent = (disk.available_bytes as f64 / disk.total_bytes as f64) * 100.0;
                free_percent < 10.0
            });

        if has_critical_space {
            // Apply critical space penalty
            return space_availability * 0.5;
        }

        space_availability
    }

    /// Get the availability level based on the availability percentage
    fn get_availability_level(&self, availability: f64) -> AvailabilityLevel {
        if availability >= self.threshold.high_threshold {
            AvailabilityLevel::High
        } else if availability >= self.threshold.medium_threshold {
            AvailabilityLevel::Medium
        } else if availability >= self.threshold.low_threshold {
            AvailabilityLevel::Low
        } else {
            AvailabilityLevel::Critical
        }
    }
}

#[async_trait]
impl ResourceScoreCalculator for DiskScoreCalculator {
    fn get_resource_type(&self) -> ResourceType {
        ResourceType::DiskIO
    }

    async fn calculate_score(&self, metrics: &SystemMetrics) -> PrismaResult<ResourceScore> {
        // Get disk metrics from system metrics
        let disk_metrics = if let Some(disk) = &metrics.disk {
            disk
        } else {
            warn!("No disk metrics available for scoring");
            return Ok(ResourceScore {
                resource_type: ResourceType::DiskIO,
                availability: ResourceUsage(100.0),
                level: AvailabilityLevel::High,
                timestamp: SystemTime::now(),
                raw_metrics: Some(ResourceMetrics::None),
            });
        };

        // Get total I/O rate from throughput
        let total_io_rate = disk_metrics.total_throughput_bytes_per_sec.unwrap_or(0.0);

        // Store current values for later use
        let mut smoothed_io_rate = total_io_rate;

        // Use the last values from the moving averages if available
        if let Some(last_io) = self.io_average.values().last() {
            smoothed_io_rate = *last_io;
        }

        // Calculate average space usage across all disks
        let mut total_space = 0u64;
        let mut available_space = 0u64;

        for disk in disk_metrics.disks.values() {
            total_space += disk.total_bytes;
            available_space += disk.available_bytes;
        }

        let space_usage_percent = if total_space > 0 {
            100.0 - ((available_space as f64 / total_space as f64) * 100.0)
        } else {
            0.0
        };

        // Store current space usage for later use
        let mut smoothed_space_usage = space_usage_percent;

        // Use the last values from the moving averages if available
        if let Some(last_space) = self.space_average.values().last() {
            smoothed_space_usage = *last_space;
        }

        // Calculate availability
        let availability = self.calculate_availability(disk_metrics);

        // Get availability level
        let level = self.get_availability_level(availability.0);

        debug!(
            "Disk score: availability={:.1}%, level={:?}, IO={:.2}MB/s, space_usage={:.1}%",
            availability.0,
            level,
            total_io_rate / 1024.0 / 1024.0,
            space_usage_percent
        );

        Ok(ResourceScore {
            resource_type: ResourceType::DiskIO,
            availability,
            level,
            timestamp: SystemTime::now(),
            raw_metrics: Some(ResourceMetrics::Disk(disk_metrics.clone())),
        })
    }

    async fn calculate_trend(
        &self,
        current_score: &ResourceScore,
        historical_scores: &[ResourceScore]
    ) -> PrismaResult<ResourceTrend> {
        // Extract availability values from historical scores
        let mut availability_values: Vec<f64> = historical_scores
            .iter()
            .map(|score| score.availability.0)
            .collect();

        // Add current score to the list
        availability_values.push(current_score.availability.0);

        // Determine trend
        let trend = determine_trend(&availability_values);

        debug!(
            "Disk trend: {:?}, based on {} data points",
            trend,
            availability_values.len()
        );

        Ok(trend)
    }

    fn get_threshold(&self) -> ResourceThreshold {
        self.threshold.clone()
    }

    fn set_threshold(&mut self, threshold: ResourceThreshold) {
        self.threshold = threshold;
    }

    fn get_weight(&self) -> f64 {
        self.weight
    }

    fn set_weight(&mut self, weight: f64) {
        self.weight = weight;
    }
}