// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/system_scores/memory_score.rs
// =================================================================================================
// Purpose: Implements memory-specific scoring logic for evaluating system resource availability.
// This file contains functions and structures to analyze memory metrics and determine appropriate
// execution strategies based on current memory conditions, helping the decision maker optimize
// task execution for memory-intensive operations.
//
// Integration:
// - Internal Dependencies:
//   - system_scores/mod.rs: Exports this module
//   - system_scores/logic.rs: Uses memory scoring in overall evaluation
//   - system_scores/types.rs: Uses types defined for scoring
//   - system_scores/traits.rs: Implements traits defined for scoring
//   - system_scores/generics.rs: Uses generic utilities
//   - system_scores/system_scores.rs: Integrates memory scoring into overall system scoring
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses SystemScore, ResourceType, ResourceUsage types
//   - prisma_engine/monitor/system/memory.rs: Receives memory metrics from monitor module
//   - sysinfo: May use directly for memory information
//
// Platform Considerations:
// - This module interprets platform-specific memory metrics but presents them in a
//   platform-independent way through the SystemScore abstraction
// =================================================================================================

use async_trait::async_trait;
use std::time::SystemTime;
use tracing::{debug, warn};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{ResourceType, ResourceUsage};
use crate::prisma::prisma_engine::monitor::system::types::{MemoryMetrics, SystemMetrics};

use super::traits::ResourceScoreCalculator;
use super::types::{
    ResourceScore, ResourceTrend, AvailabilityLevel, ResourceThreshold,
    ResourceMetrics
};
use super::generics::{determine_trend, MovingAverage};

/// Calculator for memory resource scores
#[derive(Debug)]
pub struct MemoryScoreCalculator {
    /// Threshold configuration for memory scoring
    threshold: ResourceThreshold,
    /// Weight of memory in the overall system score
    weight: f64,
    /// Moving average for smoothing memory usage
    usage_average: MovingAverage,
    /// Moving average for smoothing swap usage
    swap_average: MovingAverage,
}

impl MemoryScoreCalculator {
    /// Create a new memory score calculator with default settings
    pub fn new() -> Self {
        MemoryScoreCalculator {
            threshold: ResourceThreshold {
                resource_type: ResourceType::Memory,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            },
            weight: 1.0,
            usage_average: MovingAverage::new(5),
            swap_average: MovingAverage::new(5),
        }
    }

    /// Create a new memory score calculator with custom settings
    pub fn with_config(threshold: ResourceThreshold, weight: f64) -> Self {
        MemoryScoreCalculator {
            threshold,
            weight,
            usage_average: MovingAverage::new(5),
            swap_average: MovingAverage::new(5),
        }
    }

    /// Calculate memory availability based on memory metrics
    pub fn calculate_availability(&self, memory_metrics: &MemoryMetrics) -> ResourceUsage {
        // Get memory usage percentage
        let usage_percent = memory_metrics.usage_percent;

        // Smooth the usage with moving average
        let smoothed_usage = self.usage_average.values().last()
            .copied()
            .unwrap_or(usage_percent);

        // Calculate base availability (100 - usage)
        let mut availability = 100.0 - smoothed_usage;

        // Adjust availability based on swap usage
        let swap_usage_percent = memory_metrics.swap_usage_percent;

        // Smooth the swap usage with moving average
        let smoothed_swap = self.swap_average.values().last()
            .copied()
            .unwrap_or(swap_usage_percent);

        // If swap usage is high, reduce availability
        if smoothed_swap > 50.0 {
            // Calculate swap penalty factor (0.5 to 1.0)
            // At 50% swap usage, factor is 1.0 (no penalty)
            // At 100% swap usage, factor is 0.5 (50% penalty)
            let swap_factor = 1.0 - ((smoothed_swap - 50.0) / 100.0);

            // Apply swap penalty
            availability *= swap_factor;
        }

        // If available memory is critically low (less than 10% of total), apply additional penalty
        if memory_metrics.available_bytes < (memory_metrics.total_bytes / 10) {
            // Apply critical memory penalty
            availability *= 0.5;
        }

        ResourceUsage(availability)
    }

    /// Get the availability level based on the availability percentage
    fn get_availability_level(&self, availability: f64) -> AvailabilityLevel {
        if availability >= self.threshold.high_threshold {
            AvailabilityLevel::High
        } else if availability >= self.threshold.medium_threshold {
            AvailabilityLevel::Medium
        } else if availability >= self.threshold.low_threshold {
            AvailabilityLevel::Low
        } else {
            AvailabilityLevel::Critical
        }
    }
}

#[async_trait]
impl ResourceScoreCalculator for MemoryScoreCalculator {
    fn get_resource_type(&self) -> ResourceType {
        ResourceType::Memory
    }

    async fn calculate_score(&self, metrics: &SystemMetrics) -> PrismaResult<ResourceScore> {
        // Get memory metrics from system metrics
        let memory_metrics = if let Some(memory) = &metrics.memory {
            memory
        } else {
            warn!("No memory metrics available for scoring");
            return Ok(ResourceScore {
                resource_type: ResourceType::Memory,
                availability: ResourceUsage(100.0),
                level: AvailabilityLevel::High,
                timestamp: SystemTime::now(),
                raw_metrics: Some(ResourceMetrics::None),
            });
        };

        // Create local copies of the values we need
        let usage_percent = memory_metrics.usage_percent;
        let swap_usage_percent = memory_metrics.swap_usage_percent;
        let mut smoothed_usage = usage_percent;
        let mut smoothed_swap = swap_usage_percent;

        // Use the last values from the moving averages if available
        if let Some(last_usage) = self.usage_average.values().last() {
            smoothed_usage = *last_usage;
        }

        if let Some(last_swap) = self.swap_average.values().last() {
            smoothed_swap = *last_swap;
        }

        // Calculate availability
        let availability = self.calculate_availability(memory_metrics);

        // Get availability level
        let level = self.get_availability_level(availability.0);

        debug!(
            "Memory score: availability={:.1}%, level={:?}, usage={:.1}%, swap={:.1}%",
            availability.0,
            level,
            memory_metrics.usage_percent,
            memory_metrics.swap_usage_percent
        );

        Ok(ResourceScore {
            resource_type: ResourceType::Memory,
            availability,
            level,
            timestamp: SystemTime::now(),
            raw_metrics: Some(ResourceMetrics::Memory(memory_metrics.clone())),
        })
    }

    async fn calculate_trend(
        &self,
        current_score: &ResourceScore,
        historical_scores: &[ResourceScore]
    ) -> PrismaResult<ResourceTrend> {
        // Extract availability values from historical scores
        let mut availability_values: Vec<f64> = historical_scores
            .iter()
            .map(|score| score.availability.0)
            .collect();

        // Add current score to the list
        availability_values.push(current_score.availability.0);

        // Determine trend
        let trend = determine_trend(&availability_values);

        debug!(
            "Memory trend: {:?}, based on {} data points",
            trend,
            availability_values.len()
        );

        Ok(trend)
    }

    fn get_threshold(&self) -> ResourceThreshold {
        self.threshold.clone()
    }

    fn set_threshold(&mut self, threshold: ResourceThreshold) {
        self.threshold = threshold;
    }

    fn get_weight(&self) -> f64 {
        self.weight
    }

    fn set_weight(&mut self, weight: f64) {
        self.weight = weight;
    }
}