// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/system_scores/network_score.rs
// =================================================================================================
// Purpose: Implements network-specific scoring logic for evaluating system resource availability.
// This file contains functions and structures to analyze network metrics and determine appropriate
// execution strategies based on current network conditions, helping the decision maker optimize
// task execution for network-bound operations.
//
// Integration:
// - Internal Dependencies:
//   - system_scores/mod.rs: Exports this module
//   - system_scores/logic.rs: Uses network scoring in overall evaluation
//   - system_scores/types.rs: Uses types defined for scoring
//   - system_scores/traits.rs: Implements traits defined for scoring
//   - system_scores/generics.rs: Uses generic utilities
//   - system_scores/system_scores.rs: Integrates network scoring into overall system scoring
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses SystemScore, ResourceType, ResourceUsage types
//   - prisma_engine/monitor/system/network.rs: Receives network metrics from monitor module
//   - sysinfo: May use directly for network information
//
// Platform Considerations:
// - This module interprets platform-specific network metrics but presents them in a
//   platform-independent way through the SystemScore abstraction
// - Different network interfaces and technologies may have different performance characteristics
// =================================================================================================

use async_trait::async_trait;
use std::time::SystemTime;
use tracing::{debug, warn};

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{ResourceType, ResourceUsage};
use crate::prisma::prisma_engine::monitor::system::types::{NetworkMetrics, SystemMetrics};

use super::traits::ResourceScoreCalculator;
use super::types::{
    ResourceScore, ResourceTrend, AvailabilityLevel, ResourceThreshold,
    ResourceMetrics
};
use super::generics::{determine_trend, MovingAverage, normalize_score};

/// Calculator for network resource scores
#[derive(Debug)]
pub struct NetworkScoreCalculator {
    /// Threshold configuration for network scoring
    threshold: ResourceThreshold,
    /// Weight of network in the overall system score
    weight: f64,
    /// Moving average for smoothing network bandwidth usage
    bandwidth_average: MovingAverage,
    /// Maximum expected bandwidth in bytes per second
    max_bandwidth: f64,
}

impl NetworkScoreCalculator {
    /// Create a new network score calculator with default settings
    pub fn new() -> Self {
        NetworkScoreCalculator {
            threshold: ResourceThreshold {
                resource_type: ResourceType::NetworkBandwidth,
                high_threshold: 75.0,
                medium_threshold: 50.0,
                low_threshold: 25.0,
                enabled: true,
            },
            weight: 0.7,
            bandwidth_average: MovingAverage::new(5),
            // Default max bandwidth: 100 MB/s (typical for gigabit Ethernet)
            max_bandwidth: 100.0 * 1024.0 * 1024.0,
        }
    }

    /// Create a new network score calculator with custom settings
    pub fn with_config(threshold: ResourceThreshold, weight: f64, max_bandwidth: f64) -> Self {
        NetworkScoreCalculator {
            threshold,
            weight,
            bandwidth_average: MovingAverage::new(5),
            max_bandwidth,
        }
    }

    /// Calculate network availability based on network metrics
    fn calculate_availability(&self, network_metrics: &NetworkMetrics) -> ResourceUsage {
        // Get total bandwidth usage (rx + tx)
        let total_bandwidth = network_metrics.total_rx_bytes_per_sec + network_metrics.total_tx_bytes_per_sec;

        // Smooth the bandwidth with moving average
        let smoothed_bandwidth = self.bandwidth_average.values().last()
            .copied()
            .unwrap_or(total_bandwidth);

        // Normalize bandwidth to a percentage of max bandwidth
        // Higher bandwidth usage means lower availability
        let bandwidth_usage_percent = normalize_score(smoothed_bandwidth, 0.0, self.max_bandwidth);

        // Calculate bandwidth availability (100 - usage)
        let bandwidth_availability = 100.0 - bandwidth_usage_percent;

        // Check for network errors
        let has_errors = network_metrics.interfaces.values()
            .any(|interface| interface.rx_errors > 0 || interface.tx_errors > 0);

        if has_errors {
            // Apply error penalty
            return ResourceUsage(bandwidth_availability * 0.8);
        }

        ResourceUsage(bandwidth_availability)
    }

    /// Get the availability level based on the availability percentage
    fn get_availability_level(&self, availability: f64) -> AvailabilityLevel {
        if availability >= self.threshold.high_threshold {
            AvailabilityLevel::High
        } else if availability >= self.threshold.medium_threshold {
            AvailabilityLevel::Medium
        } else if availability >= self.threshold.low_threshold {
            AvailabilityLevel::Low
        } else {
            AvailabilityLevel::Critical
        }
    }
}

#[async_trait]
impl ResourceScoreCalculator for NetworkScoreCalculator {
    fn get_resource_type(&self) -> ResourceType {
        ResourceType::NetworkBandwidth
    }

    async fn calculate_score(&self, metrics: &SystemMetrics) -> PrismaResult<ResourceScore> {
        // Get network metrics from system metrics
        let network_metrics = if let Some(network) = &metrics.network {
            network
        } else {
            warn!("No network metrics available for scoring");
            return Ok(ResourceScore {
                resource_type: ResourceType::NetworkBandwidth,
                availability: ResourceUsage(100.0),
                level: AvailabilityLevel::High,
                timestamp: SystemTime::now(),
                raw_metrics: Some(ResourceMetrics::None),
            });
        };

        // Get total bandwidth
        let total_bandwidth = network_metrics.total_rx_bytes_per_sec + network_metrics.total_tx_bytes_per_sec;

        // Store current values for later use
        let mut smoothed_bandwidth = total_bandwidth;

        // Use the last values from the moving averages if available
        if let Some(last_bandwidth) = self.bandwidth_average.values().last() {
            smoothed_bandwidth = *last_bandwidth;
        }

        // Calculate availability
        let availability = self.calculate_availability(network_metrics);

        // Get availability level
        let level = self.get_availability_level(availability.0);

        debug!(
            "Network score: availability={:.1}%, level={:?}, bandwidth={:.2}MB/s, interfaces={}",
            availability.0,
            level,
            total_bandwidth / 1024.0 / 1024.0,
            network_metrics.interfaces.len()
        );

        Ok(ResourceScore {
            resource_type: ResourceType::NetworkBandwidth,
            availability,
            level,
            timestamp: SystemTime::now(),
            raw_metrics: Some(ResourceMetrics::Network(network_metrics.clone())),
        })
    }

    async fn calculate_trend(
        &self,
        current_score: &ResourceScore,
        historical_scores: &[ResourceScore]
    ) -> PrismaResult<ResourceTrend> {
        // Extract availability values from historical scores
        let mut availability_values: Vec<f64> = historical_scores
            .iter()
            .map(|score| score.availability.0)
            .collect();

        // Add current score to the list
        availability_values.push(current_score.availability.0);

        // Determine trend
        let trend = determine_trend(&availability_values);

        debug!(
            "Network trend: {:?}, based on {} data points",
            trend,
            availability_values.len()
        );

        Ok(trend)
    }

    fn get_threshold(&self) -> ResourceThreshold {
        self.threshold.clone()
    }

    fn set_threshold(&mut self, threshold: ResourceThreshold) {
        self.threshold = threshold;
    }

    fn get_weight(&self) -> f64 {
        self.weight
    }

    fn set_weight(&mut self, weight: f64) {
        self.weight = weight;
    }
}