// /Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/monitor_tests_v4.rs
// Integration tests for the Task Monitor focusing on v4 requirements.

#[cfg(test)]
mod monitor_tests_v4 {
    use prisma_ai::prisma::prisma_engine::monitor::Monitor;
    use prisma_ai::prisma::prisma_engine::monitor::types::MonitorConfig;
    use prisma_ai::prisma::prisma_engine::monitor::prisma::types::PrismaMonitorConfig;
    use prisma_ai::prisma::prisma_engine::monitor::prisma::types::{TaskMetrics, TaskStatus};
    use prisma_ai::prisma::prisma_engine::types::{TaskId, TaskCategory, TaskPriority};
    use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor;
    use std::sync::Arc;
    use std::time::{Duration, Instant};
    use tokio::time::sleep;

    // Helper function to create a monitor instance with default configuration
    fn create_monitor() -> Monitor {
        let monitor_config = MonitorConfig {
            poll_interval_ms: 100, // Use a short poll interval for tests
        };
        Monitor::new(monitor_config)
    }

    // Test integration with queue monitoring
    // This test verifies that task events (creation, start, completion) are correctly
    // reflected in both task monitor metrics and queue monitor metrics.
    #[tokio::test]
    async fn test_task_queue_integration() {
        let mut monitor = create_monitor();
        
        // Start the monitor components
        // Import traits locally for starting monitors
        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _; // Alias to avoid conflict
            use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{QueueMonitoring, TaskMonitoring};
            
            monitor.start().await.expect("Failed to start aggregate monitor");
        }

        let task_id = TaskId::new();
        let queue_name = "test_queue_integration";

        let task_metrics_created = TaskMetrics {
            task_id: task_id.clone(),
            category: TaskCategory::Internal,
            priority: TaskPriority::Normal,
            status: TaskStatus::Queued,
            created_at: Instant::now(),
            started_at: None,
            completed_at: None,
            queue_time: None,
            processing_time: None,
            queue_name: Some(queue_name.to_string()),
            error_message: None,
        };

        // Record task creation
        monitor.record_task_created(task_metrics_created.clone()).await.expect("Failed to record task created");
        monitor.update_queue_metrics(queue_name, 1).await.expect("Failed to update queue metrics");

        // Check initial queue metrics
        let queue_metrics_after_creation = monitor.get_queue_metrics().await.expect("Failed to get queue metrics");
        let q_metrics = queue_metrics_after_creation.queue_metrics.get(queue_name).expect("Queue not found after creation");
        assert_eq!(q_metrics.length, 1, "Queue length should be 1 after task creation");

        // Record task start
        monitor.record_task_started(&task_id, queue_name).await.expect("Failed to record task started");
        // Simulate task processing by having it leave the queue
        monitor.update_queue_metrics(queue_name, 0).await.expect("Failed to update queue metrics for task start");
        
        sleep(Duration::from_millis(50)).await; // Allow monitor to process

        // Check task status after start
        let task_metrics_after_start = monitor.get_task_metrics_by_id(&task_id).await.expect("Failed to get task metrics").expect("Task not found after start");
        assert_eq!(task_metrics_after_start.status, TaskStatus::Processing, "Task status should be Processing");
        assert!(task_metrics_after_start.started_at.is_some(), "Task started_at should be set");
        assert!(task_metrics_after_start.queue_time.is_some(), "Task queue_time should be set");

        // Record task completion
        monitor.record_task_completed(&task_id, true, None).await.expect("Failed to record task completed");
        
        // Get updated task metrics after completion to get actual processing time
        let task_metrics_after_completion_temp = monitor.get_task_metrics_by_id(&task_id).await.expect("Failed to get task metrics").expect("Task not found after completion");
        
        // Simulate queue processing the task (for avg processing time etc.)
        {
            use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;
            monitor.record_task_processed(queue_name, task_metrics_after_completion_temp.processing_time.unwrap_or_default().as_millis() as f64, true).await.expect("Failed to record task processed in queue");
        }

        sleep(Duration::from_millis(50)).await; // Allow monitor to process

        // Check task status after completion
        let task_metrics_after_completion = monitor.get_task_metrics_by_id(&task_id).await.expect("Failed to get task metrics").expect("Task not found after completion");
        assert_eq!(task_metrics_after_completion.status, TaskStatus::Completed, "Task status should be Completed");
        assert!(task_metrics_after_completion.completed_at.is_some(), "Task completed_at should be set");
        assert!(task_metrics_after_completion.processing_time.is_some(), "Task processing_time should be set");

        // Check queue metrics after task completion
        let final_queue_metrics = monitor.get_queue_metrics().await.expect("Failed to get final queue metrics");
        let fq_metrics = final_queue_metrics.queue_metrics.get(queue_name).expect("Queue not found at end");
        assert_eq!(fq_metrics.length, 0, "Queue length should be 0 after task completion");
        assert_eq!(fq_metrics.tasks_processed, 1, "Queue should have processed 1 task");
        assert!(fq_metrics.avg_processing_time_ms > 0.0, "Average processing time should be updated");

        // Stop the monitor
        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _; // Alias to avoid conflict
            monitor.stop().await.expect("Failed to stop aggregate monitor");
        }
    }

    // Test task metrics correlation with system resources
    // This test is conceptual as direct resource manipulation is complex.
    // It checks if the aggregated system score reflects changes when tasks are processed,
    // implying the monitor's internal logic for score adjustment is working.
    #[tokio::test]
    async fn test_task_metrics_system_resource_correlation() {
        let mut monitor = create_monitor();
        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _; 
            monitor.start().await.expect("Failed to start monitor");
        }

        // Get initial system score
        let initial_system_score = monitor.get_system_score().await.expect("Failed to get initial system score");

        // Simulate a few tasks being processed
        let num_tasks = 5;
        let queue_name = "resource_correlation_queue";
        for i in 0..num_tasks {
            let task_id = TaskId::new();
            let task_metrics = TaskMetrics {
                task_id: task_id.clone(),
                category: TaskCategory::Internal,
                priority: TaskPriority::Normal,
                status: TaskStatus::Queued,
                created_at: Instant::now(),
                started_at: None,
                completed_at: None,
                queue_time: None,
                processing_time: None,
                queue_name: Some(queue_name.to_string()),
                error_message: None,
            };
            monitor.record_task_created(task_metrics).await.unwrap();
            monitor.update_queue_metrics(queue_name, num_tasks - i).await.unwrap();
            monitor.record_task_started(&task_id, queue_name).await.unwrap();
            monitor.update_queue_metrics(queue_name, num_tasks - i -1).await.unwrap(); // Task leaves queue
            sleep(Duration::from_millis(1)).await; // Simulate processing
            monitor.record_task_completed(&task_id, true, None).await.unwrap();
            {
                use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;
                monitor.record_task_processed(queue_name, 10.0, true).await.unwrap();
            }
        }
        
        sleep(Duration::from_millis(50)).await; // Allow monitor to update scores based on activity

        // Get system score after tasks
        let final_system_score = monitor.get_system_score().await.expect("Failed to get final system score");

        // We expect some change in the system score, particularly CPU or Memory availability,
        // due to the Monitor's internal logic that adjusts scores based on task/queue activity.
        // The exact change depends on the Monitor's aggregation logic.
        // This is a soft check; a more robust test would require deeper inspection or controlled resource usage.
        let initial_cpu_avail = initial_system_score.availability.get(&prisma_ai::prisma::prisma_engine::types::ResourceType::CPU).map_or(100.0, |r| r.0);
        let final_cpu_avail = final_system_score.availability.get(&prisma_ai::prisma::prisma_engine::types::ResourceType::CPU).map_or(100.0, |r| r.0);
        
        // Depending on the Monitor's aggregation logic, CPU availability might decrease due to task load.
        // If queue length was high, it would reduce. If many tasks active, memory might reduce.
        // For this test, we'll assert that *something* potentially changed or that the values are plausible.
        // This test is more about exercising the pathways than asserting specific numeric outcomes without knowing the exact aggregation formula.
        assert!(final_cpu_avail <= initial_cpu_avail, 
            "CPU availability (final: {}, initial: {}) should not increase after load, or at least stay same if load was light.", 
            final_cpu_avail, initial_cpu_avail);
        
        println!("Initial CPU Availability: {:.2}%", initial_cpu_avail);
        println!("Final CPU Availability after tasks: {:.2}%", final_cpu_avail);

        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _; 
            monitor.stop().await.expect("Failed to stop monitor");
        }
    }

    // Test task monitor performance under high task loads
    // This test submits a large number of task events rapidly and checks if the monitor
    // processes them without significant delay and maintains correct counts.
    #[tokio::test]
    async fn test_task_monitor_high_load_performance() {
        let mut monitor = create_monitor();
        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _; 
            monitor.start().await.expect("Failed to start monitor");
        }

        let num_tasks = 100;
        let queue_name = "high_load_queue";
        let start_time = Instant::now();

        for i in 0..num_tasks {
            let task_id = TaskId::new();
            let task_metrics = TaskMetrics {
                task_id: task_id.clone(),
                category: TaskCategory::FileProcessing,
                priority: TaskPriority::Low,
                status: TaskStatus::Queued,
                created_at: Instant::now(),
                started_at: None,
                completed_at: None,
                queue_time: None,
                processing_time: None,
                queue_name: Some(format!("{}_{}", queue_name, i % 5)), // Distribute over a few queues
                error_message: None,
            };
            monitor.record_task_created(task_metrics).await.unwrap();
            monitor.record_task_started(&task_id, queue_name).await.unwrap();
            monitor.record_task_completed(&task_id, true, None).await.unwrap();
        }

        let processing_duration = start_time.elapsed();
        println!("Processed {} task events (create, start, complete) in {:?}", num_tasks * 3, processing_duration);

        // Allow some time for internal monitor processing/aggregation if any async tasks are spawned internally by monitor
        sleep(Duration::from_millis(200)).await; 

        let task_monitor_metrics = monitor.get_task_metrics().await.expect("Failed to get task monitor metrics");
        
        // Check if all tasks were recorded as completed (active_tasks should be empty or near empty if some are still being processed by monitor's internal loop)
        // And completed_tasks should have num_tasks
        // Due to the nature of async processing and the short poll interval, some tasks might still be in active_tasks
        // if the monitor's internal loop hasn't caught up perfectly. A more robust check looks at totals.
        assert_eq!(task_monitor_metrics.completed_tasks.len(), num_tasks, "Number of completed tasks should match total tasks submitted.");
        assert_eq!(task_monitor_metrics.total_tasks, num_tasks, "Total tasks count should be correct.");
        assert_eq!(task_monitor_metrics.successful_tasks, num_tasks, "Succeeded tasks count should be correct.");

        // A loose check on performance: processing 300 events (100 tasks * 3 states)
        // should be reasonably fast. For example, less than 2 seconds.
        // This depends heavily on the machine and test environment.
        assert!(processing_duration < Duration::from_millis(2000), "Task event processing took too long: {:?}", processing_duration);

        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _; 
            monitor.stop().await.expect("Failed to stop monitor");
        }
    }

    // Test task monitor scalability with many concurrent tasks
    // This test spawns multiple tokio tasks that concurrently interact with the monitor.
    #[tokio::test]
    async fn test_task_monitor_concurrent_scalability() {
        let monitor = Arc::new(tokio::sync::Mutex::new(create_monitor()));
        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _; 
            let mut m = monitor.lock().await;
            m.start().await.expect("Failed to start monitor");
        }

        let num_concurrent_tasks = 10;
        let tasks_per_worker = 5;
        let queue_prefix = "concurrent_queue";
        let mut handles = vec![];

        for i in 0..num_concurrent_tasks {
            let monitor_clone = Arc::clone(&monitor);
            let handle = tokio::spawn(async move {
                let queue_name = format!("{}_{}", queue_prefix, i);
                for j in 0..tasks_per_worker {
                    let task_id = TaskId::new();
                    let task_metrics = TaskMetrics {
                        task_id: task_id.clone(),
                        category: TaskCategory::LLMInference,
                        priority: TaskPriority::High,
                        status: TaskStatus::Queued,
                        created_at: Instant::now(),
                        started_at: None,
                        completed_at: None,
                        queue_time: None,
                        processing_time: None,
                        queue_name: Some(queue_name.clone()),
                        error_message: None,
                    };
                    // Lock the monitor for each operation sequence
                    let mut m_guard = monitor_clone.lock().await;
                    m_guard.record_task_created(task_metrics).await.unwrap();
                    m_guard.update_queue_metrics(&queue_name, j + 1).await.unwrap();
                    m_guard.record_task_started(&task_id, &queue_name).await.unwrap();
                    m_guard.update_queue_metrics(&queue_name, j).await.unwrap(); // Task leaves queue
                    drop(m_guard); // Release lock before sleep
                    
                    sleep(Duration::from_millis(1)).await; // Simulate work
                    
                    let mut m_guard = monitor_clone.lock().await;
                    m_guard.record_task_completed(&task_id, true, None).await.unwrap();
                    {
                        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;
                        m_guard.record_task_processed(&queue_name, 10.0, true).await.unwrap();
                    }
                    drop(m_guard);
                }
            });
            handles.push(handle);
        }

        for handle in handles {
            handle.await.expect("Tokio task failed");
        }

        // Allow monitor to process all updates
        sleep(Duration::from_millis(100)).await;

        let mut m_guard = monitor.lock().await;
        let task_monitor_metrics = m_guard.get_task_metrics().await.expect("Failed to get task monitor metrics");
        let queue_monitor_metrics = m_guard.get_queue_metrics().await.expect("Failed to get queue metrics");
        
        let expected_total_tasks = (num_concurrent_tasks * tasks_per_worker) as u64;
        assert_eq!(task_monitor_metrics.completed_tasks.len() as u64, expected_total_tasks, "Mismatch in completed tasks count");
        assert_eq!(task_monitor_metrics.total_tasks, expected_total_tasks as usize, "Mismatch in total tasks count");
        assert_eq!(task_monitor_metrics.successful_tasks, expected_total_tasks as usize, "Mismatch in succeeded tasks count");

        let mut total_processed_in_queues = 0;
        for i in 0..num_concurrent_tasks {
            let queue_name = format!("{}_{}", queue_prefix, i);
            if let Some(qm) = queue_monitor_metrics.queue_metrics.get(&queue_name) {
                assert_eq!(qm.tasks_processed, tasks_per_worker, "Mismatch in tasks processed for queue {}", queue_name);
                total_processed_in_queues += qm.tasks_processed;
            }
        }
        assert_eq!(total_processed_in_queues as u64, expected_total_tasks, "Total tasks processed across all queues mismatch");

        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _; 
            // m_guard is still held here, which is fine for stop
            m_guard.stop().await.expect("Failed to stop monitor");
        }
    }

    // ============================================================================
    // NEW COMPREHENSIVE CROSS-MONITOR INTEGRATION TESTS
    // ============================================================================

    #[tokio::test]
    async fn test_cross_monitor_metric_correlation() {
        // Test cross-monitor integration: verify that task processing affects system metrics
        // and that all monitors report correlated data during intensive operations
        
        let mut monitor = create_monitor();
    
    // Start monitoring all subsystems
    {
        use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
        monitor.start().await.expect("Failed to start monitor");
    }
    
    // Allow initial metrics collection
    tokio::time::sleep(std::time::Duration::from_millis(100)).await;
    
    // Capture baseline metrics from all monitors
    let initial_system_score = monitor.get_system_score().await.expect("Failed to get initial system score");
    
    // Create multiple queues and submit intensive tasks
    let queue_ids: Vec<String> = (0..3).map(|i| format!("test_queue_{}", i)).collect();
    
    // Submit tasks that will stress the system
    let mut task_ids = Vec::new();
    for (_i, queue_id) in queue_ids.iter().enumerate() {
        for j in 0..5 {
            let task_id = TaskId::new();
            task_ids.push(task_id.clone());
            
            // Create task metrics and record task creation
            let task_metrics = TaskMetrics {
                task_id: task_id.clone(),
                category: TaskCategory::Internal,
                priority: TaskPriority::Normal,
                status: TaskStatus::Queued,
                created_at: Instant::now(),
                started_at: None,
                completed_at: None,
                queue_time: None,
                processing_time: None,
                queue_name: Some(queue_id.clone()),
                error_message: None,
            };
            monitor.record_task_created(task_metrics).await.expect("Failed to record task creation");
            monitor.update_queue_metrics(queue_id, j + 1).await.expect("Failed to update queue metrics");
        }
    }
    
    // Process tasks and monitor metric correlation
    for (idx, task_id) in task_ids.iter().enumerate() {
        let queue_id = &queue_ids[idx / 5]; // 5 tasks per queue
        monitor.record_task_started(task_id, queue_id).await.expect("Failed to record task start");
        
        // Simulate intensive processing
        tokio::time::sleep(std::time::Duration::from_millis(50)).await;
        
        monitor.record_task_completed(task_id, true, None).await.expect("Failed to record task completion");
        {
            use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;
            monitor.record_task_processed(queue_id, 50.0, true).await.expect("Failed to record task processing");
        }
    }
    
    // Allow metrics to propagate
    tokio::time::sleep(std::time::Duration::from_millis(200)).await;
    
    // Verify cross-monitor correlation
    let final_system_score = monitor.get_system_score().await.expect("Failed to get final system score");
    
    // Assert that task processing affected system metrics
    assert_ne!(initial_system_score, final_system_score, 
        "System score should change after intensive task processing");
    
    // Verify that task and queue metrics are available
    let task_metrics = monitor.get_task_metrics().await.expect("Failed to get task metrics");
    let queue_metrics = monitor.get_queue_metrics().await.expect("Failed to get queue metrics");
    
    // Assert that metrics reflect the processing activity
    assert!(task_metrics.total_tasks >= task_ids.len(),
        "Task metrics should reflect processed tasks");
    assert!(queue_metrics.queue_metrics.len() >= queue_ids.len(),
        "Queue metrics should reflect created queues");
    
    // Cleanup
    {
        use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
        monitor.stop().await.expect("Failed to stop monitor");
    }
    }

    #[tokio::test]
    async fn test_monitor_synchronization_and_timing() {
    // Test monitor synchronization: verify that metrics from different monitors
    // are synchronized and timing-consistent across the system
    
    let mut monitor = create_monitor();
    
    {
        use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
        monitor.start().await.expect("Failed to start monitor");
    }
    
    // Create synchronized test scenario
    let queue_id = "sync_test_queue".to_string();
    
    let start_time = std::time::Instant::now();
    
    // Submit and process tasks with precise timing
    let task_ids: Vec<TaskId> = (0..10).map(|_| TaskId::new()).collect();
    
    for (idx, task_id) in task_ids.iter().enumerate() {
        let task_start = std::time::Instant::now();
        
        // Create task metrics and record task creation
        let task_metrics = TaskMetrics {
            task_id: task_id.clone(),
            category: TaskCategory::Internal,
            priority: TaskPriority::Normal,
            status: TaskStatus::Queued,
            created_at: Instant::now(),
            started_at: None,
            completed_at: None,
            queue_time: None,
            processing_time: None,
            queue_name: Some(queue_id.clone()),
            error_message: None,
        };
        monitor.record_task_created(task_metrics).await.expect("Failed to record task creation");
        monitor.update_queue_metrics(&queue_id, idx + 1).await.expect("Failed to update queue metrics");
        
        monitor.record_task_started(task_id, &queue_id).await
            .expect("Failed to record task start");
        
        // Controlled processing time
        tokio::time::sleep(Duration::from_millis(25)).await;
        
        monitor.record_task_completed(task_id, true, None).await
            .expect("Failed to record task completion");
        {
            use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;
            monitor.record_task_processed(&queue_id, 25.0, true).await.expect("Failed to record task processing");
        }
        
        let task_duration = task_start.elapsed();
        
        // Verify timing consistency (should be roughly 25ms + overhead, but allow for system variance)
        assert!(task_duration >= Duration::from_millis(20) && 
                task_duration <= Duration::from_millis(500),
                "Task timing should be consistent: {:?}", task_duration);
    }
    
    let total_duration = start_time.elapsed();
    
    // Allow final metric synchronization
    tokio::time::sleep(Duration::from_millis(100)).await;
    
    // Verify synchronized metrics
    let task_metrics = monitor.get_task_metrics().await.expect("Failed to get task metrics");
    let final_system_score = monitor.get_system_score().await.expect("Failed to get final system score");
    
    // Assert timing-based expectations
    assert!(total_duration >= Duration::from_millis(250), // 10 tasks * 25ms minimum
        "Total processing time should reflect sequential task execution");
    
    assert!(task_metrics.total_tasks >= task_ids.len(),
        "All tasks should be counted in synchronized metrics");
    
    // Verify system score is within reasonable bounds
    let cpu_availability = final_system_score.availability.get(&prisma_ai::prisma::prisma_engine::types::ResourceType::CPU).map_or(100.0, |r| r.0);
    assert!(cpu_availability >= 0.0 && cpu_availability <= 100.0,
        "CPU availability should be normalized: {}", cpu_availability);
    
    {
        use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
        monitor.stop().await.expect("Failed to stop monitor");
    }
    }

    #[tokio::test]
    async fn test_error_propagation_across_monitors() {
    // Test error propagation: verify that errors in one monitor subsystem
    // are properly handled and don't corrupt other monitor states
    
    let mut monitor = create_monitor();
    
    {
        use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
        monitor.start().await.expect("Failed to start monitor");
    }
    
    // Establish baseline state
    let queue_id = "error_test_queue".to_string();
    
    let valid_task_id = TaskId::new();
    // Create valid task metrics and record task creation
    let valid_task_metrics = TaskMetrics {
        task_id: valid_task_id.clone(),
        category: TaskCategory::Internal,
        priority: TaskPriority::Normal,
        status: TaskStatus::Queued,
        created_at: Instant::now(),
        started_at: None,
        completed_at: None,
        queue_time: None,
        processing_time: None,
        queue_name: Some(queue_id.clone()),
        error_message: None,
    };
    monitor.record_task_created(valid_task_metrics).await.expect("Failed to record valid task creation");
    monitor.update_queue_metrics(&queue_id, 1).await.expect("Failed to update queue metrics");
    
    let baseline_task_metrics = monitor.get_task_metrics().await.expect("Failed to get baseline task metrics");
    let baseline_queue_metrics = monitor.get_queue_metrics().await.expect("Failed to get baseline queue metrics");
    let baseline_system_score = monitor.get_system_score().await.expect("Failed to get baseline system score");
    
    // Attempt operations that should fail gracefully
    
    // 1. Try to record completion for a non-existent task (should not crash)
    let invalid_task_id = TaskId::new();
    let invalid_task_result = monitor.record_task_completed(&invalid_task_id, false, Some("Task not found".to_string())).await;
    // This should either succeed (creating the task) or fail gracefully
    
    // 2. Try to record task with invalid data
    let unstarted_task_id = TaskId::new();
    let unstarted_task_metrics = TaskMetrics {
        task_id: unstarted_task_id.clone(),
        category: TaskCategory::Internal,
        priority: TaskPriority::Normal,
        status: TaskStatus::Queued,
        created_at: Instant::now(),
        started_at: None,
        completed_at: None,
        queue_time: None,
        processing_time: None,
        queue_name: Some(queue_id.clone()),
        error_message: None,
    };
    monitor.record_task_created(unstarted_task_metrics).await.expect("Failed to record unstarted task");
    let complete_unstarted_result = monitor.record_task_completed(&unstarted_task_id, false, Some("Task failed".to_string())).await;
    // This should succeed as we're just recording the completion
    
    // 3. Try to update metrics for invalid queue (should handle gracefully without creating new queues)
    // Note: We'll skip this test as update_queue_metrics creates queues if they don't exist
    // let _invalid_queue_result = monitor.update_queue_metrics("non_existent_queue_12345", 0).await;
    
    // Verify that valid operations still work after errors
    monitor.record_task_started(&valid_task_id, &queue_id).await
        .expect("Failed to record valid task start after errors");
    
    tokio::time::sleep(Duration::from_millis(50)).await;
    
    monitor.record_task_completed(&valid_task_id, true, None).await
        .expect("Failed to record valid task completion after errors");
    {
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;
        monitor.record_task_processed(&queue_id, 50.0, true).await.expect("Failed to record valid task processing after errors");
    }
    
    // Allow metrics to update
    tokio::time::sleep(std::time::Duration::from_millis(100)).await;
    
    // Verify system integrity after errors
    let post_error_task_metrics = monitor.get_task_metrics().await.expect("Failed to get post-error task metrics");
    let post_error_queue_metrics = monitor.get_queue_metrics().await.expect("Failed to get post-error queue metrics");
    let post_error_system_score = monitor.get_system_score().await.expect("Failed to get post-error system score");
    
    // Assert that valid operations succeeded despite errors
    assert!(post_error_task_metrics.total_tasks >= baseline_task_metrics.total_tasks,
        "Task count should reflect valid operations despite errors");
    
    assert_eq!(post_error_queue_metrics.queue_metrics.len(), baseline_queue_metrics.queue_metrics.len(),
        "Queue count should remain stable after failed operations");
    
    // System score should still be valid
    let cpu_availability = post_error_system_score.availability.get(&prisma_ai::prisma::prisma_engine::types::ResourceType::CPU).map_or(100.0, |r| r.0);
    assert!(cpu_availability >= 0.0 && cpu_availability <= 100.0,
        "System score should remain valid after errors: {}", cpu_availability);
    
    {
        use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
        monitor.stop().await.expect("Failed to stop monitor");
    }
    }

    #[tokio::test]
    async fn test_comprehensive_monitor_integration_stress() {
    // Comprehensive stress test: combine all monitor subsystems under high load
    // to verify overall system stability and performance
    
    let mut monitor = create_monitor();
    
    {
        use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
        monitor.start().await.expect("Failed to start monitor");
    }
    
    let start_time = std::time::Instant::now();
    
    // Create multiple queues for parallel processing
    let queue_count = 5;
    let tasks_per_queue = 20;
    let total_tasks = queue_count * tasks_per_queue;
    
    let queue_ids: Vec<String> = (0..queue_count)
        .map(|i| format!("stress_queue_{}", i))
        .collect();
    
    // Submit all tasks
    let mut all_task_ids = Vec::new();
    for (_queue_idx, queue_id) in queue_ids.iter().enumerate() {
        // Initialize queue metrics
        monitor.update_queue_metrics(queue_id, 0).await
            .expect("Failed to initialize stress test queue");
            
        for task_idx in 0..tasks_per_queue {
            let task_id = TaskId::new();
            all_task_ids.push(task_id.clone());
            
            // Create task metrics and record task creation
            let task_metrics = TaskMetrics {
                task_id: task_id.clone(),
                category: TaskCategory::Internal,
                priority: TaskPriority::Normal,
                status: TaskStatus::Queued,
                created_at: Instant::now(),
                started_at: None,
                completed_at: None,
                queue_time: None,
                processing_time: None,
                queue_name: Some(queue_id.clone()),
                error_message: None,
            };
            monitor.record_task_created(task_metrics).await
                .expect("Failed to record stress test task creation");
            monitor.update_queue_metrics(queue_id, task_idx + 1).await
                .expect("Failed to update queue metrics for stress test");
        }
    }
    
    let submission_time = start_time.elapsed();
    
    // Process all tasks with concurrent-like behavior
    let mut completed_tasks = 0;
    let processing_start = std::time::Instant::now();
    
    for (idx, task_id) in all_task_ids.iter().enumerate() {
        let queue_id = &queue_ids[idx / tasks_per_queue];
        
        monitor.record_task_started(task_id, queue_id).await
            .expect("Failed to record stress test task start");
        
        // Minimal processing time to simulate high throughput
        tokio::time::sleep(std::time::Duration::from_millis(1)).await;
        
        monitor.record_task_completed(task_id, true, None).await
            .expect("Failed to record stress test task completion");
        {
            use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;
            monitor.record_task_processed(queue_id, 1.0, true).await
                .expect("Failed to record stress test task processing");
        }
        
        completed_tasks += 1;
        
        // Periodic system health checks during processing
        if completed_tasks % 25 == 0 {
            let current_system_score = monitor.get_system_score().await.expect("Failed to get current system score");
            let cpu_availability = current_system_score.availability.get(&prisma_ai::prisma::prisma_engine::types::ResourceType::CPU).map_or(100.0, |r| r.0);
            assert!(cpu_availability >= 0.0 && cpu_availability <= 100.0,
                "System score should remain valid during stress: {}", cpu_availability);
        }
    }
    
    let processing_time = processing_start.elapsed();
    let total_time = start_time.elapsed();
    
    // Allow final metrics collection
    tokio::time::sleep(std::time::Duration::from_millis(200)).await;
    
    // Verify final system state
    let final_task_metrics = monitor.get_task_metrics().await.expect("Failed to get final task metrics");
    let final_queue_metrics = monitor.get_queue_metrics().await.expect("Failed to get final queue metrics");
    let final_system_score = monitor.get_system_score().await.expect("Failed to get final system score");
    
    // Performance assertions
    assert!(submission_time <= std::time::Duration::from_secs(5),
        "Task submission should complete within reasonable time: {:?}", submission_time);
    
    assert!(processing_time <= std::time::Duration::from_secs(10),
        "Task processing should complete within reasonable time: {:?}", processing_time);
    
    // Correctness assertions
    assert!(final_task_metrics.total_tasks >= total_tasks,
        "All stress test tasks should be counted: {} >= {}", final_task_metrics.total_tasks, total_tasks);
    
    assert!(final_queue_metrics.queue_metrics.len() >= queue_count,
        "All stress test queues should be counted: {} >= {}", final_queue_metrics.queue_metrics.len(), queue_count);
    
    let cpu_availability = final_system_score.availability.get(&prisma_ai::prisma::prisma_engine::types::ResourceType::CPU).map_or(100.0, |r| r.0);
    assert!(cpu_availability >= 0.0 && cpu_availability <= 100.0,
        "Final system score should be valid after stress test: {}", cpu_availability);
    
    // Throughput verification
    let throughput = total_tasks as f64 / processing_time.as_secs_f64();
    assert!(throughput > 10.0, // At least 10 tasks per second
        "System should maintain reasonable throughput under stress: {:.2} tasks/sec", throughput);
    
        println!("Stress test completed: {} tasks in {:?} ({:.2} tasks/sec)", 
                 total_tasks, total_time, throughput);
        
        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
            monitor.stop().await.expect("Failed to stop monitor");
        }
    }

    // ============================================================================
    // PERFORMANCE AND SCALABILITY TESTS
    // ============================================================================

    #[tokio::test]
    async fn test_monitor_performance_under_high_system_load() {
        // Test monitor performance when system is under high load
        // This test simulates high system load and verifies monitor responsiveness

        let mut monitor = create_monitor();

        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
            monitor.start().await.expect("Failed to start monitor");
        }

        // Allow initial metrics collection
        tokio::time::sleep(Duration::from_millis(100)).await;

        let start_time = Instant::now();

        // Create high load scenario with multiple concurrent operations
        let num_concurrent_operations = 50;
        let operations_per_batch = 20;

        // Measure baseline performance
        let baseline_system_score = monitor.get_system_score().await.expect("Failed to get baseline system score");
        let baseline_response_time = start_time.elapsed();

        // Create high load with rapid task submissions and completions
        let mut handles: Vec<tokio::task::JoinHandle<()>> = vec![];

        for batch_id in 0..num_concurrent_operations {
            let queue_name = format!("high_load_queue_{}", batch_id);

            for op_id in 0..operations_per_batch {
                let task_id = TaskId::new();

                // Create task with high priority to simulate system stress
                let task_metrics = TaskMetrics {
                    task_id: task_id.clone(),
                    category: TaskCategory::LLMInference, // CPU intensive category
                    priority: TaskPriority::High,
                    status: TaskStatus::Queued,
                    created_at: Instant::now(),
                    started_at: None,
                    completed_at: None,
                    queue_time: None,
                    processing_time: None,
                    queue_name: Some(queue_name.clone()),
                    error_message: None,
                };

                // Record rapid task lifecycle
                monitor.record_task_created(task_metrics).await.expect("Failed to record task creation under load");
                monitor.update_queue_metrics(&queue_name, op_id + 1).await.expect("Failed to update queue metrics under load");
                monitor.record_task_started(&task_id, &queue_name).await.expect("Failed to record task start under load");
                monitor.update_queue_metrics(&queue_name, op_id).await.expect("Failed to update queue metrics for task start under load");

                // Minimal processing time to maximize load
                tokio::time::sleep(Duration::from_millis(1)).await;

                monitor.record_task_completed(&task_id, true, None).await.expect("Failed to record task completion under load");
                {
                    use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;
                    monitor.record_task_processed(&queue_name, 1.0, true).await.expect("Failed to record task processing under load");
                }
            }
        }

        let high_load_duration = start_time.elapsed();

        // Allow metrics to stabilize
        tokio::time::sleep(Duration::from_millis(200)).await;

        // Measure performance under load
        let load_test_start = Instant::now();
        let under_load_system_score = monitor.get_system_score().await.expect("Failed to get system score under load");
        let under_load_response_time = load_test_start.elapsed();

        // Verify monitor responsiveness under load
        assert!(under_load_response_time <= Duration::from_millis(500),
            "Monitor should remain responsive under high load: {:?}", under_load_response_time);

        // Verify system score is still valid
        let cpu_availability = under_load_system_score.availability.get(&prisma_ai::prisma::prisma_engine::types::ResourceType::CPU).map_or(100.0, |r| r.0);
        assert!(cpu_availability >= 0.0 && cpu_availability <= 100.0,
            "CPU availability should remain valid under load: {}", cpu_availability);

        // Verify task metrics are accurate despite high load
        let final_task_metrics = monitor.get_task_metrics().await.expect("Failed to get task metrics under load");
        let expected_total_tasks = num_concurrent_operations * operations_per_batch;

        assert!(final_task_metrics.total_tasks >= expected_total_tasks,
            "All tasks should be tracked despite high load: {} >= {}", final_task_metrics.total_tasks, expected_total_tasks);

        // Performance metrics
        let throughput = (expected_total_tasks as f64) / high_load_duration.as_secs_f64();
        assert!(throughput > 20.0, // Should handle at least 20 tasks per second
            "Monitor should maintain high throughput under load: {:.2} tasks/sec", throughput);

        println!("High load test: {} tasks processed in {:?} ({:.2} tasks/sec)",
                 expected_total_tasks, high_load_duration, throughput);
        println!("Response time under load: {:?} vs baseline: {:?}",
                 under_load_response_time, baseline_response_time);

        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
            monitor.stop().await.expect("Failed to stop monitor");
        }
    }

    #[tokio::test]
    async fn test_monitor_memory_usage_and_optimization() {
        // Test monitor memory usage patterns and optimization features
        // This test monitors memory consumption during intensive operations

        let mut monitor = create_monitor();

        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
            monitor.start().await.expect("Failed to start monitor");
        }

        // Allow initial metrics collection
        tokio::time::sleep(Duration::from_millis(100)).await;

        // Get baseline memory metrics
        let baseline_system_metrics = monitor.get_system_metrics().await.expect("Failed to get baseline system metrics");
        let baseline_memory_usage = baseline_system_metrics.memory.as_ref()
            .map(|m| m.usage_percent)
            .unwrap_or(0.0);

        // Create memory-intensive scenario with large task volumes
        let memory_test_tasks = 500;
        let queue_name = "memory_test_queue";
        let mut task_ids = Vec::with_capacity(memory_test_tasks);

        let memory_test_start = Instant::now();

        // Submit large volume of tasks to test memory usage
        for i in 0..memory_test_tasks {
            let task_id = TaskId::new();
            task_ids.push(task_id.clone());

            let task_metrics = TaskMetrics {
                task_id: task_id.clone(),
                category: TaskCategory::FileProcessing, // Memory intensive category
                priority: TaskPriority::Normal,
                status: TaskStatus::Queued,
                created_at: Instant::now(),
                started_at: None,
                completed_at: None,
                queue_time: None,
                processing_time: None,
                queue_name: Some(queue_name.to_string()),
                error_message: None,
            };

            monitor.record_task_created(task_metrics).await.expect("Failed to record task for memory test");
            monitor.update_queue_metrics(queue_name, i + 1).await.expect("Failed to update queue metrics for memory test");

            // Check memory usage periodically
            if i % 100 == 0 {
                let current_metrics = monitor.get_system_metrics().await.expect("Failed to get current system metrics");
                let current_memory_usage = current_metrics.memory.as_ref()
                    .map(|m| m.usage_percent)
                    .unwrap_or(0.0);

                // Memory usage should not grow excessively
                assert!(current_memory_usage < baseline_memory_usage + 20.0,
                    "Memory usage should not grow excessively during task submission: {:.2}% vs baseline {:.2}%",
                    current_memory_usage, baseline_memory_usage);
            }
        }

        // Process all tasks and monitor memory optimization
        for (idx, task_id) in task_ids.iter().enumerate() {
            monitor.record_task_started(task_id, queue_name).await.expect("Failed to record task start for memory test");
            monitor.update_queue_metrics(queue_name, memory_test_tasks - idx - 1).await.expect("Failed to update queue metrics for memory test");

            // Minimal processing time
            tokio::time::sleep(Duration::from_millis(1)).await;

            monitor.record_task_completed(task_id, true, None).await.expect("Failed to record task completion for memory test");
            {
                use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;
                monitor.record_task_processed(queue_name, 1.0, true).await.expect("Failed to record task processing for memory test");
            }

            // Check memory optimization during processing
            if idx % 100 == 0 {
                let current_metrics = monitor.get_system_metrics().await.expect("Failed to get current system metrics during processing");
                let current_memory_usage = current_metrics.memory.as_ref()
                    .map(|m| m.usage_percent)
                    .unwrap_or(0.0);

                // Memory should be managed efficiently
                assert!(current_memory_usage < baseline_memory_usage + 25.0,
                    "Memory usage should be optimized during processing: {:.2}% vs baseline {:.2}%",
                    current_memory_usage, baseline_memory_usage);
            }
        }

        let memory_test_duration = memory_test_start.elapsed();

        // Allow memory cleanup and optimization
        tokio::time::sleep(Duration::from_millis(300)).await;

        // Verify memory optimization after processing
        let final_system_metrics = monitor.get_system_metrics().await.expect("Failed to get final system metrics");
        let final_memory_usage = final_system_metrics.memory.as_ref()
            .map(|m| m.usage_percent)
            .unwrap_or(0.0);

        // Memory should return to reasonable levels after processing
        assert!(final_memory_usage <= baseline_memory_usage + 15.0,
            "Memory should be optimized after processing completion: {:.2}% vs baseline {:.2}%",
            final_memory_usage, baseline_memory_usage);

        // Verify task metrics accuracy despite memory optimization
        let final_task_metrics = monitor.get_task_metrics().await.expect("Failed to get final task metrics");
        assert!(final_task_metrics.total_tasks >= memory_test_tasks,
            "All tasks should be tracked despite memory optimization: {} >= {}",
            final_task_metrics.total_tasks, memory_test_tasks);

        // Performance verification
        let memory_throughput = (memory_test_tasks as f64) / memory_test_duration.as_secs_f64();
        assert!(memory_throughput > 30.0, // Should handle at least 30 tasks per second
            "Memory optimization should not significantly impact throughput: {:.2} tasks/sec", memory_throughput);

        println!("Memory test: {} tasks processed in {:?} ({:.2} tasks/sec)",
                 memory_test_tasks, memory_test_duration, memory_throughput);
        println!("Memory usage: baseline {:.2}% -> peak during processing -> final {:.2}%",
                 baseline_memory_usage, final_memory_usage);

        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
            monitor.stop().await.expect("Failed to stop monitor");
        }
    }

    #[tokio::test]
    async fn test_monitor_cpu_overhead_and_efficiency() {
        // Test monitor CPU overhead and efficiency during intensive monitoring
        // This test measures CPU impact of monitoring operations

        let mut monitor = create_monitor();

        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
            monitor.start().await.expect("Failed to start monitor");
        }

        // Allow initial metrics collection and stabilization
        tokio::time::sleep(Duration::from_millis(200)).await;

        // Get baseline CPU metrics
        let baseline_system_metrics = monitor.get_system_metrics().await.expect("Failed to get baseline system metrics");
        let baseline_cpu_usage = baseline_system_metrics.cpu.as_ref()
            .map(|c| c.usage_percent)
            .unwrap_or(0.0);

        // Create CPU efficiency test scenario
        let efficiency_test_tasks = 200;
        let num_queues = 10;
        let queue_names: Vec<String> = (0..num_queues)
            .map(|i| format!("cpu_efficiency_queue_{}", i))
            .collect();

        let cpu_test_start = Instant::now();

        // Rapid monitoring operations to test CPU efficiency
        for i in 0..efficiency_test_tasks {
            let queue_idx = i % num_queues;
            let queue_name = &queue_names[queue_idx];
            let task_id = TaskId::new();

            let task_metrics = TaskMetrics {
                task_id: task_id.clone(),
                category: TaskCategory::LLMInference,
                priority: TaskPriority::High,
                status: TaskStatus::Queued,
                created_at: Instant::now(),
                started_at: None,
                completed_at: None,
                queue_time: None,
                processing_time: None,
                queue_name: Some(queue_name.clone()),
                error_message: None,
            };

            // Rapid task lifecycle operations
            monitor.record_task_created(task_metrics).await.expect("Failed to record task for CPU test");
            monitor.update_queue_metrics(queue_name, (i / num_queues) + 1).await.expect("Failed to update queue metrics for CPU test");
            monitor.record_task_started(&task_id, queue_name).await.expect("Failed to record task start for CPU test");
            monitor.update_queue_metrics(queue_name, i / num_queues).await.expect("Failed to update queue metrics for task start in CPU test");
            monitor.record_task_completed(&task_id, true, None).await.expect("Failed to record task completion for CPU test");
            {
                use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;
                monitor.record_task_processed(queue_name, 1.0, true).await.expect("Failed to record task processing for CPU test");
            }

            // Check CPU overhead periodically
            if i % 50 == 0 {
                let current_metrics = monitor.get_system_metrics().await.expect("Failed to get current system metrics during CPU test");
                let current_cpu_usage = current_metrics.cpu.as_ref()
                    .map(|c| c.usage_percent)
                    .unwrap_or(0.0);

                // Monitor should not cause excessive CPU overhead
                assert!(current_cpu_usage < baseline_cpu_usage + 30.0,
                    "Monitor CPU overhead should be reasonable: {:.2}% vs baseline {:.2}%",
                    current_cpu_usage, baseline_cpu_usage);
            }
        }

        let cpu_test_duration = cpu_test_start.elapsed();

        // Allow CPU usage to stabilize
        tokio::time::sleep(Duration::from_millis(200)).await;

        // Measure final CPU efficiency
        let final_system_metrics = monitor.get_system_metrics().await.expect("Failed to get final system metrics");
        let final_cpu_usage = final_system_metrics.cpu.as_ref()
            .map(|c| c.usage_percent)
            .unwrap_or(0.0);

        // CPU usage should return to reasonable levels
        assert!(final_cpu_usage <= baseline_cpu_usage + 20.0,
            "CPU usage should be efficient after monitoring operations: {:.2}% vs baseline {:.2}%",
            final_cpu_usage, baseline_cpu_usage);

        // Verify monitoring efficiency
        let cpu_throughput = (efficiency_test_tasks as f64) / cpu_test_duration.as_secs_f64();
        assert!(cpu_throughput > 25.0, // Should handle at least 25 tasks per second efficiently
            "Monitor should be CPU efficient: {:.2} tasks/sec", cpu_throughput);

        // Verify all operations were tracked despite CPU efficiency focus
        let final_task_metrics = monitor.get_task_metrics().await.expect("Failed to get final task metrics");
        assert!(final_task_metrics.total_tasks >= efficiency_test_tasks,
            "All tasks should be tracked despite CPU efficiency optimizations: {} >= {}",
            final_task_metrics.total_tasks, efficiency_test_tasks);

        // Verify queue metrics accuracy
        let final_queue_metrics = monitor.get_queue_metrics().await.expect("Failed to get final queue metrics");
        assert!(final_queue_metrics.queue_metrics.len() >= num_queues,
            "All queues should be tracked despite CPU efficiency focus: {} >= {}",
            final_queue_metrics.queue_metrics.len(), num_queues);

        println!("CPU efficiency test: {} tasks across {} queues in {:?} ({:.2} tasks/sec)",
                 efficiency_test_tasks, num_queues, cpu_test_duration, cpu_throughput);
        println!("CPU usage: baseline {:.2}% -> final {:.2}% (overhead: {:.2}%)",
                 baseline_cpu_usage, final_cpu_usage, final_cpu_usage - baseline_cpu_usage);

        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
            monitor.stop().await.expect("Failed to stop monitor");
        }
    }

    #[tokio::test]
    async fn test_monitor_scalability_with_increasing_metrics_volume() {
        // Test monitor scalability as metrics volume increases over time
        // This test gradually increases load to verify scalability characteristics

        let mut monitor = create_monitor();

        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
            monitor.start().await.expect("Failed to start monitor");
        }

        // Allow initial stabilization
        tokio::time::sleep(Duration::from_millis(100)).await;

        // Scalability test parameters
        let scalability_phases = 5;
        let base_tasks_per_phase = 50;
        let phase_multiplier: u32 = 2; // Each phase doubles the load
        let queues_per_phase = 3;

        let mut total_tasks_processed = 0;
        let mut phase_performance_data: Vec<(usize, usize, Duration, f64)> = Vec::new();

        let scalability_test_start = Instant::now();

        // Execute scalability phases with increasing load
        for phase in 0..scalability_phases {
            let phase_start = Instant::now();
            let tasks_in_phase = base_tasks_per_phase * (phase_multiplier.pow(phase as u32) as usize);
            let phase_queues: Vec<String> = (0..queues_per_phase)
                .map(|i| format!("scalability_phase_{}_queue_{}", phase, i))
                .collect();

            println!("Starting scalability phase {}: {} tasks across {} queues",
                     phase, tasks_in_phase, queues_per_phase);

            // Submit tasks for this phase
            let mut phase_task_ids = Vec::new();
            for i in 0..tasks_in_phase {
                let queue_idx = i % queues_per_phase;
                let queue_name = &phase_queues[queue_idx];
                let task_id = TaskId::new();
                phase_task_ids.push(task_id.clone());

                let task_metrics = TaskMetrics {
                    task_id: task_id.clone(),
                    category: TaskCategory::Internal,
                    priority: TaskPriority::Normal,
                    status: TaskStatus::Queued,
                    created_at: Instant::now(),
                    started_at: None,
                    completed_at: None,
                    queue_time: None,
                    processing_time: None,
                    queue_name: Some(queue_name.clone()),
                    error_message: None,
                };

                monitor.record_task_created(task_metrics).await.expect("Failed to record task for scalability test");
                monitor.update_queue_metrics(queue_name, (i / queues_per_phase) + 1).await.expect("Failed to update queue metrics for scalability test");
            }

            // Process tasks for this phase
            for (idx, task_id) in phase_task_ids.iter().enumerate() {
                let queue_idx = idx % queues_per_phase;
                let queue_name = &phase_queues[queue_idx];

                monitor.record_task_started(task_id, queue_name).await.expect("Failed to record task start for scalability test");
                monitor.update_queue_metrics(queue_name, tasks_in_phase - idx - 1).await.expect("Failed to update queue metrics for task start in scalability test");

                // Minimal processing time to focus on scalability
                tokio::time::sleep(Duration::from_millis(1)).await;

                monitor.record_task_completed(task_id, true, None).await.expect("Failed to record task completion for scalability test");
                {
                    use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;
                    monitor.record_task_processed(queue_name, 1.0, true).await.expect("Failed to record task processing for scalability test");
                }
            }

            let phase_duration = phase_start.elapsed();
            let phase_throughput = (tasks_in_phase as f64) / phase_duration.as_secs_f64();

            phase_performance_data.push((phase, tasks_in_phase, phase_duration, phase_throughput));
            total_tasks_processed += tasks_in_phase;

            // Verify system remains responsive as load increases
            let phase_system_check_start = Instant::now();
            let phase_system_score = monitor.get_system_score().await.expect("Failed to get system score during scalability test");
            let phase_response_time = phase_system_check_start.elapsed();

            // Response time should remain reasonable even as load increases
            assert!(phase_response_time <= Duration::from_millis(1000),
                "System should remain responsive in phase {}: {:?}", phase, phase_response_time);

            // System score should remain valid
            let cpu_availability = phase_system_score.availability.get(&prisma_ai::prisma::prisma_engine::types::ResourceType::CPU).map_or(100.0, |r| r.0);
            assert!(cpu_availability >= 0.0 && cpu_availability <= 100.0,
                "System score should remain valid in phase {}: {}", phase, cpu_availability);

            println!("Phase {} completed: {} tasks in {:?} ({:.2} tasks/sec, response time: {:?})",
                     phase, tasks_in_phase, phase_duration, phase_throughput, phase_response_time);

            // Brief pause between phases to allow metrics stabilization
            tokio::time::sleep(Duration::from_millis(50)).await;
        }

        let total_scalability_duration = scalability_test_start.elapsed();

        // Allow final metrics collection
        tokio::time::sleep(Duration::from_millis(200)).await;

        // Verify scalability characteristics
        let final_task_metrics = monitor.get_task_metrics().await.expect("Failed to get final task metrics");
        let final_queue_metrics = monitor.get_queue_metrics().await.expect("Failed to get final queue metrics");

        // All tasks should be tracked
        assert!(final_task_metrics.total_tasks >= total_tasks_processed,
            "All scalability test tasks should be tracked: {} >= {}",
            final_task_metrics.total_tasks, total_tasks_processed);

        // All queues should be tracked
        let expected_total_queues = scalability_phases * queues_per_phase;
        assert!(final_queue_metrics.queue_metrics.len() >= expected_total_queues,
            "All scalability test queues should be tracked: {} >= {}",
            final_queue_metrics.queue_metrics.len(), expected_total_queues);

        // Analyze scalability performance
        let overall_throughput = (total_tasks_processed as f64) / total_scalability_duration.as_secs_f64();

        // System should maintain reasonable throughput even with increasing load
        assert!(overall_throughput > 15.0, // Should maintain at least 15 tasks per second overall
            "System should maintain reasonable throughput during scalability test: {:.2} tasks/sec", overall_throughput);

        // Verify throughput doesn't degrade excessively with increased load
        let first_phase_throughput = phase_performance_data[0].3;
        let last_phase_throughput = phase_performance_data[scalability_phases - 1].3;
        let throughput_degradation = (first_phase_throughput - last_phase_throughput) / first_phase_throughput;

        assert!(throughput_degradation < 0.7, // Throughput should not degrade by more than 70%
            "Throughput degradation should be reasonable: {:.2}% (from {:.2} to {:.2} tasks/sec)",
            throughput_degradation * 100.0, first_phase_throughput, last_phase_throughput);

        println!("Scalability test completed: {} total tasks across {} phases in {:?} ({:.2} tasks/sec overall)",
                 total_tasks_processed, scalability_phases, total_scalability_duration, overall_throughput);

        // Print phase-by-phase performance
        for (phase, tasks, duration, throughput) in phase_performance_data {
            println!("  Phase {}: {} tasks in {:?} ({:.2} tasks/sec)",
                     phase, tasks, duration, throughput);
        }

        {
            use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor as _;
            monitor.stop().await.expect("Failed to stop monitor");
        }
    }
}