use std::time::{Duration, Instant};
use tokio::time::sleep;

// Import types without conflicting traits
use prisma_ai::prisma::prisma_engine::monitor::system::{
    MemoryMonitor,
    DiskMonitor,
    NetworkMonitor,
    types::SystemMonitorConfig,
};

// Import Prisma monitor types and traits
use prisma_ai::prisma::prisma_engine::types::{TaskId, TaskCategory, TaskPriority};
use prisma_ai::prisma::prisma_engine::monitor::prisma::types::{TaskMetrics, TaskStatus};

// Import Queue monitor types
use prisma_ai::prisma::prisma_engine::monitor::prisma::{
    queue_monitor::QueueMonitor,
    task_monitor::TaskMonitor,
    types::PrismaMonitorConfig,
    traits::{Monitorable, QueueMonitoring, TaskMonitoring},
};

// Helper function to create a default memory monitor
fn create_default_memory_monitor() -> MemoryMonitor {
    let config = SystemMonitorConfig::default();
    MemoryMonitor::new(config)
}

// Helper function to create a custom memory monitor
fn create_custom_memory_monitor(poll_interval_ms: u64) -> MemoryMonitor {
    let config = SystemMonitorConfig {
        poll_interval_ms,
        monitor_cpu: true,
        monitor_memory: true,
        monitor_disk: true,
        monitor_network: true,
    };
    MemoryMonitor::new(config)
}

// Helper function to create a default disk monitor
fn create_default_disk_monitor() -> DiskMonitor {
    let config = SystemMonitorConfig::default();
    DiskMonitor::new(config)
}

// Helper function to create a custom disk monitor
fn create_custom_disk_monitor(poll_interval_ms: u64) -> DiskMonitor {
    let config = SystemMonitorConfig {
        poll_interval_ms,
        monitor_cpu: true,
        monitor_memory: true,
        monitor_disk: true,
        monitor_network: true,
    };
    DiskMonitor::new(config)
}

// Helper function to create a default network monitor
fn create_default_network_monitor() -> NetworkMonitor {
    let config = SystemMonitorConfig::default();
    NetworkMonitor::new(config)
}

// Helper function to create a custom network monitor
fn create_custom_network_monitor(poll_interval_ms: u64) -> NetworkMonitor {
    let config = SystemMonitorConfig {
        poll_interval_ms,
        monitor_cpu: true,
        monitor_memory: true,
        monitor_disk: true,
        monitor_network: true,
    };
    NetworkMonitor::new(config)
}

// Helper function to create a default queue monitor
fn create_default_queue_monitor() -> QueueMonitor {
    let config = PrismaMonitorConfig::default();
    QueueMonitor::new(config)
}

// Helper function to create a custom queue monitor
fn create_custom_queue_monitor(queue_poll_interval_ms: u64, task_poll_interval_ms: u64) -> QueueMonitor {
    let config = PrismaMonitorConfig {
        queue_poll_interval_ms,
        task_poll_interval_ms,
        max_task_history: 1000,
        enable_detailed_task_tracking: true,
    };
    QueueMonitor::new(config)
}

// Helper function to create a queue monitor with minimal polling interval for testing
fn create_fast_queue_monitor() -> QueueMonitor {
    let config = PrismaMonitorConfig {
        queue_poll_interval_ms: 100, // Very fast polling for tests
        task_poll_interval_ms: 100,
        max_task_history: 100,
        enable_detailed_task_tracking: true,
    };
    QueueMonitor::new(config)
}

// Helper function to create a queue monitor with custom task history size
fn create_queue_monitor_with_history(max_task_history: usize) -> QueueMonitor {
    let config = PrismaMonitorConfig {
        queue_poll_interval_ms: 1000,
        task_poll_interval_ms: 1000,
        max_task_history,
        enable_detailed_task_tracking: true,
    };
    QueueMonitor::new(config)
}

/// Test MemoryMonitor initialization and configuration
#[tokio::test]
async fn test_memory_monitor_initialization_and_configuration() {
    println!("Starting MemoryMonitor initialization and configuration test");

    // Test 1: Default configuration initialization
    {
        println!("Testing MemoryMonitor initialization with default configuration");

        let monitor = create_default_memory_monitor();

        // Verify monitor was created successfully
        // Import ResourceMonitor trait locally to access get_poll_interval
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;
        let poll_interval = monitor.get_poll_interval();
        assert_eq!(poll_interval.as_millis(), 5000, "Default poll interval should be 5000ms");

        println!("MemoryMonitor default initialization test passed");
    }

    // Test 2: Custom configuration initialization
    {
        println!("Testing MemoryMonitor initialization with custom configuration");

        let custom_intervals = vec![500, 1000, 2000, 10000];

        // Import ResourceMonitor trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        for (i, interval) in custom_intervals.into_iter().enumerate() {
            println!("Testing custom config {}: poll_interval_ms = {}", i + 1, interval);

            let monitor = create_custom_memory_monitor(interval);
            let poll_interval = monitor.get_poll_interval();

            assert_eq!(poll_interval.as_millis(), interval as u128,
                "Custom poll interval should be preserved for config {}", i + 1);

            println!("Custom config {} passed", i + 1);
        }

        println!("MemoryMonitor custom initialization test passed");
    }

    // Test 3: Configuration validation
    {
        println!("Testing MemoryMonitor configuration validation");

        // Test edge case configurations
        let edge_configs = vec![
            (0, "Zero poll interval"),
            (1, "Minimum poll interval"),
            (u64::MAX, "Maximum poll interval"),
        ];

        // Import ResourceMonitor trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        for (i, (interval, description)) in edge_configs.into_iter().enumerate() {
            println!("Testing edge config {}: {} ({}ms)", i + 1, description, interval);

            let monitor = create_custom_memory_monitor(interval);
            let poll_interval = monitor.get_poll_interval();

            assert_eq!(poll_interval.as_millis(), interval as u128,
                "Edge case poll interval should be preserved for {}", description);

            println!("Edge config {} ({}) passed", i + 1, description);
        }

        println!("MemoryMonitor configuration validation test passed");
    }

    println!("MemoryMonitor initialization and configuration test passed");
}

/// Test memory monitoring task management
#[tokio::test]
async fn test_memory_monitoring_task_management() {
    println!("Starting memory monitoring task management test");

    // Test 1: Start monitoring task
    {
        println!("Testing memory monitoring task start");

        let mut monitor = create_default_memory_monitor();

        // Import the trait locally to avoid conflicts
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        let start_result = monitor.start().await;
        assert!(start_result.is_ok(), "Memory monitor should start successfully");

        // Allow time for monitoring task to initialize
        sleep(Duration::from_millis(100)).await;

        // Stop the monitor
        let stop_result = monitor.stop().await;
        assert!(stop_result.is_ok(), "Memory monitor should stop successfully");

        println!("Memory monitoring task start test passed");
    }

    // Test 2: Multiple start/stop cycles
    {
        println!("Testing multiple memory monitoring start/stop cycles");

        let mut monitor = create_default_memory_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        for cycle in 1..=3 {
            println!("Memory monitoring cycle {}", cycle);

            let start_result = monitor.start().await;
            assert!(start_result.is_ok(), "Memory monitor should start in cycle {}", cycle);

            // Brief monitoring period
            sleep(Duration::from_millis(50)).await;

            let stop_result = monitor.stop().await;
            assert!(stop_result.is_ok(), "Memory monitor should stop in cycle {}", cycle);

            // Brief pause between cycles
            sleep(Duration::from_millis(20)).await;
        }

        println!("Multiple memory monitoring start/stop cycles test passed");
    }

    // Test 3: Idempotent operations
    {
        println!("Testing idempotent memory monitoring operations");

        let mut monitor = create_default_memory_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Multiple starts should be idempotent
        monitor.start().await.expect("First start should succeed");
        let second_start = monitor.start().await;
        assert!(second_start.is_ok(), "Second start should be idempotent");

        // Multiple stops should be idempotent
        monitor.stop().await.expect("First stop should succeed");
        let second_stop = monitor.stop().await;
        assert!(second_stop.is_ok(), "Second stop should be idempotent");

        println!("Idempotent memory monitoring operations test passed");
    }

    println!("Memory monitoring task management test passed");
}

/// Test memory monitor error handling
#[tokio::test]
async fn test_memory_monitor_error_handling() {
    println!("Starting memory monitor error handling test");

    // Test 1: Error recovery during monitoring
    {
        println!("Testing memory monitor error recovery");

        let mut monitor = create_default_memory_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Start monitoring
        monitor.start().await.expect("Monitor should start");

        // Allow some monitoring activity
        sleep(Duration::from_millis(200)).await;

        // Monitor should continue to function despite any internal errors
        // (The implementation should handle errors gracefully)

        // Stop monitoring
        monitor.stop().await.expect("Monitor should stop");

        println!("Memory monitor error recovery test passed");
    }

    // Test 2: Configuration error handling
    {
        println!("Testing memory monitor configuration error handling");

        // Test with potentially problematic configurations
        let problematic_configs = vec![
            (0, "Zero interval"),
            (u64::MAX, "Maximum interval"),
        ];

        for (interval, description) in problematic_configs {
            println!("Testing configuration error handling: {}", description);

            let monitor = create_custom_memory_monitor(interval);

            // Monitor should be created successfully even with edge case configs
            // Import ResourceMonitor trait locally to access get_poll_interval
            use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;
            let poll_interval = monitor.get_poll_interval();
            assert_eq!(poll_interval.as_millis(), interval as u128,
                "Configuration should be preserved for {}", description);

            println!("Configuration error handling for {} passed", description);
        }

        println!("Memory monitor configuration error handling test passed");
    }

    println!("Memory monitor error handling test passed");
}

/// Test memory monitor cleanup operations
#[tokio::test]
async fn test_memory_monitor_cleanup_operations() {
    println!("Starting memory monitor cleanup operations test");

    // Test 1: Resource cleanup after stop
    {
        println!("Testing memory monitor resource cleanup");

        let mut monitor = create_default_memory_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Start and run monitoring
        monitor.start().await.expect("Monitor should start");
        sleep(Duration::from_millis(100)).await;

        // Stop monitoring and verify cleanup
        let stop_result = monitor.stop().await;
        assert!(stop_result.is_ok(), "Monitor should stop and cleanup successfully");

        // Monitor should be in a clean state after stop
        // (Implementation-specific cleanup verification would go here)

        println!("Memory monitor resource cleanup test passed");
    }

    // Test 2: Cleanup during multiple cycles
    {
        println!("Testing memory monitor cleanup during multiple cycles");

        let mut monitor = create_default_memory_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        for cycle in 1..=5 {
            monitor.start().await.expect(&format!("Monitor should start in cycle {}", cycle));
            sleep(Duration::from_millis(30)).await;
            monitor.stop().await.expect(&format!("Monitor should stop and cleanup in cycle {}", cycle));

            // Brief pause to allow cleanup
            sleep(Duration::from_millis(10)).await;
        }

        println!("Memory monitor cleanup during multiple cycles test passed");
    }

    println!("Memory monitor cleanup operations test passed");
}

/// Test disk availability based on free disk space
#[tokio::test]
async fn test_disk_availability_based_on_free_space() {
    println!("Starting disk availability based on free space test");

    // Test 1: Basic availability calculation with default monitor
    {
        println!("Testing basic disk availability calculation");

        let mut monitor = create_default_disk_monitor();

        // Import traits locally to avoid conflicts
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, DiskMonitoring};

        // Start monitoring to collect real disk metrics
        monitor.start().await.expect("Disk monitor should start");

        // Allow time for initial metrics collection
        sleep(Duration::from_millis(200)).await;

        // Get availability based on current disk space
        let availability = monitor.get_availability().await;
        assert!(availability.is_ok(), "Should get disk availability successfully");

        let availability_value = availability.unwrap().0;
        assert!(availability_value >= 0.0 && availability_value <= 100.0,
            "Availability should be between 0-100%, got: {}", availability_value);

        // Get disk metrics to verify calculation
        let disk_metrics = monitor.get_disk_metrics().await;
        assert!(disk_metrics.is_ok(), "Should get disk metrics successfully");

        let metrics = disk_metrics.unwrap();
        assert!(!metrics.disks.is_empty(), "Should have at least one disk");

        // Verify availability calculation logic
        let avg_usage: f64 = metrics.disks.values()
            .map(|d| d.usage_percent)
            .sum::<f64>() / metrics.disks.len() as f64;
        let expected_availability = 100.0 - avg_usage;

        assert!((availability_value - expected_availability).abs() < 0.1,
            "Availability calculation should match expected: {} vs {}",
            availability_value, expected_availability);

        monitor.stop().await.expect("Disk monitor should stop");
        println!("Basic disk availability calculation test passed");
    }

    // Test 2: Availability with different poll intervals
    {
        println!("Testing disk availability with different poll intervals");

        let poll_intervals = vec![500, 1000, 2000];

        for (_i, interval) in poll_intervals.into_iter().enumerate() {
            println!("Testing availability with poll interval: {}ms", interval);

            let mut monitor = create_custom_disk_monitor(interval);

            // Import traits locally
            use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

            monitor.start().await.expect("Disk monitor should start");
            sleep(Duration::from_millis(300)).await;

            let availability = monitor.get_availability().await;
            assert!(availability.is_ok(), "Should get availability for interval {}", interval);

            let availability_value = availability.unwrap().0;
            assert!(availability_value >= 0.0 && availability_value <= 100.0,
                "Availability should be valid for interval {}: {}", interval, availability_value);

            monitor.stop().await.expect("Disk monitor should stop");
            println!("Poll interval {} test passed", interval);
        }

        println!("Disk availability with different poll intervals test passed");
    }

    // Test 3: Availability calculation consistency
    {
        println!("Testing disk availability calculation consistency");

        let mut monitor = create_default_disk_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        monitor.start().await.expect("Disk monitor should start");
        sleep(Duration::from_millis(200)).await;

        // Take multiple availability measurements
        let mut measurements = Vec::new();
        for i in 0..5 {
            let availability = monitor.get_availability().await
                .expect("Should get availability");
            measurements.push(availability.0);

            println!("Measurement {}: {:.2}%", i + 1, availability.0);
            sleep(Duration::from_millis(100)).await;
        }

        // Verify measurements are consistent (within reasonable variance)
        let avg = measurements.iter().sum::<f64>() / measurements.len() as f64;
        let variance = measurements.iter()
            .map(|x| (x - avg).powi(2))
            .sum::<f64>() / measurements.len() as f64;
        let std_dev = variance.sqrt();

        assert!(std_dev < 5.0, "Availability measurements should be consistent, std_dev: {}", std_dev);

        monitor.stop().await.expect("Disk monitor should stop");
        println!("Disk availability calculation consistency test passed");
    }

    println!("Disk availability based on free space test passed");
}

/// Test disk availability with I/O load consideration
#[tokio::test]
async fn test_disk_availability_with_io_load_consideration() {
    println!("Starting disk availability with I/O load consideration test");

    // Test 1: Monitor I/O metrics collection
    {
        println!("Testing disk I/O metrics collection");

        let mut monitor = create_default_disk_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, DiskMonitoring};

        monitor.start().await.expect("Disk monitor should start");

        // Allow time for I/O metrics to be collected
        sleep(Duration::from_millis(500)).await;

        let disk_metrics = monitor.get_disk_metrics().await;
        assert!(disk_metrics.is_ok(), "Should get disk metrics successfully");

        let metrics = disk_metrics.unwrap();
        assert!(!metrics.disks.is_empty(), "Should have at least one disk");

        // Verify I/O metrics are being collected
        for (mount_point, disk_info) in &metrics.disks {
            println!("Disk {}: Read IOPS: {:?}, Write IOPS: {:?}",
                mount_point, disk_info.read_iops, disk_info.write_iops);
            println!("Disk {}: Read bytes/sec: {:?}, Write bytes/sec: {:?}",
                mount_point, disk_info.read_bytes_per_sec, disk_info.write_bytes_per_sec);

            // I/O metrics may be None initially, but should be Some after monitoring
            // Note: On some systems, I/O metrics might not be available
            if disk_info.read_iops.is_some() {
                assert!(disk_info.read_iops.unwrap() >= 0.0, "Read IOPS should be non-negative");
            }
            if disk_info.write_iops.is_some() {
                assert!(disk_info.write_iops.unwrap() >= 0.0, "Write IOPS should be non-negative");
            }
        }

        // Check total I/O metrics
        if let Some(total_iops) = metrics.total_iops {
            assert!(total_iops >= 0.0, "Total IOPS should be non-negative: {}", total_iops);
            println!("Total IOPS: {:.2}", total_iops);
        }

        if let Some(total_throughput) = metrics.total_throughput_bytes_per_sec {
            assert!(total_throughput >= 0.0, "Total throughput should be non-negative: {}", total_throughput);
            println!("Total throughput: {:.2} bytes/sec", total_throughput);
        }

        monitor.stop().await.expect("Disk monitor should stop");
        println!("Disk I/O metrics collection test passed");
    }

    // Test 2: I/O load impact on availability
    {
        println!("Testing I/O load impact on availability");

        let mut monitor = create_default_disk_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, DiskMonitoring};

        monitor.start().await.expect("Disk monitor should start");

        // Collect baseline availability
        sleep(Duration::from_millis(300)).await;
        let baseline_availability = monitor.get_availability().await
            .expect("Should get baseline availability").0;

        println!("Baseline availability: {:.2}%", baseline_availability);

        // Monitor over time to see I/O impact
        let mut availability_samples = Vec::new();
        for i in 0..10 {
            sleep(Duration::from_millis(200)).await;

            let availability = monitor.get_availability().await
                .expect("Should get availability").0;
            let metrics = monitor.get_disk_metrics().await
                .expect("Should get metrics");

            availability_samples.push(availability);

            // Log I/O activity and availability
            let total_iops = metrics.total_iops.unwrap_or(0.0);
            let total_throughput = metrics.total_throughput_bytes_per_sec.unwrap_or(0.0);

            println!("Sample {}: Availability={:.2}%, IOPS={:.2}, Throughput={:.2} bytes/sec",
                i + 1, availability, total_iops, total_throughput);
        }

        // Verify availability remains within reasonable bounds
        let min_availability = availability_samples.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_availability = availability_samples.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));

        assert!(min_availability >= 0.0, "Minimum availability should be non-negative: {}", min_availability);
        assert!(max_availability <= 100.0, "Maximum availability should not exceed 100%: {}", max_availability);

        println!("Availability range: {:.2}% - {:.2}%", min_availability, max_availability);

        monitor.stop().await.expect("Disk monitor should stop");
        println!("I/O load impact on availability test passed");
    }

    // Test 3: IOPS threshold impact
    {
        println!("Testing IOPS threshold impact on availability");

        let mut monitor = create_default_disk_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, DiskMonitoring};

        monitor.start().await.expect("Disk monitor should start");
        sleep(Duration::from_millis(400)).await;

        // Get current IOPS and availability
        let metrics = monitor.get_disk_metrics().await
            .expect("Should get disk metrics");
        let availability = monitor.get_availability().await
            .expect("Should get availability").0;

        let current_iops = metrics.total_iops.unwrap_or(0.0);
        println!("Current IOPS: {:.2}, Availability: {:.2}%", current_iops, availability);

        // Verify IOPS calculation consistency
        let calculated_iops: f64 = metrics.disks.values()
            .map(|disk| {
                let read_iops = disk.read_iops.unwrap_or(0.0);
                let write_iops = disk.write_iops.unwrap_or(0.0);
                read_iops + write_iops
            })
            .sum();

        if current_iops > 0.0 && calculated_iops > 0.0 {
            // Allow for some variance in IOPS calculation due to timing
            let iops_diff = (current_iops - calculated_iops).abs();
            let tolerance = current_iops * 0.1; // 10% tolerance
            assert!(iops_diff <= tolerance,
                "IOPS calculation should be consistent: total={:.2}, calculated={:.2}",
                current_iops, calculated_iops);
        }

        monitor.stop().await.expect("Disk monitor should stop");
        println!("IOPS threshold impact test passed");
    }

    println!("Disk availability with I/O load consideration test passed");
}

/// Test disk availability across multiple disks
#[tokio::test]
async fn test_disk_availability_across_multiple_disks() {
    println!("Starting disk availability across multiple disks test");

    // Test 1: Multiple disk detection and metrics
    {
        println!("Testing multiple disk detection and metrics");

        let mut monitor = create_default_disk_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, DiskMonitoring};

        monitor.start().await.expect("Disk monitor should start");
        sleep(Duration::from_millis(400)).await;

        let disk_metrics = monitor.get_disk_metrics().await;
        assert!(disk_metrics.is_ok(), "Should get disk metrics successfully");

        let metrics = disk_metrics.unwrap();
        println!("Detected {} disk(s)", metrics.disks.len());

        // Verify each disk has valid metrics
        for (mount_point, disk_info) in &metrics.disks {
            println!("Disk {}: {} bytes total, {} bytes available ({:.1}% used)",
                mount_point,
                disk_info.total_bytes,
                disk_info.available_bytes,
                disk_info.usage_percent);

            assert!(disk_info.total_bytes > 0, "Total bytes should be positive for disk {}", mount_point);
            assert!(disk_info.available_bytes <= disk_info.total_bytes,
                "Available bytes should not exceed total for disk {}", mount_point);
            assert!(disk_info.usage_percent >= 0.0 && disk_info.usage_percent <= 100.0,
                "Usage percent should be 0-100% for disk {}: {}", mount_point, disk_info.usage_percent);

            // Verify used bytes calculation
            let calculated_used = disk_info.total_bytes.saturating_sub(disk_info.available_bytes);
            assert_eq!(disk_info.used_bytes, calculated_used,
                "Used bytes calculation should be consistent for disk {}", mount_point);
        }

        monitor.stop().await.expect("Disk monitor should stop");
        println!("Multiple disk detection and metrics test passed");
    }

    // Test 2: Aggregated availability calculation across disks
    {
        println!("Testing aggregated availability calculation across disks");

        let mut monitor = create_default_disk_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, DiskMonitoring};

        monitor.start().await.expect("Disk monitor should start");
        sleep(Duration::from_millis(300)).await;

        let availability = monitor.get_availability().await;
        assert!(availability.is_ok(), "Should get aggregated availability");

        let availability_value = availability.unwrap().0;
        let disk_metrics = monitor.get_disk_metrics().await.unwrap();

        // Manually calculate expected availability
        if !disk_metrics.disks.is_empty() {
            let avg_usage: f64 = disk_metrics.disks.values()
                .map(|d| d.usage_percent)
                .sum::<f64>() / disk_metrics.disks.len() as f64;
            let expected_availability = 100.0 - avg_usage;

            assert!((availability_value - expected_availability).abs() < 0.1,
                "Aggregated availability should match manual calculation: {} vs {}",
                availability_value, expected_availability);

            println!("Aggregated availability: {:.2}% (from {} disks)",
                availability_value, disk_metrics.disks.len());
        } else {
            // If no disks detected, availability should be 100%
            assert_eq!(availability_value, 100.0, "No disks should result in 100% availability");
        }

        monitor.stop().await.expect("Disk monitor should stop");
        println!("Aggregated availability calculation test passed");
    }

    // Test 3: Total disk space calculation across multiple disks
    {
        println!("Testing total disk space calculation across multiple disks");

        let mut monitor = create_default_disk_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, DiskMonitoring};

        monitor.start().await.expect("Disk monitor should start");
        sleep(Duration::from_millis(300)).await;

        let total_space = monitor.get_total_disk_space().await;
        assert!(total_space.is_ok(), "Should get total disk space");

        let available_space = monitor.get_available_disk_space().await;
        assert!(available_space.is_ok(), "Should get available disk space");

        let total_bytes = total_space.unwrap();
        let available_bytes = available_space.unwrap();

        println!("Total space across all disks: {} bytes", total_bytes);
        println!("Available space across all disks: {} bytes", available_bytes);

        assert!(available_bytes <= total_bytes,
            "Available space should not exceed total space: {} vs {}",
            available_bytes, total_bytes);

        // Verify against individual disk metrics
        let disk_metrics = monitor.get_disk_metrics().await.unwrap();
        let calculated_total: u64 = disk_metrics.disks.values()
            .map(|d| d.total_bytes)
            .sum();
        let calculated_available: u64 = disk_metrics.disks.values()
            .map(|d| d.available_bytes)
            .sum();

        assert_eq!(total_bytes, calculated_total,
            "Total space should match sum of individual disks");
        assert_eq!(available_bytes, calculated_available,
            "Available space should match sum of individual disks");

        monitor.stop().await.expect("Disk monitor should stop");
        println!("Total disk space calculation test passed");
    }

    // Test 4: Disk usage percentage across multiple disks
    {
        println!("Testing disk usage percentage across multiple disks");

        let mut monitor = create_default_disk_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, DiskMonitoring};

        monitor.start().await.expect("Disk monitor should start");
        sleep(Duration::from_millis(300)).await;

        let disk_usage = monitor.get_disk_usage().await;
        assert!(disk_usage.is_ok(), "Should get disk usage percentage");

        let usage_percent = disk_usage.unwrap();
        assert!(usage_percent >= 0.0 && usage_percent <= 100.0,
            "Disk usage should be 0-100%: {}", usage_percent);

        // Verify against individual disk metrics
        let disk_metrics = monitor.get_disk_metrics().await.unwrap();
        if !disk_metrics.disks.is_empty() {
            let calculated_avg_usage: f64 = disk_metrics.disks.values()
                .map(|d| d.usage_percent)
                .sum::<f64>() / disk_metrics.disks.len() as f64;

            assert!((usage_percent - calculated_avg_usage).abs() < 0.1,
                "Usage percentage should match calculated average: {} vs {}",
                usage_percent, calculated_avg_usage);

            println!("Average disk usage across {} disks: {:.2}%",
                disk_metrics.disks.len(), usage_percent);

            // Log individual disk usage
            for (mount_point, disk_info) in &disk_metrics.disks {
                println!("  Disk {}: {:.2}% used", mount_point, disk_info.usage_percent);
            }
        }

        monitor.stop().await.expect("Disk monitor should stop");
        println!("Disk usage percentage test passed");
    }

    println!("Disk availability across multiple disks test passed");
}

/// Test disk availability calculation with disk quotas
#[tokio::test]
async fn test_disk_availability_calculation_with_disk_quotas() {
    println!("Starting disk availability calculation with disk quotas test");

    // Test 1: Simulated quota-aware availability calculation
    {
        println!("Testing simulated quota-aware availability calculation");

        let mut monitor = create_default_disk_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, DiskMonitoring};

        monitor.start().await.expect("Disk monitor should start");
        sleep(Duration::from_millis(400)).await;

        let disk_metrics = monitor.get_disk_metrics().await;
        assert!(disk_metrics.is_ok(), "Should get disk metrics successfully");

        let metrics = disk_metrics.unwrap();
        let availability = monitor.get_availability().await.unwrap().0;

        println!("Current availability without quota consideration: {:.2}%", availability);

        // Simulate quota-aware calculations for each disk
        for (mount_point, disk_info) in &metrics.disks {
            // Simulate different quota scenarios
            let quota_scenarios = vec![
                ("No quota", None),
                ("50% quota", Some(disk_info.total_bytes / 2)),
                ("75% quota", Some(disk_info.total_bytes * 3 / 4)),
                ("90% quota", Some(disk_info.total_bytes * 9 / 10)),
            ];

            for (scenario_name, quota_limit) in quota_scenarios {
                let effective_availability = if let Some(quota) = quota_limit {
                    // Calculate availability considering quota
                    let quota_used = disk_info.used_bytes.min(quota);
                    let _quota_available = quota.saturating_sub(quota_used);
                    let quota_usage_percent = if quota > 0 {
                        (quota_used as f64 / quota as f64) * 100.0
                    } else {
                        100.0
                    };
                    100.0 - quota_usage_percent
                } else {
                    // No quota, use regular availability
                    100.0 - disk_info.usage_percent
                };

                println!("  Disk {}, {}: {:.2}% availability",
                    mount_point, scenario_name, effective_availability);

                assert!(effective_availability >= 0.0 && effective_availability <= 100.0,
                    "Quota-aware availability should be 0-100% for {}: {}",
                    scenario_name, effective_availability);
            }
        }

        monitor.stop().await.expect("Disk monitor should stop");
        println!("Simulated quota-aware availability calculation test passed");
    }

    // Test 2: Critical space threshold simulation (quota-like behavior)
    {
        println!("Testing critical space threshold simulation");

        let mut monitor = create_default_disk_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, DiskMonitoring};

        monitor.start().await.expect("Disk monitor should start");
        sleep(Duration::from_millis(300)).await;

        let disk_metrics = monitor.get_disk_metrics().await.unwrap();
        let base_availability = monitor.get_availability().await.unwrap().0;

        println!("Base availability: {:.2}%", base_availability);

        // Test different critical thresholds (simulating quota enforcement)
        let critical_thresholds = vec![10.0, 5.0, 2.0, 1.0]; // Percentage of total space

        for threshold_percent in critical_thresholds {
            println!("Testing critical threshold: {:.1}% of total space", threshold_percent);

            let mut adjusted_availability_sum = 0.0;
            let mut disk_count = 0;

            for (mount_point, disk_info) in &disk_metrics.disks {
                let threshold_bytes = (disk_info.total_bytes as f64 * threshold_percent / 100.0) as u64;
                let is_critical = disk_info.available_bytes < threshold_bytes;

                let adjusted_availability = if is_critical {
                    // Apply penalty for being below critical threshold
                    let penalty_factor = 0.5; // 50% penalty
                    (100.0 - disk_info.usage_percent) * penalty_factor
                } else {
                    100.0 - disk_info.usage_percent
                };

                println!("  Disk {}: {} critical, {:.2}% adjusted availability",
                    mount_point,
                    if is_critical { "IS" } else { "NOT" },
                    adjusted_availability);

                adjusted_availability_sum += adjusted_availability;
                disk_count += 1;
            }

            if disk_count > 0 {
                let avg_adjusted_availability = adjusted_availability_sum / disk_count as f64;
                println!("  Average adjusted availability: {:.2}%", avg_adjusted_availability);

                assert!(avg_adjusted_availability >= 0.0 && avg_adjusted_availability <= 100.0,
                    "Adjusted availability should be 0-100%: {}", avg_adjusted_availability);
            }
        }

        monitor.stop().await.expect("Disk monitor should stop");
        println!("Critical space threshold simulation test passed");
    }

    // Test 3: Quota enforcement impact on I/O availability
    {
        println!("Testing quota enforcement impact on I/O availability");

        let mut monitor = create_default_disk_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, DiskMonitoring};

        monitor.start().await.expect("Disk monitor should start");
        sleep(Duration::from_millis(400)).await;

        let disk_metrics = monitor.get_disk_metrics().await.unwrap();
        let disk_iops = monitor.get_disk_iops().await.unwrap();

        println!("Current total IOPS: {:?}", disk_iops);

        // Simulate quota impact on I/O operations
        for (mount_point, disk_info) in &disk_metrics.disks {
            let read_iops = disk_info.read_iops.unwrap_or(0.0);
            let write_iops = disk_info.write_iops.unwrap_or(0.0);
            let total_disk_iops = read_iops + write_iops;

            // Simulate quota-based I/O throttling scenarios
            let throttling_scenarios = vec![
                ("No throttling", 1.0),
                ("Light throttling", 0.8),
                ("Moderate throttling", 0.5),
                ("Heavy throttling", 0.2),
            ];

            for (scenario_name, throttle_factor) in throttling_scenarios {
                let throttled_iops = total_disk_iops * throttle_factor;
                let io_availability = if total_disk_iops > 0.0 {
                    // Higher IOPS means lower availability, throttling reduces effective IOPS
                    let max_expected_iops = 1000.0; // Assume 1000 IOPS as baseline
                    100.0 - (throttled_iops / max_expected_iops * 100.0).min(100.0)
                } else {
                    100.0 // No I/O activity means full availability
                };

                println!("  Disk {}, {}: {:.2} IOPS -> {:.2}% I/O availability",
                    mount_point, scenario_name, throttled_iops, io_availability);

                assert!(io_availability >= 0.0 && io_availability <= 100.0,
                    "I/O availability should be 0-100%: {}", io_availability);
            }
        }

        monitor.stop().await.expect("Disk monitor should stop");
        println!("Quota enforcement impact on I/O availability test passed");
    }

    // Test 4: Combined space and I/O quota simulation
    {
        println!("Testing combined space and I/O quota simulation");

        let mut monitor = create_default_disk_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, DiskMonitoring};

        monitor.start().await.expect("Disk monitor should start");
        sleep(Duration::from_millis(300)).await;

        let disk_metrics = monitor.get_disk_metrics().await.unwrap();
        let base_availability = monitor.get_availability().await.unwrap().0;

        println!("Base availability: {:.2}%", base_availability);

        // Simulate combined quota enforcement
        for (mount_point, disk_info) in &disk_metrics.disks {
            // Space quota simulation
            let space_quota = disk_info.total_bytes * 8 / 10; // 80% quota
            let space_used_against_quota = disk_info.used_bytes.min(space_quota);
            let space_quota_usage = if space_quota > 0 {
                (space_used_against_quota as f64 / space_quota as f64) * 100.0
            } else {
                100.0
            };
            let space_availability = 100.0 - space_quota_usage;

            // I/O quota simulation
            let current_iops = disk_info.read_iops.unwrap_or(0.0) + disk_info.write_iops.unwrap_or(0.0);
            let iops_quota = 500.0; // Simulated IOPS quota
            let iops_usage = (current_iops / iops_quota * 100.0).min(100.0);
            let io_availability = 100.0 - iops_usage;

            // Combined availability (weighted average)
            let space_weight = 0.7;
            let io_weight = 0.3;
            let combined_availability = (space_availability * space_weight) + (io_availability * io_weight);

            println!("  Disk {}: Space={:.2}%, I/O={:.2}%, Combined={:.2}% availability",
                mount_point, space_availability, io_availability, combined_availability);

            assert!(combined_availability >= 0.0 && combined_availability <= 100.0,
                "Combined availability should be 0-100%: {}", combined_availability);

            // Verify individual components are reasonable
            assert!(space_availability >= 0.0 && space_availability <= 100.0,
                "Space availability should be 0-100%: {}", space_availability);
            assert!(io_availability >= 0.0 && io_availability <= 100.0,
                "I/O availability should be 0-100%: {}", io_availability);
        }

        monitor.stop().await.expect("Disk monitor should stop");
        println!("Combined space and I/O quota simulation test passed");
    }

    println!("Disk availability calculation with disk quotas test passed");
}

// ================================================================================================
// Prisma Monitor Integration Tests
// ================================================================================================

/// Test integration between queue and task monitors
#[tokio::test]
async fn test_integration_between_queue_and_task_monitors() {
    println!("Starting integration between queue and task monitors test");

    // Test 1: Basic queue and task monitor coordination
    {
        println!("Testing basic queue and task monitor coordination");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::{
            QueueMonitor, TaskMonitor,
            types::{PrismaMonitorConfig, TaskMetrics, TaskStatus},
        };
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{QueueMonitoring, TaskMonitoring};
        use prisma_ai::prisma::prisma_engine::types::TaskId;
        use std::time::Instant;

        // Create monitors with shared configuration
        let config = PrismaMonitorConfig {
            queue_poll_interval_ms: 500,
            task_poll_interval_ms: 500,
            max_task_history: 100,
            enable_detailed_task_tracking: true,
        };

        let mut queue_monitor = QueueMonitor::new(config.clone());
        let mut task_monitor = TaskMonitor::new(config);

        // Start both monitors
        queue_monitor.start().await.expect("Queue monitor should start");
        task_monitor.start().await.expect("Task monitor should start");

        // Allow monitors to initialize
        tokio::time::sleep(std::time::Duration::from_millis(100)).await;

        // Simulate task lifecycle through queue and task monitors
        let task_id = TaskId::new();
        let queue_name = "test_queue";

        // 1. Record task creation in task monitor
        let task_metrics = TaskMetrics {
            task_id,
            category: TaskCategory::Internal,
            priority: TaskPriority::Normal,
            status: TaskStatus::Queued,
            created_at: Instant::now(),
            started_at: None,
            completed_at: None,
            queue_time: None,
            processing_time: None,
            queue_name: None,
            error_message: None,
        };

        task_monitor.record_task_created(task_metrics).await
            .expect("Should record task creation");

        // 2. Update queue metrics to reflect new task
        queue_monitor.update_queue_metrics(queue_name, 1).await
            .expect("Should update queue metrics");

        // 3. Record task starting in both monitors
        task_monitor.record_task_started(&task_id, queue_name).await
            .expect("Should record task started");

        // 4. Record task processing in queue monitor
        let processing_time = 150.0; // 150ms
        queue_monitor.record_task_processed(queue_name, processing_time, true).await
            .expect("Should record task processed");

        // 5. Record task completion in task monitor
        task_monitor.record_task_completed(&task_id, true, None).await
            .expect("Should record task completed");

        // 6. Update queue metrics to reflect task completion
        queue_monitor.update_queue_metrics(queue_name, 0).await
            .expect("Should update queue metrics after completion");

        // Verify coordination between monitors
        let queue_metrics = queue_monitor.get_metrics().await
            .expect("Should get queue metrics");
        let task_metrics = task_monitor.get_metrics().await
            .expect("Should get task metrics");

        // Verify queue metrics
        assert!(queue_metrics.queue_metrics.contains_key(queue_name),
            "Queue metrics should contain the test queue");
        let queue_info = &queue_metrics.queue_metrics[queue_name];
        assert_eq!(queue_info.length, 0, "Queue should be empty after task completion");
        assert_eq!(queue_info.tasks_processed, 1, "Queue should show 1 processed task");

        // Verify task metrics
        assert_eq!(task_metrics.total_tasks, 1, "Should have 1 total task");
        assert_eq!(task_metrics.successful_tasks, 1, "Should have 1 successful task");
        assert_eq!(task_metrics.failed_tasks, 0, "Should have 0 failed tasks");
        assert_eq!(task_metrics.completed_tasks.len(), 1, "Should have 1 completed task");

        // Verify task details
        let completed_task = &task_metrics.completed_tasks[0];
        assert_eq!(completed_task.task_id, task_id, "Task ID should match");
        assert_eq!(completed_task.status, TaskStatus::Completed, "Task should be completed");
        assert!(completed_task.queue_name.as_ref().unwrap() == queue_name,
            "Task should be associated with correct queue");

        // Stop monitors
        queue_monitor.stop().await.expect("Queue monitor should stop");
        task_monitor.stop().await.expect("Task monitor should stop");

        println!("Basic queue and task monitor coordination test passed");
    }

    // Test 2: Multiple tasks coordination
    {
        println!("Testing multiple tasks coordination between monitors");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::{
            QueueMonitor, TaskMonitor,
            types::{PrismaMonitorConfig, TaskMetrics, TaskStatus},
        };
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{QueueMonitoring, TaskMonitoring};
        use prisma_ai::prisma::prisma_engine::types::TaskId;
        use std::time::Instant;

        let config = PrismaMonitorConfig::default();
        let mut queue_monitor = QueueMonitor::new(config.clone());
        let mut task_monitor = TaskMonitor::new(config);

        queue_monitor.start().await.expect("Queue monitor should start");
        task_monitor.start().await.expect("Task monitor should start");

        tokio::time::sleep(std::time::Duration::from_millis(100)).await;

        // Create multiple tasks across different queues
        let task_ids: Vec<TaskId> = (0..5).map(|_| TaskId::new()).collect();
        let queue_names = vec!["queue_1", "queue_2", "queue_1", "queue_3", "queue_2"];

        // Record all tasks as created
        for (i, &task_id) in task_ids.iter().enumerate() {
            let task_metrics = TaskMetrics {
                task_id,
                category: TaskCategory::Internal,
                priority: TaskPriority::Normal,
                status: TaskStatus::Queued,
                created_at: Instant::now(),
                started_at: None,
                completed_at: None,
                queue_time: None,
                processing_time: None,
                queue_name: None,
                error_message: None,
            };

            task_monitor.record_task_created(task_metrics).await
                .expect(&format!("Should record task {} creation", i));
        }

        // Update queue metrics to reflect queued tasks
        let mut queue_lengths = std::collections::HashMap::new();
        for queue_name in &queue_names {
            *queue_lengths.entry(*queue_name).or_insert(0) += 1;
        }

        for (queue_name, length) in &queue_lengths {
            queue_monitor.update_queue_metrics(queue_name, *length).await
                .expect(&format!("Should update metrics for queue {}", queue_name));
        }

        // Process tasks one by one
        for (i, (&task_id, &queue_name)) in task_ids.iter().zip(queue_names.iter()).enumerate() {
            // Start task
            task_monitor.record_task_started(&task_id, queue_name).await
                .expect(&format!("Should start task {}", i));

            // Update queue length (one less task)
            let current_length = queue_lengths.get_mut(queue_name).unwrap();
            *current_length -= 1;
            queue_monitor.update_queue_metrics(queue_name, *current_length).await
                .expect(&format!("Should update queue {} length", queue_name));

            // Process and complete task
            let processing_time = 100.0 + (i as f64 * 50.0); // Variable processing times
            let success = i % 4 != 3; // Make every 4th task fail

            queue_monitor.record_task_processed(queue_name, processing_time, success).await
                .expect(&format!("Should record task {} processed", i));

            let error_message = if success { None } else { Some("Simulated failure".to_string()) };
            task_monitor.record_task_completed(&task_id, success, error_message).await
                .expect(&format!("Should complete task {}", i));

            // Brief delay between tasks
            tokio::time::sleep(std::time::Duration::from_millis(10)).await;
        }

        // Verify final state
        let queue_metrics = queue_monitor.get_metrics().await
            .expect("Should get final queue metrics");
        let task_metrics = task_monitor.get_metrics().await
            .expect("Should get final task metrics");

        // All queues should be empty
        for (queue_name, queue_info) in &queue_metrics.queue_metrics {
            assert_eq!(queue_info.length, 0, "Queue {} should be empty", queue_name);
        }

        // Verify task counts
        assert_eq!(task_metrics.total_tasks, 5, "Should have 5 total tasks");
        assert_eq!(task_metrics.successful_tasks, 4, "Should have 4 successful tasks");
        assert_eq!(task_metrics.failed_tasks, 1, "Should have 1 failed task");
        assert_eq!(task_metrics.completed_tasks.len(), 5, "Should have 5 completed tasks");

        // Verify queue processing counts
        let total_processed: usize = queue_metrics.queue_metrics.values()
            .map(|q| q.tasks_processed)
            .sum();
        assert_eq!(total_processed, 5, "Total processed tasks should be 5");

        queue_monitor.stop().await.expect("Queue monitor should stop");
        task_monitor.stop().await.expect("Task monitor should stop");

        println!("Multiple tasks coordination test passed");
    }

    println!("Integration between queue and task monitors test passed");
}

/// Test metric correlation between different Prisma monitors
#[tokio::test]
async fn test_metric_correlation_between_different_prisma_monitors() {
    println!("Starting metric correlation between different Prisma monitors test");

    // Test 1: Queue length and task processing correlation
    {
        println!("Testing queue length and task processing correlation");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::{
            QueueMonitor, TaskMonitor,
            types::{PrismaMonitorConfig, TaskMetrics, TaskStatus},
        };
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{QueueMonitoring, TaskMonitoring};
        use prisma_ai::prisma::prisma_engine::types::TaskId;
        use std::time::Instant;

        let config = PrismaMonitorConfig {
            queue_poll_interval_ms: 200,
            task_poll_interval_ms: 200,
            max_task_history: 50,
            enable_detailed_task_tracking: true,
        };

        let mut queue_monitor = QueueMonitor::new(config.clone());
        let mut task_monitor = TaskMonitor::new(config);

        queue_monitor.start().await.expect("Queue monitor should start");
        task_monitor.start().await.expect("Task monitor should start");

        tokio::time::sleep(std::time::Duration::from_millis(100)).await;

        let queue_name = "correlation_queue";
        let mut correlation_data = Vec::new();

        // Simulate varying queue loads and measure correlation
        for batch in 0..3 {
            let batch_size = (batch + 1) * 2; // 2, 4, 6 tasks per batch
            println!("Processing batch {} with {} tasks", batch + 1, batch_size);

            let mut batch_task_ids = Vec::new();

            // Add tasks to queue
            for i in 0..batch_size {
                let task_id = TaskId::new();
                batch_task_ids.push(task_id);

                let task_metrics = TaskMetrics {
                    task_id,
                    category: TaskCategory::Internal,
                    priority: TaskPriority::Normal,
                    status: TaskStatus::Queued,
                    created_at: Instant::now(),
                    started_at: None,
                    completed_at: None,
                    queue_time: None,
                    processing_time: None,
                    queue_name: None,
                    error_message: None,
                };

                task_monitor.record_task_created(task_metrics).await
                    .expect(&format!("Should create task {} in batch {}", i, batch));
            }

            // Update queue length
            queue_monitor.update_queue_metrics(queue_name, batch_size).await
                .expect("Should update queue length");

            // Record initial correlation point
            let queue_metrics = queue_monitor.get_metrics().await.unwrap();
            let task_metrics = task_monitor.get_metrics().await.unwrap();

            correlation_data.push((
                queue_metrics.queue_metrics.get(queue_name).map(|q| q.length).unwrap_or(0),
                task_metrics.active_tasks.len(),
                task_metrics.total_tasks,
            ));

            // Process tasks one by one
            for (i, task_id) in batch_task_ids.iter().enumerate() {
                // Start task
                task_monitor.record_task_started(task_id, queue_name).await
                    .expect("Should start task");

                // Update queue length (one less)
                let remaining = batch_size - i - 1;
                queue_monitor.update_queue_metrics(queue_name, remaining).await
                    .expect("Should update queue length");

                // Process task
                let processing_time = 50.0 + (i as f64 * 10.0);
                queue_monitor.record_task_processed(queue_name, processing_time, true).await
                    .expect("Should process task");

                // Complete task
                task_monitor.record_task_completed(task_id, true, None).await
                    .expect("Should complete task");

                // Record correlation point
                let queue_metrics = queue_monitor.get_metrics().await.unwrap();
                let task_metrics = task_monitor.get_metrics().await.unwrap();

                correlation_data.push((
                    queue_metrics.queue_metrics.get(queue_name).map(|q| q.length).unwrap_or(0),
                    task_metrics.active_tasks.len(),
                    task_metrics.total_tasks,
                ));

                tokio::time::sleep(std::time::Duration::from_millis(20)).await;
            }
        }

        // Analyze correlation
        println!("Correlation data points: {}", correlation_data.len());
        for (i, (queue_len, active_tasks, total_tasks)) in correlation_data.iter().enumerate() {
            println!("Point {}: Queue={}, Active={}, Total={}", i, queue_len, active_tasks, total_tasks);

            // Verify correlation: active tasks should never exceed queue length + processing tasks
            assert!(*active_tasks <= *queue_len + 10, // Allow some tolerance for processing
                "Active tasks ({}) should not significantly exceed queue length ({})", active_tasks, queue_len);
        }

        // Verify final state
        let final_queue_metrics = queue_monitor.get_metrics().await.unwrap();
        let final_task_metrics = task_monitor.get_metrics().await.unwrap();

        assert_eq!(final_queue_metrics.queue_metrics.get(queue_name).unwrap().length, 0,
            "Queue should be empty at the end");
        assert_eq!(final_task_metrics.active_tasks.len(), 0,
            "No active tasks should remain");
        assert_eq!(final_task_metrics.total_tasks, 12, // 2+4+6
            "Total tasks should match expected count");

        queue_monitor.stop().await.expect("Queue monitor should stop");
        task_monitor.stop().await.expect("Task monitor should stop");

        println!("Queue length and task processing correlation test passed");
    }

    // Test 2: Processing time correlation between monitors
    {
        println!("Testing processing time correlation between monitors");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::{
            QueueMonitor, TaskMonitor,
            types::{PrismaMonitorConfig, TaskMetrics, TaskStatus},
        };
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{QueueMonitoring, TaskMonitoring};
        use prisma_ai::prisma::prisma_engine::types::TaskId;
        use std::time::Instant;

        let config = PrismaMonitorConfig::default();
        let mut queue_monitor = QueueMonitor::new(config.clone());
        let mut task_monitor = TaskMonitor::new(config);

        queue_monitor.start().await.expect("Queue monitor should start");
        task_monitor.start().await.expect("Task monitor should start");

        tokio::time::sleep(std::time::Duration::from_millis(100)).await;

        let queue_name = "timing_queue";
        let processing_times = vec![100.0, 200.0, 150.0, 300.0, 75.0];
        let mut task_ids = Vec::new();

        // Create and process tasks with known processing times
        for (i, &expected_time) in processing_times.iter().enumerate() {
            let task_id = TaskId::new();
            task_ids.push(task_id);

            // Create task
            let task_metrics = TaskMetrics {
                task_id,
                category: TaskCategory::Internal,
                priority: TaskPriority::Normal,
                status: TaskStatus::Queued,
                created_at: Instant::now(),
                started_at: None,
                completed_at: None,
                queue_time: None,
                processing_time: None,
                queue_name: None,
                error_message: None,
            };

            task_monitor.record_task_created(task_metrics).await
                .expect("Should create task");

            queue_monitor.update_queue_metrics(queue_name, 1).await
                .expect("Should update queue");

            // Start and process task
            task_monitor.record_task_started(&task_id, queue_name).await
                .expect("Should start task");

            queue_monitor.record_task_processed(queue_name, expected_time, true).await
                .expect("Should process task");

            task_monitor.record_task_completed(&task_id, true, None).await
                .expect("Should complete task");

            queue_monitor.update_queue_metrics(queue_name, 0).await
                .expect("Should update queue");

            println!("Processed task {} with expected time {}ms", i, expected_time);
        }

        // Verify processing time correlation
        let queue_metrics = queue_monitor.get_metrics().await.unwrap();
        let task_metrics = task_monitor.get_metrics().await.unwrap();

        let queue_info = queue_metrics.queue_metrics.get(queue_name).unwrap();
        let avg_queue_time = queue_info.avg_processing_time_ms;
        let avg_task_time = task_metrics.avg_processing_time_ms;

        println!("Average processing time - Queue: {:.2}ms, Task: {:.2}ms",
            avg_queue_time, avg_task_time);

        // The averages should be similar (within reasonable tolerance)
        let expected_avg = processing_times.iter().sum::<f64>() / processing_times.len() as f64;
        println!("Expected average: {:.2}ms", expected_avg);

        // Allow some tolerance for timing variations
        let tolerance = 50.0; // 50ms tolerance
        assert!((avg_queue_time - expected_avg).abs() < tolerance,
            "Queue average time should be close to expected: {:.2} vs {:.2}", avg_queue_time, expected_avg);

        queue_monitor.stop().await.expect("Queue monitor should stop");
        task_monitor.stop().await.expect("Task monitor should stop");

        println!("Processing time correlation test passed");
    }

    println!("Metric correlation between different Prisma monitors test passed");
}

/// Test monitor coordination and synchronization
#[tokio::test]
async fn test_monitor_coordination_and_synchronization() {
    println!("Starting monitor coordination and synchronization test");

    // Test 1: Concurrent access to shared metrics
    {
        println!("Testing concurrent access to shared metrics");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::{
            QueueMonitor, TaskMonitor,
            types::{PrismaMonitorConfig, TaskMetrics, TaskStatus},
        };
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{QueueMonitoring, TaskMonitoring};
        use prisma_ai::prisma::prisma_engine::types::TaskId;
        use std::time::Instant;
        use std::sync::Arc;
        use tokio::sync::Mutex;

        let config = PrismaMonitorConfig {
            queue_poll_interval_ms: 100,
            task_poll_interval_ms: 100,
            max_task_history: 200,
            enable_detailed_task_tracking: true,
        };

        let queue_monitor = Arc::new(Mutex::new(QueueMonitor::new(config.clone())));
        let task_monitor = Arc::new(Mutex::new(TaskMonitor::new(config)));

        // Start monitors
        {
            let mut qm = queue_monitor.lock().await;
            let mut tm = task_monitor.lock().await;
            qm.start().await.expect("Queue monitor should start");
            tm.start().await.expect("Task monitor should start");
        }

        tokio::time::sleep(std::time::Duration::from_millis(100)).await;

        // Spawn concurrent tasks that access both monitors
        let mut handles = Vec::new();
        let num_concurrent_tasks = 10;

        for i in 0..num_concurrent_tasks {
            let qm_clone = Arc::clone(&queue_monitor);
            let tm_clone = Arc::clone(&task_monitor);
            let queue_name = format!("queue_{}", i % 3); // Use 3 different queues

            let handle = tokio::spawn(async move {
                let task_id = TaskId::new();

                // Create task in task monitor
                {
                    let mut tm = tm_clone.lock().await;
                    let task_metrics = TaskMetrics {
                        task_id,
                        category: TaskCategory::Internal,
                        priority: TaskPriority::Normal,
                        status: TaskStatus::Queued,
                        created_at: Instant::now(),
                        started_at: None,
                        completed_at: None,
                        queue_time: None,
                        processing_time: None,
                        queue_name: None,
                        error_message: None,
                    };

                    tm.record_task_created(task_metrics).await
                        .expect("Should create task");
                }

                // Update queue metrics
                {
                    let mut qm = qm_clone.lock().await;
                    qm.update_queue_metrics(&queue_name, 1).await
                        .expect("Should update queue metrics");
                }

                // Start task
                {
                    let mut tm = tm_clone.lock().await;
                    tm.record_task_started(&task_id, &queue_name).await
                        .expect("Should start task");
                }

                // Process task
                {
                    let mut qm = qm_clone.lock().await;
                    let processing_time = 50.0 + (i as f64 * 5.0);
                    qm.record_task_processed(&queue_name, processing_time, true).await
                        .expect("Should process task");
                }

                // Complete task
                {
                    let mut tm = tm_clone.lock().await;
                    tm.record_task_completed(&task_id, true, None).await
                        .expect("Should complete task");
                }

                // Update queue (task completed)
                {
                    let mut qm = qm_clone.lock().await;
                    qm.update_queue_metrics(&queue_name, 0).await
                        .expect("Should update queue after completion");
                }

                task_id
            });

            handles.push(handle);
        }

        // Wait for all concurrent tasks to complete
        let _completed_task_ids: Vec<TaskId> = futures::future::join_all(handles).await
            .into_iter()
            .map(|result| result.expect("Task should complete successfully"))
            .collect();

        // Verify final state consistency
        let final_queue_metrics = {
            let qm = queue_monitor.lock().await;
            qm.get_metrics().await.expect("Should get queue metrics")
        };

        let final_task_metrics = {
            let tm = task_monitor.lock().await;
            tm.get_metrics().await.expect("Should get task metrics")
        };

        // Verify all tasks were processed
        assert_eq!(final_task_metrics.total_tasks, num_concurrent_tasks,
            "Should have processed all {} tasks", num_concurrent_tasks);
        assert_eq!(final_task_metrics.successful_tasks, num_concurrent_tasks,
            "All tasks should be successful");
        assert_eq!(final_task_metrics.failed_tasks, 0,
            "No tasks should have failed");

        // Verify all queues are empty
        for (queue_name, queue_info) in &final_queue_metrics.queue_metrics {
            assert_eq!(queue_info.length, 0,
                "Queue {} should be empty", queue_name);
        }

        // Verify total processed count
        let total_processed: usize = final_queue_metrics.queue_metrics.values()
            .map(|q| q.tasks_processed)
            .sum();
        assert_eq!(total_processed, num_concurrent_tasks,
            "Total processed should match concurrent tasks");

        // Stop monitors
        {
            let mut qm = queue_monitor.lock().await;
            let mut tm = task_monitor.lock().await;
            qm.stop().await.expect("Queue monitor should stop");
            tm.stop().await.expect("Task monitor should stop");
        }

        println!("Concurrent access to shared metrics test passed");
    }

    // Test 2: Monitor lifecycle coordination (start/stop sequences)
    {
        println!("Testing monitor lifecycle coordination");

        use prisma_ai::prisma::prisma_engine::monitor::Monitor;
        use prisma_ai::prisma::prisma_engine::monitor::types::MonitorConfig;
        use prisma_ai::prisma::prisma_engine::ResourceMonitor;
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::Monitorable;

        // Create integrated monitor that coordinates both queue and task monitors
        let monitor_config = MonitorConfig {
            poll_interval_ms: 1000,
        };

        let mut monitor = Monitor::new(monitor_config);

        // Test multiple start/stop cycles
        for cycle in 1..=3 {
            println!("Monitor lifecycle cycle {}", cycle);

            // Start monitor (should start all sub-monitors)
            monitor.start().await.expect("Monitor should start");

            // Allow time for initialization
            tokio::time::sleep(std::time::Duration::from_millis(200)).await;

            // Verify monitor is running by checking status
            let status = monitor.get_status().await.expect("Should get monitor status");
            println!("Monitor status in cycle {}: {}", cycle, status);
            assert!(status.contains("Running") || status.contains("Stopped"),
                "Status should indicate monitor state");

            // Perform some operations to verify monitors are working
            let task_id = TaskId::new();
            let queue_name = "lifecycle_test_queue";

            // These operations should work when monitor is running
            monitor.record_task_created(TaskMetrics {
                task_id,
                category: TaskCategory::Internal,
                priority: TaskPriority::Normal,
                status: TaskStatus::Queued,
                created_at: Instant::now(),
                started_at: None,
                completed_at: None,
                queue_time: None,
                processing_time: None,
                queue_name: None,
                error_message: None,
            }).await.expect("Should record task creation");

            monitor.update_queue_metrics(queue_name, 1).await
                .expect("Should update queue metrics");

            monitor.record_task_started(&task_id, queue_name).await
                .expect("Should record task started");

            monitor.record_task_processed(queue_name, 100.0, true).await
                .expect("Should record task processed");

            monitor.record_task_completed(&task_id, true, None).await
                .expect("Should record task completed");

            monitor.update_queue_metrics(queue_name, 0).await
                .expect("Should update queue metrics");

            // Stop monitor (should stop all sub-monitors)
            monitor.stop().await.expect("Monitor should stop");

            // Brief pause between cycles
            tokio::time::sleep(std::time::Duration::from_millis(100)).await;
        }

        println!("Monitor lifecycle coordination test passed");
    }

    println!("Monitor coordination and synchronization test passed");
}

/// Test error propagation between Prisma monitors
#[tokio::test]
async fn test_error_propagation_between_prisma_monitors() {
    println!("Starting error propagation between Prisma monitors test");

    // Test 1: Error handling when one monitor fails
    {
        println!("Testing error handling when one monitor fails");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::{
            QueueMonitor, TaskMonitor,
            types::{PrismaMonitorConfig, TaskMetrics, TaskStatus},
        };
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{QueueMonitoring, TaskMonitoring};
        use prisma_ai::prisma::prisma_engine::types::TaskId;
        use std::time::Instant;

        let config = PrismaMonitorConfig::default();
        let mut queue_monitor = QueueMonitor::new(config.clone());
        let mut task_monitor = TaskMonitor::new(config);

        queue_monitor.start().await.expect("Queue monitor should start");
        task_monitor.start().await.expect("Task monitor should start");

        tokio::time::sleep(std::time::Duration::from_millis(100)).await;

        // Test error scenarios
        let task_id = TaskId::new();
        let queue_name = "error_test_queue";

        // 1. Test duplicate task creation error
        {
            println!("Testing duplicate task creation error");

            let task_metrics = TaskMetrics {
                task_id,
                category: TaskCategory::Internal,
                priority: TaskPriority::Normal,
                status: TaskStatus::Queued,
                created_at: Instant::now(),
                started_at: None,
                completed_at: None,
                queue_time: None,
                processing_time: None,
                queue_name: None,
                error_message: None,
            };

            // First creation should succeed
            let result1 = task_monitor.record_task_created(task_metrics.clone()).await;
            assert!(result1.is_ok(), "First task creation should succeed");

            // Second creation with same ID should fail
            let result2 = task_monitor.record_task_created(task_metrics).await;
            assert!(result2.is_err(), "Duplicate task creation should fail");

            if let Err(error) = result2 {
                println!("Expected duplicate task error: {:?}", error);
                // Verify error contains expected information
                let error_string = format!("{:?}", error);
                assert!(error_string.contains("already exists") || error_string.contains("OperationFailed"),
                    "Error should indicate duplicate task: {}", error_string);
            }
        }

        // 2. Test operations on non-existent tasks
        {
            println!("Testing operations on non-existent tasks");

            let non_existent_task_id = TaskId::new();

            // Try to start a non-existent task
            let result = task_monitor.record_task_started(&non_existent_task_id, queue_name).await;
            assert!(result.is_err(), "Starting non-existent task should fail");

            if let Err(error) = result {
                println!("Expected non-existent task error: {:?}", error);
                let error_string = format!("{:?}", error);
                assert!(error_string.contains("not found") || error_string.contains("ResourceNotAvailable"),
                    "Error should indicate task not found: {}", error_string);
            }

            // Try to complete a non-existent task
            let result = task_monitor.record_task_completed(&non_existent_task_id, true, None).await;
            assert!(result.is_err(), "Completing non-existent task should fail");
        }

        // 3. Test task failure propagation
        {
            println!("Testing task failure propagation");

            let failed_task_id = TaskId::new();
            let error_message = "Simulated task failure for testing";

            // Create and start task
            let task_metrics = TaskMetrics {
                task_id: failed_task_id,
                category: TaskCategory::Internal,
                priority: TaskPriority::Normal,
                status: TaskStatus::Queued,
                created_at: Instant::now(),
                started_at: None,
                completed_at: None,
                queue_time: None,
                processing_time: None,
                queue_name: None,
                error_message: None,
            };

            task_monitor.record_task_created(task_metrics).await
                .expect("Should create failed task");

            queue_monitor.update_queue_metrics(queue_name, 1).await
                .expect("Should update queue for failed task");

            task_monitor.record_task_started(&failed_task_id, queue_name).await
                .expect("Should start failed task");

            // Record failure in both monitors
            queue_monitor.record_task_processed(queue_name, 100.0, false).await
                .expect("Should record failed task processing");

            task_monitor.record_task_completed(&failed_task_id, false, Some(error_message.to_string())).await
                .expect("Should record task failure");

            queue_monitor.update_queue_metrics(queue_name, 0).await
                .expect("Should update queue after failure");

            // Verify failure is recorded correctly
            let queue_metrics = queue_monitor.get_metrics().await.unwrap();
            let task_metrics = task_monitor.get_metrics().await.unwrap();

            let queue_info = queue_metrics.queue_metrics.get(queue_name).unwrap();
            assert_eq!(queue_info.tasks_failed, 1, "Queue should show 1 failed task");

            assert_eq!(task_metrics.failed_tasks, 1, "Task monitor should show 1 failed task");
            assert_eq!(task_metrics.completed_tasks.len(), 1, "Should have 1 completed task (the failed one)");

            // Find the failed task and verify error message
            let failed_task = task_metrics.completed_tasks.iter()
                .find(|t| t.task_id == failed_task_id)
                .expect("Should find failed task");

            assert_eq!(failed_task.status, TaskStatus::Failed, "Task should be marked as failed");
            assert!(failed_task.error_message.is_some(), "Failed task should have error message");
            assert_eq!(failed_task.error_message.as_ref().unwrap(), error_message,
                "Error message should match");
        }

        queue_monitor.stop().await.expect("Queue monitor should stop");
        task_monitor.stop().await.expect("Task monitor should stop");

        println!("Error handling when one monitor fails test passed");
    }

    // Test 2: Error recovery and resilience
    {
        println!("Testing error recovery and resilience");

        use prisma_ai::prisma::prisma_engine::monitor::Monitor;
        use prisma_ai::prisma::prisma_engine::monitor::types::MonitorConfig;
        use prisma_ai::prisma::prisma_engine::ResourceMonitor;
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::Monitorable;

        let monitor_config = MonitorConfig {
            poll_interval_ms: 1000,
        };

        let mut monitor = Monitor::new(monitor_config);
        monitor.start().await.expect("Monitor should start");

        tokio::time::sleep(std::time::Duration::from_millis(100)).await;

        // Test recovery after errors
        let queue_name = "recovery_test_queue";
        let mut successful_operations = 0;
        let mut failed_operations = 0;

        // Mix successful and failing operations
        for i in 0..10 {
            let task_id = TaskId::new();

            // Create task
            let create_result = monitor.record_task_created(TaskMetrics {
                task_id,
                category: TaskCategory::Internal,
                priority: TaskPriority::Normal,
                status: TaskStatus::Queued,
                created_at: Instant::now(),
                started_at: None,
                completed_at: None,
                queue_time: None,
                processing_time: None,
                queue_name: None,
                error_message: None,
            }).await;

            if create_result.is_ok() {
                successful_operations += 1;

                // Continue with successful task
                monitor.update_queue_metrics(queue_name, 1).await
                    .expect("Should update queue");

                monitor.record_task_started(&task_id, queue_name).await
                    .expect("Should start task");

                // Try to create duplicate WHILE task is still active (should fail)
                let duplicate_result = monitor.record_task_created(TaskMetrics {
                    task_id, // Same ID
                    category: TaskCategory::Internal,
                    priority: TaskPriority::Normal,
                    status: TaskStatus::Queued,
                    created_at: Instant::now(),
                    started_at: None,
                    completed_at: None,
                    queue_time: None,
                    processing_time: None,
                    queue_name: None,
                    error_message: None,
                }).await;

                // This should fail because the task is still active
                if duplicate_result.is_err() {
                    println!("Expected duplicate error for active task {}: {:?}", i, duplicate_result.err());
                } else {
                    println!("Warning: Duplicate creation succeeded for task {} (unexpected)", i);
                }

                let success = i % 3 != 0; // Make every 3rd task fail
                let processing_time = 50.0 + (i as f64 * 10.0);

                monitor.record_task_processed(queue_name, processing_time, success).await
                    .expect("Should process task");

                let error_msg = if success { None } else { Some("Planned failure".to_string()) };
                monitor.record_task_completed(&task_id, success, error_msg).await
                    .expect("Should complete task");

                monitor.update_queue_metrics(queue_name, 0).await
                    .expect("Should update queue");

                if !success {
                    failed_operations += 1;
                }
            } else {
                failed_operations += 1;
                println!("Expected error in operation {}: {:?}", i, create_result.err());
            }

            tokio::time::sleep(std::time::Duration::from_millis(10)).await;
        }

        println!("Successful operations: {}, Failed operations: {}", successful_operations, failed_operations);
        assert!(successful_operations > 0, "Should have some successful operations");
        assert!(failed_operations > 0, "Should have some failed operations for testing");

        // Verify monitor is still functional after errors
        let final_status = monitor.get_status().await.expect("Should get final status");
        println!("Final monitor status: {}", final_status);

        monitor.stop().await.expect("Monitor should stop");

        println!("Error recovery and resilience test passed");
    }

    println!("Error propagation between Prisma monitors test passed");
}

// ================================================================================================
// Prisma Monitor Configuration Tests
// ================================================================================================

/// Test PrismaMonitorConfig creation and validation
#[tokio::test]
async fn test_prisma_monitor_config_creation_and_validation() {
    println!("Starting PrismaMonitorConfig creation and validation test");

    // Test 1: Default configuration creation
    {
        println!("Testing PrismaMonitorConfig default creation");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::types::PrismaMonitorConfig;

        let default_config = PrismaMonitorConfig::default();

        // Verify default values
        assert_eq!(default_config.queue_poll_interval_ms, 1000, "Default queue poll interval should be 1000ms");
        assert_eq!(default_config.task_poll_interval_ms, 1000, "Default task poll interval should be 1000ms");
        assert_eq!(default_config.max_task_history, 1000, "Default max task history should be 1000");
        assert_eq!(default_config.enable_detailed_task_tracking, true, "Default detailed tracking should be enabled");

        println!("PrismaMonitorConfig default creation test passed");
    }

    // Test 2: Custom configuration creation with valid parameters
    {
        println!("Testing PrismaMonitorConfig custom creation with valid parameters");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::types::PrismaMonitorConfig;

        let custom_configs = vec![
            (500, 750, 500, true, "Fast polling with detailed tracking"),
            (2000, 3000, 2000, false, "Slow polling without detailed tracking"),
            (1, 1, 1, true, "Minimum values with tracking"),
            (60000, 120000, 10000, false, "High values without tracking"),
        ];

        for (i, (queue_interval, task_interval, max_history, detailed_tracking, description)) in custom_configs.into_iter().enumerate() {
            println!("Testing custom config {}: {}", i + 1, description);

            let config = PrismaMonitorConfig {
                queue_poll_interval_ms: queue_interval,
                task_poll_interval_ms: task_interval,
                max_task_history: max_history,
                enable_detailed_task_tracking: detailed_tracking,
            };

            // Verify all fields are set correctly
            assert_eq!(config.queue_poll_interval_ms, queue_interval,
                "Queue poll interval should match for config {}", i + 1);
            assert_eq!(config.task_poll_interval_ms, task_interval,
                "Task poll interval should match for config {}", i + 1);
            assert_eq!(config.max_task_history, max_history,
                "Max task history should match for config {}", i + 1);
            assert_eq!(config.enable_detailed_task_tracking, detailed_tracking,
                "Detailed tracking should match for config {}", i + 1);

            println!("Custom config {} ({}) passed", i + 1, description);
        }

        println!("PrismaMonitorConfig custom creation test passed");
    }

    // Test 3: Edge case and boundary value testing
    {
        println!("Testing PrismaMonitorConfig edge cases and boundary values");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::types::PrismaMonitorConfig;

        let edge_cases = vec![
            (0, 0, 0, true, "Zero values"),
            (u64::MAX, u64::MAX, usize::MAX, false, "Maximum values"),
            (1, u64::MAX, 1, true, "Mixed extreme values"),
            (u64::MAX, 1, usize::MAX, false, "Reversed extreme values"),
        ];

        for (i, (queue_interval, task_interval, max_history, detailed_tracking, description)) in edge_cases.into_iter().enumerate() {
            println!("Testing edge case {}: {}", i + 1, description);

            let config = PrismaMonitorConfig {
                queue_poll_interval_ms: queue_interval,
                task_poll_interval_ms: task_interval,
                max_task_history: max_history,
                enable_detailed_task_tracking: detailed_tracking,
            };

            // Verify configuration can be created with edge values
            assert_eq!(config.queue_poll_interval_ms, queue_interval,
                "Queue poll interval should handle edge case {}", i + 1);
            assert_eq!(config.task_poll_interval_ms, task_interval,
                "Task poll interval should handle edge case {}", i + 1);
            assert_eq!(config.max_task_history, max_history,
                "Max task history should handle edge case {}", i + 1);
            assert_eq!(config.enable_detailed_task_tracking, detailed_tracking,
                "Detailed tracking should handle edge case {}", i + 1);

            println!("Edge case {} ({}) passed", i + 1, description);
        }

        println!("PrismaMonitorConfig edge cases and boundary values test passed");
    }

    // Test 4: Configuration field validation logic
    {
        println!("Testing PrismaMonitorConfig field validation logic");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::types::PrismaMonitorConfig;

        // Test reasonable configuration ranges
        let reasonable_configs = vec![
            (100, 100, 100, true, "Minimum reasonable values"),
            (5000, 5000, 5000, false, "Standard values"),
            (30000, 30000, 50000, true, "High but reasonable values"),
        ];

        for (i, (queue_interval, task_interval, max_history, detailed_tracking, description)) in reasonable_configs.into_iter().enumerate() {
            println!("Testing reasonable config {}: {}", i + 1, description);

            let config = PrismaMonitorConfig {
                queue_poll_interval_ms: queue_interval,
                task_poll_interval_ms: task_interval,
                max_task_history: max_history,
                enable_detailed_task_tracking: detailed_tracking,
            };

            // Verify reasonable values are within expected ranges
            assert!(config.queue_poll_interval_ms >= 100 && config.queue_poll_interval_ms <= 30000,
                "Queue poll interval should be reasonable for config {}: {}", i + 1, config.queue_poll_interval_ms);
            assert!(config.task_poll_interval_ms >= 100 && config.task_poll_interval_ms <= 30000,
                "Task poll interval should be reasonable for config {}: {}", i + 1, config.task_poll_interval_ms);
            assert!(config.max_task_history >= 100 && config.max_task_history <= 50000,
                "Max task history should be reasonable for config {}: {}", i + 1, config.max_task_history);

            println!("Reasonable config {} ({}) passed", i + 1, description);
        }

        println!("PrismaMonitorConfig field validation logic test passed");
    }

    println!("PrismaMonitorConfig creation and validation test passed");
}

/// Test configuration parameter validation
#[tokio::test]
async fn test_configuration_parameter_validation() {
    println!("Starting configuration parameter validation test");

    // Test 1: Valid parameter ranges for poll intervals
    {
        println!("Testing valid parameter ranges for poll intervals");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::types::PrismaMonitorConfig;

        let valid_intervals = vec![
            (1, "Minimum interval"),
            (100, "Very fast interval"),
            (1000, "Standard interval"),
            (5000, "Moderate interval"),
            (30000, "Slow interval"),
            (60000, "Very slow interval"),
        ];

        for (interval, description) in valid_intervals {
            println!("Testing interval validation: {} ({}ms)", description, interval);

            let config = PrismaMonitorConfig {
                queue_poll_interval_ms: interval,
                task_poll_interval_ms: interval,
                max_task_history: 1000,
                enable_detailed_task_tracking: true,
            };

            // Verify intervals are preserved
            assert_eq!(config.queue_poll_interval_ms, interval,
                "Queue poll interval should be preserved for {}", description);
            assert_eq!(config.task_poll_interval_ms, interval,
                "Task poll interval should be preserved for {}", description);

            // Test that intervals can be used to create Duration objects
            use std::time::Duration;
            let queue_duration = Duration::from_millis(config.queue_poll_interval_ms);
            let task_duration = Duration::from_millis(config.task_poll_interval_ms);

            assert_eq!(queue_duration.as_millis(), interval as u128,
                "Queue duration should match interval for {}", description);
            assert_eq!(task_duration.as_millis(), interval as u128,
                "Task duration should match interval for {}", description);

            println!("Interval validation for {} passed", description);
        }

        println!("Valid parameter ranges for poll intervals test passed");
    }

    // Test 2: Max task history bounds testing
    {
        println!("Testing max task history bounds");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::types::PrismaMonitorConfig;

        let history_bounds = vec![
            (0, "Zero history"),
            (1, "Minimum history"),
            (10, "Small history"),
            (100, "Small reasonable history"),
            (1000, "Standard history"),
            (10000, "Large history"),
            (100000, "Very large history"),
            (usize::MAX, "Maximum possible history"),
        ];

        for (history_size, description) in history_bounds {
            println!("Testing history bounds: {} ({})", description, history_size);

            let config = PrismaMonitorConfig {
                queue_poll_interval_ms: 1000,
                task_poll_interval_ms: 1000,
                max_task_history: history_size,
                enable_detailed_task_tracking: true,
            };

            // Verify history size is preserved
            assert_eq!(config.max_task_history, history_size,
                "Max task history should be preserved for {}", description);

            // Verify the value can be used in practical scenarios
            if history_size > 0 && history_size < 1000000 {
                // Test that we can create a vector with this capacity (for reasonable sizes)
                let _test_vec: Vec<u32> = Vec::with_capacity(config.max_task_history);
            }

            println!("History bounds for {} passed", description);
        }

        println!("Max task history bounds test passed");
    }

    // Test 3: Enable detailed task tracking boolean validation
    {
        println!("Testing enable detailed task tracking boolean validation");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::types::PrismaMonitorConfig;

        let tracking_options = vec![
            (true, "Detailed tracking enabled"),
            (false, "Detailed tracking disabled"),
        ];

        for (tracking_enabled, description) in tracking_options {
            println!("Testing tracking option: {}", description);

            let config = PrismaMonitorConfig {
                queue_poll_interval_ms: 1000,
                task_poll_interval_ms: 1000,
                max_task_history: 1000,
                enable_detailed_task_tracking: tracking_enabled,
            };

            // Verify boolean value is preserved
            assert_eq!(config.enable_detailed_task_tracking, tracking_enabled,
                "Detailed tracking should be preserved for {}", description);

            // Test boolean logic operations
            assert_eq!(config.enable_detailed_task_tracking && true, tracking_enabled,
                "Boolean AND should work correctly for {}", description);
            assert_eq!(config.enable_detailed_task_tracking || false, tracking_enabled,
                "Boolean OR should work correctly for {}", description);

            println!("Tracking option {} passed", description);
        }

        println!("Enable detailed task tracking boolean validation test passed");
    }

    println!("Configuration parameter validation test passed");
}

/// Test configuration updates and propagation
#[tokio::test]
async fn test_configuration_updates_and_propagation() {
    println!("Starting configuration updates and propagation test");

    // Test 1: Configuration updates through Monitor::update_config()
    {
        println!("Testing configuration updates through Monitor::update_config()");

        use prisma_ai::prisma::prisma_engine::monitor::{Monitor, types::MonitorConfig};

        // Create initial monitor with default config
        let initial_config = MonitorConfig::default();
        let mut monitor = Monitor::new(initial_config.clone());

        // Verify initial configuration
        let initial_prisma_config = monitor.get_prisma_config();
        assert_eq!(initial_prisma_config.queue_poll_interval_ms, initial_config.poll_interval_ms,
            "Initial queue poll interval should match monitor config");
        assert_eq!(initial_prisma_config.task_poll_interval_ms, initial_config.poll_interval_ms,
            "Initial task poll interval should match monitor config");

        println!("Initial config: poll_interval={}ms", initial_config.poll_interval_ms);

        // Update configuration
        let updated_config = MonitorConfig {
            poll_interval_ms: 2500, // Different from default 5000ms
        };

        let update_result = monitor.update_config(updated_config.clone()).await;
        assert!(update_result.is_ok(), "Configuration update should succeed");

        // Verify configuration was updated
        let updated_prisma_config = monitor.get_prisma_config();
        assert_eq!(updated_prisma_config.queue_poll_interval_ms, updated_config.poll_interval_ms,
            "Updated queue poll interval should match new monitor config");
        assert_eq!(updated_prisma_config.task_poll_interval_ms, updated_config.poll_interval_ms,
            "Updated task poll interval should match new monitor config");

        println!("Updated config: poll_interval={}ms", updated_config.poll_interval_ms);
        println!("Configuration updates through Monitor::update_config() test passed");
    }

    // Test 2: Configuration propagation to QueueMonitor and TaskMonitor
    {
        println!("Testing configuration propagation to QueueMonitor and TaskMonitor");

        use prisma_ai::prisma::prisma_engine::monitor::{Monitor, types::MonitorConfig};
        use prisma_ai::prisma::prisma_engine::monitor::prisma::{QueueMonitor, TaskMonitor};

        // Create monitor with custom configuration
        let custom_config = MonitorConfig {
            poll_interval_ms: 1500,
        };
        let mut monitor = Monitor::new(custom_config.clone());

        // Verify that the configuration is propagated to sub-monitors
        let prisma_config = monitor.get_prisma_config();
        assert_eq!(prisma_config.queue_poll_interval_ms, custom_config.poll_interval_ms,
            "Queue monitor should receive propagated config");
        assert_eq!(prisma_config.task_poll_interval_ms, custom_config.poll_interval_ms,
            "Task monitor should receive propagated config");

        // Test creating individual monitors with the same config
        let _queue_monitor = QueueMonitor::new(prisma_config.clone());
        let _task_monitor = TaskMonitor::new(prisma_config.clone());

        // Verify monitors can be created with propagated config
        // (The monitors should be created successfully without panicking)
        println!("QueueMonitor and TaskMonitor created with propagated config");

        // Test configuration update propagation
        let new_config = MonitorConfig {
            poll_interval_ms: 3500,
        };

        let update_result = monitor.update_config(new_config.clone()).await;
        assert!(update_result.is_ok(), "Configuration update should succeed");

        // Verify propagation after update
        let updated_prisma_config = monitor.get_prisma_config();
        assert_eq!(updated_prisma_config.queue_poll_interval_ms, new_config.poll_interval_ms,
            "Queue monitor config should be updated after propagation");
        assert_eq!(updated_prisma_config.task_poll_interval_ms, new_config.poll_interval_ms,
            "Task monitor config should be updated after propagation");

        println!("Configuration propagation to QueueMonitor and TaskMonitor test passed");
    }

    // Test 3: Configuration changes during runtime
    {
        println!("Testing configuration changes during runtime");

        use prisma_ai::prisma::prisma_engine::monitor::{Monitor, types::MonitorConfig};
        use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor;
        use std::time::Duration;
        use tokio::time::sleep;

        // Create and start monitor
        let initial_config = MonitorConfig {
            poll_interval_ms: 1000,
        };
        let mut monitor = Monitor::new(initial_config.clone());

        // Start the monitor
        let start_result = ResourceMonitor::start(&mut monitor).await;
        assert!(start_result.is_ok(), "Monitor should start successfully");

        // Allow some monitoring activity
        sleep(Duration::from_millis(200)).await;

        // Update configuration while running
        let runtime_config = MonitorConfig {
            poll_interval_ms: 2000,
        };

        let runtime_update_result = monitor.update_config(runtime_config.clone()).await;
        assert!(runtime_update_result.is_ok(), "Runtime configuration update should succeed");

        // Verify configuration was updated during runtime
        let runtime_prisma_config = monitor.get_prisma_config();
        assert_eq!(runtime_prisma_config.queue_poll_interval_ms, runtime_config.poll_interval_ms,
            "Runtime queue poll interval should be updated");
        assert_eq!(runtime_prisma_config.task_poll_interval_ms, runtime_config.poll_interval_ms,
            "Runtime task poll interval should be updated");

        // Allow time for new configuration to take effect
        sleep(Duration::from_millis(200)).await;

        // Stop the monitor
        let stop_result = ResourceMonitor::stop(&mut monitor).await;
        assert!(stop_result.is_ok(), "Monitor should stop successfully");

        println!("Configuration changes during runtime test passed");
    }

    // Test 4: Configuration persistence across monitor restarts
    {
        println!("Testing configuration persistence across monitor restarts");

        use prisma_ai::prisma::prisma_engine::monitor::{Monitor, types::MonitorConfig};
        use prisma_ai::prisma::prisma_engine::traits::ResourceMonitor;
        use std::time::Duration;
        use tokio::time::sleep;

        // Create monitor with custom configuration
        let persistent_config = MonitorConfig {
            poll_interval_ms: 4000,
        };
        let mut monitor = Monitor::new(persistent_config.clone());

        // Start and stop monitor multiple times
        for cycle in 1..=3 {
            println!("Testing persistence cycle {}", cycle);

            // Start monitor
            let start_result = ResourceMonitor::start(&mut monitor).await;
            assert!(start_result.is_ok(), "Monitor should start in cycle {}", cycle);

            // Verify configuration persists
            let cycle_config = monitor.get_prisma_config();
            assert_eq!(cycle_config.queue_poll_interval_ms, persistent_config.poll_interval_ms,
                "Configuration should persist across restart cycle {}", cycle);
            assert_eq!(cycle_config.task_poll_interval_ms, persistent_config.poll_interval_ms,
                "Configuration should persist across restart cycle {}", cycle);

            // Brief monitoring period
            sleep(Duration::from_millis(100)).await;

            // Stop monitor
            let stop_result = ResourceMonitor::stop(&mut monitor).await;
            assert!(stop_result.is_ok(), "Monitor should stop in cycle {}", cycle);

            // Brief pause between cycles
            sleep(Duration::from_millis(50)).await;
        }

        // Final verification after all cycles
        let final_config = monitor.get_prisma_config();
        assert_eq!(final_config.queue_poll_interval_ms, persistent_config.poll_interval_ms,
            "Configuration should persist after all restart cycles");
        assert_eq!(final_config.task_poll_interval_ms, persistent_config.poll_interval_ms,
            "Configuration should persist after all restart cycles");

        println!("Configuration persistence across monitor restarts test passed");
    }

    println!("Configuration updates and propagation test passed");
}

/// Test configuration serialization and deserialization
#[tokio::test]
async fn test_configuration_serialization_and_deserialization() {
    println!("Starting configuration serialization and deserialization test");

    // Test 1: JSON serialization/deserialization
    {
        println!("Testing JSON serialization/deserialization");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::types::PrismaMonitorConfig;
        use serde_json;

        let test_configs = vec![
            PrismaMonitorConfig::default(),
            PrismaMonitorConfig {
                queue_poll_interval_ms: 500,
                task_poll_interval_ms: 750,
                max_task_history: 2000,
                enable_detailed_task_tracking: false,
            },
            PrismaMonitorConfig {
                queue_poll_interval_ms: 0,
                task_poll_interval_ms: u64::MAX,
                max_task_history: 0,
                enable_detailed_task_tracking: true,
            },
        ];

        for (i, original_config) in test_configs.into_iter().enumerate() {
            println!("Testing JSON serialization/deserialization for config {}", i + 1);

            // Serialize to JSON
            let json_result = serde_json::to_string(&original_config);
            assert!(json_result.is_ok(), "JSON serialization should succeed for config {}", i + 1);

            let json_string = json_result.unwrap();
            println!("Serialized JSON: {}", json_string);

            // Verify JSON contains expected fields
            assert!(json_string.contains("queue_poll_interval_ms"), "JSON should contain queue_poll_interval_ms");
            assert!(json_string.contains("task_poll_interval_ms"), "JSON should contain task_poll_interval_ms");
            assert!(json_string.contains("max_task_history"), "JSON should contain max_task_history");
            assert!(json_string.contains("enable_detailed_task_tracking"), "JSON should contain enable_detailed_task_tracking");

            // Deserialize from JSON
            let deserialize_result: Result<PrismaMonitorConfig, _> = serde_json::from_str(&json_string);
            assert!(deserialize_result.is_ok(), "JSON deserialization should succeed for config {}", i + 1);

            let deserialized_config = deserialize_result.unwrap();

            // Verify round-trip consistency
            assert_eq!(deserialized_config.queue_poll_interval_ms, original_config.queue_poll_interval_ms,
                "Queue poll interval should match after JSON round-trip for config {}", i + 1);
            assert_eq!(deserialized_config.task_poll_interval_ms, original_config.task_poll_interval_ms,
                "Task poll interval should match after JSON round-trip for config {}", i + 1);
            assert_eq!(deserialized_config.max_task_history, original_config.max_task_history,
                "Max task history should match after JSON round-trip for config {}", i + 1);
            assert_eq!(deserialized_config.enable_detailed_task_tracking, original_config.enable_detailed_task_tracking,
                "Detailed tracking should match after JSON round-trip for config {}", i + 1);

            println!("JSON round-trip for config {} passed", i + 1);
        }

        println!("JSON serialization/deserialization test passed");
    }

    // Test 2: Pretty JSON serialization
    {
        println!("Testing pretty JSON serialization");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::types::PrismaMonitorConfig;
        use serde_json;

        let config = PrismaMonitorConfig {
            queue_poll_interval_ms: 1500,
            task_poll_interval_ms: 2000,
            max_task_history: 5000,
            enable_detailed_task_tracking: true,
        };

        // Serialize to pretty JSON
        let pretty_json_result = serde_json::to_string_pretty(&config);
        assert!(pretty_json_result.is_ok(), "Pretty JSON serialization should succeed");

        let pretty_json = pretty_json_result.unwrap();
        println!("Pretty JSON:\n{}", pretty_json);

        // Verify pretty formatting (should contain newlines and indentation)
        assert!(pretty_json.contains('\n'), "Pretty JSON should contain newlines");
        assert!(pretty_json.len() > serde_json::to_string(&config).unwrap().len(),
            "Pretty JSON should be longer than compact JSON");

        // Verify it can still be deserialized
        let deserialize_result: Result<PrismaMonitorConfig, _> = serde_json::from_str(&pretty_json);
        assert!(deserialize_result.is_ok(), "Pretty JSON should be deserializable");

        let deserialized = deserialize_result.unwrap();
        assert_eq!(deserialized.queue_poll_interval_ms, config.queue_poll_interval_ms,
            "Pretty JSON deserialization should preserve queue poll interval");

        println!("Pretty JSON serialization test passed");
    }

    // Test 3: Configuration round-trip consistency
    {
        println!("Testing configuration round-trip consistency");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::types::PrismaMonitorConfig;
        use serde_json;

        // Test multiple round-trips to ensure consistency
        let mut config = PrismaMonitorConfig {
            queue_poll_interval_ms: 1234,
            task_poll_interval_ms: 5678,
            max_task_history: 9999,
            enable_detailed_task_tracking: false,
        };

        for round in 1..=5 {
            println!("Testing round-trip consistency round {}", round);

            // Serialize
            let json = serde_json::to_string(&config).unwrap();

            // Deserialize
            let deserialized: PrismaMonitorConfig = serde_json::from_str(&json).unwrap();

            // Verify consistency
            assert_eq!(deserialized.queue_poll_interval_ms, config.queue_poll_interval_ms,
                "Queue poll interval should be consistent in round {}", round);
            assert_eq!(deserialized.task_poll_interval_ms, config.task_poll_interval_ms,
                "Task poll interval should be consistent in round {}", round);
            assert_eq!(deserialized.max_task_history, config.max_task_history,
                "Max task history should be consistent in round {}", round);
            assert_eq!(deserialized.enable_detailed_task_tracking, config.enable_detailed_task_tracking,
                "Detailed tracking should be consistent in round {}", round);

            // Use deserialized config for next round
            config = deserialized;
        }

        println!("Configuration round-trip consistency test passed");
    }

    // Test 4: Error handling for malformed configuration data
    {
        println!("Testing error handling for malformed configuration data");

        use prisma_ai::prisma::prisma_engine::monitor::prisma::types::PrismaMonitorConfig;
        use serde_json;

        let malformed_json_cases = vec![
            ("", "Empty string"),
            ("{", "Incomplete JSON"),
            ("null", "Null value"),
            ("[]", "Array instead of object"),
            ("{\"invalid\": true}", "Missing required fields"),
            ("{\"queue_poll_interval_ms\": \"not_a_number\"}", "Invalid field type"),
            ("{\"queue_poll_interval_ms\": 1000, \"task_poll_interval_ms\": 1000, \"max_task_history\": -1}", "Negative number for usize"),
        ];

        for (malformed_json, description) in malformed_json_cases {
            println!("Testing malformed JSON: {}", description);

            let deserialize_result: Result<PrismaMonitorConfig, _> = serde_json::from_str(malformed_json);
            assert!(deserialize_result.is_err(), "Malformed JSON should fail to deserialize: {}", description);

            let error = deserialize_result.unwrap_err();
            println!("Expected error for {}: {}", description, error);
        }

        // Test partial JSON (missing fields)
        let partial_json = r#"{"queue_poll_interval_ms": 1000}"#;
        let partial_result: Result<PrismaMonitorConfig, _> = serde_json::from_str(partial_json);
        assert!(partial_result.is_err(), "Partial JSON should fail to deserialize");

        // Test extra fields (should be ignored)
        let extra_fields_json = r#"{
            "queue_poll_interval_ms": 1000,
            "task_poll_interval_ms": 2000,
            "max_task_history": 3000,
            "enable_detailed_task_tracking": true,
            "extra_field": "should_be_ignored"
        }"#;
        let extra_fields_result: Result<PrismaMonitorConfig, _> = serde_json::from_str(extra_fields_json);
        assert!(extra_fields_result.is_ok(), "JSON with extra fields should deserialize successfully");

        let config_with_extra = extra_fields_result.unwrap();
        assert_eq!(config_with_extra.queue_poll_interval_ms, 1000, "Extra fields should not affect valid fields");

        println!("Error handling for malformed configuration data test passed");
    }

    println!("Configuration serialization and deserialization test passed");
}

// ================================================================================================
// Network Availability Calculation Tests
// ================================================================================================

/// Test network availability based on bandwidth utilization
#[tokio::test]
async fn test_network_availability_based_on_bandwidth_utilization() {
    println!("Starting network availability based on bandwidth utilization test");

    // Test 1: Basic availability calculation with default monitor
    {
        println!("Testing basic network availability calculation");

        let mut monitor = create_default_network_monitor();

        // Import traits locally to avoid conflicts
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        // Start monitoring to collect real network metrics
        monitor.start().await.expect("Network monitor should start");

        // Allow time for initial metrics collection
        sleep(Duration::from_millis(300)).await;

        // Get availability based on current bandwidth usage
        let availability = monitor.get_availability().await;
        assert!(availability.is_ok(), "Should get network availability successfully");

        let availability_value = availability.unwrap().0;
        assert!(availability_value >= 0.0 && availability_value <= 100.0,
            "Availability should be between 0-100%, got: {}", availability_value);

        // Get network metrics to verify calculation
        let network_metrics = monitor.get_network_metrics().await;
        assert!(network_metrics.is_ok(), "Should get network metrics successfully");

        let metrics = network_metrics.unwrap();
        println!("Network interfaces detected: {}", metrics.interfaces.len());

        // Verify availability calculation logic
        let total_bandwidth = metrics.total_rx_bytes_per_sec + metrics.total_tx_bytes_per_sec;
        const MAX_BANDWIDTH: f64 = 125.0 * 1024.0 * 1024.0; // 1 Gbps
        let expected_usage_percent = (total_bandwidth / MAX_BANDWIDTH * 100.0).min(100.0);
        let expected_availability = 100.0 - expected_usage_percent;

        println!("Total bandwidth: {:.2} bytes/sec ({:.2} MB/s)",
            total_bandwidth, total_bandwidth / 1024.0 / 1024.0);
        println!("Expected availability: {:.2}%, Actual: {:.2}%",
            expected_availability, availability_value);

        // Allow for small variance due to timing and error penalties
        assert!((availability_value - expected_availability).abs() <= 20.0,
            "Availability calculation should be close to expected: {} vs {}",
            availability_value, expected_availability);

        monitor.stop().await.expect("Network monitor should stop");
        println!("Basic network availability calculation test passed");
    }

    // Test 2: Availability with different bandwidth scenarios
    {
        println!("Testing network availability with different bandwidth scenarios");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(400)).await;

        // Collect multiple samples to see bandwidth variation
        let mut availability_samples = Vec::new();
        let mut bandwidth_samples = Vec::new();

        for i in 0..10 {
            let availability = monitor.get_availability().await
                .expect("Should get availability");
            let (rx_bandwidth, tx_bandwidth) = monitor.get_network_bandwidth_usage().await
                .expect("Should get bandwidth usage");

            let total_bandwidth = rx_bandwidth + tx_bandwidth;
            availability_samples.push(availability.0);
            bandwidth_samples.push(total_bandwidth);

            println!("Sample {}: Availability={:.2}%, RX={:.2} MB/s, TX={:.2} MB/s, Total={:.2} MB/s",
                i + 1, availability.0,
                rx_bandwidth / 1024.0 / 1024.0,
                tx_bandwidth / 1024.0 / 1024.0,
                total_bandwidth / 1024.0 / 1024.0);

            sleep(Duration::from_millis(200)).await;
        }

        // Verify all availability measurements are valid
        for (i, &availability) in availability_samples.iter().enumerate() {
            assert!(availability >= 0.0 && availability <= 100.0,
                "Sample {} availability should be 0-100%: {}", i + 1, availability);
        }

        // Verify bandwidth utilization correlation with availability
        for (i, (&availability, &bandwidth)) in availability_samples.iter().zip(bandwidth_samples.iter()).enumerate() {
            const MAX_BANDWIDTH: f64 = 125.0 * 1024.0 * 1024.0;
            let expected_usage = (bandwidth / MAX_BANDWIDTH * 100.0).min(100.0);
            let expected_availability = 100.0 - expected_usage;

            // Allow for error penalties and timing variance
            let tolerance = 25.0;
            assert!((availability - expected_availability).abs() <= tolerance,
                "Sample {} availability correlation: actual={:.2}%, expected={:.2}%, bandwidth={:.2} MB/s",
                i + 1, availability, expected_availability, bandwidth / 1024.0 / 1024.0);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Network availability with different bandwidth scenarios test passed");
    }

    // Test 3: Bandwidth utilization threshold testing
    {
        println!("Testing bandwidth utilization threshold behavior");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(300)).await;

        let availability = monitor.get_availability().await.unwrap().0;
        let (rx_bandwidth, tx_bandwidth) = monitor.get_network_bandwidth_usage().await.unwrap();
        let total_bandwidth = rx_bandwidth + tx_bandwidth;

        // Test bandwidth utilization calculation
        const MAX_BANDWIDTH: f64 = 125.0 * 1024.0 * 1024.0; // 1 Gbps
        let utilization_percent = (total_bandwidth / MAX_BANDWIDTH * 100.0).min(100.0);

        println!("Current bandwidth utilization: {:.4}% of max capacity", utilization_percent);
        println!("Max bandwidth: {:.2} MB/s, Current: {:.2} MB/s",
            MAX_BANDWIDTH / 1024.0 / 1024.0, total_bandwidth / 1024.0 / 1024.0);

        // Verify utilization is reasonable for a typical system
        assert!(utilization_percent >= 0.0 && utilization_percent <= 100.0,
            "Utilization should be 0-100%: {}", utilization_percent);

        // For most systems, utilization should be quite low
        if utilization_percent < 50.0 {
            assert!(availability > 50.0,
                "Low utilization should result in high availability: {}% util, {}% avail",
                utilization_percent, availability);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Bandwidth utilization threshold behavior test passed");
    }

    println!("Network availability based on bandwidth utilization test passed");
}

/// Test network availability with multiple network interfaces
#[tokio::test]
async fn test_network_availability_with_multiple_network_interfaces() {
    println!("Starting network availability with multiple network interfaces test");

    // Test 1: Multiple interface detection and metrics
    {
        println!("Testing multiple network interface detection and metrics");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(400)).await;

        let network_metrics = monitor.get_network_metrics().await;
        assert!(network_metrics.is_ok(), "Should get network metrics successfully");

        let metrics = network_metrics.unwrap();
        println!("Detected {} network interface(s)", metrics.interfaces.len());

        // Verify each interface has valid metrics
        for (interface_name, interface_info) in &metrics.interfaces {
            println!("Interface {}: RX={:.2} bytes/sec, TX={:.2} bytes/sec, Errors: RX={}, TX={}",
                interface_name,
                interface_info.rx_bytes_per_sec,
                interface_info.tx_bytes_per_sec,
                interface_info.rx_errors,
                interface_info.tx_errors);

            assert!(interface_info.rx_bytes_per_sec >= 0.0,
                "RX bytes/sec should be non-negative for interface {}", interface_name);
            assert!(interface_info.tx_bytes_per_sec >= 0.0,
                "TX bytes/sec should be non-negative for interface {}", interface_name);
            assert!(!interface_info.name.is_empty(),
                "Interface name should not be empty");

            // Verify MAC address format if present
            if let Some(mac) = &interface_info.mac_address {
                assert!(!mac.is_empty(), "MAC address should not be empty for interface {}", interface_name);
            }
        }

        // Get list of interfaces using the trait method
        let interface_list = monitor.get_network_interfaces().await;
        assert!(interface_list.is_ok(), "Should get interface list successfully");

        let interfaces = interface_list.unwrap();
        assert_eq!(interfaces.len(), metrics.interfaces.len(),
            "Interface list should match metrics interface count");

        monitor.stop().await.expect("Network monitor should stop");
        println!("Multiple network interface detection test passed");
    }

    // Test 2: Aggregated availability calculation across interfaces
    {
        println!("Testing aggregated availability calculation across interfaces");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(300)).await;

        let availability = monitor.get_availability().await;
        assert!(availability.is_ok(), "Should get aggregated availability");

        let availability_value = availability.unwrap().0;
        let network_metrics = monitor.get_network_metrics().await.unwrap();

        // Manually calculate expected availability from individual interfaces
        let total_rx = network_metrics.total_rx_bytes_per_sec;
        let total_tx = network_metrics.total_tx_bytes_per_sec;
        let total_bandwidth = total_rx + total_tx;

        // Verify totals match sum of individual interfaces
        let calculated_rx: f64 = network_metrics.interfaces.values()
            .map(|i| i.rx_bytes_per_sec)
            .sum();
        let calculated_tx: f64 = network_metrics.interfaces.values()
            .map(|i| i.tx_bytes_per_sec)
            .sum();

        assert!((total_rx - calculated_rx).abs() < 0.1,
            "Total RX should match sum of interfaces: {} vs {}", total_rx, calculated_rx);
        assert!((total_tx - calculated_tx).abs() < 0.1,
            "Total TX should match sum of interfaces: {} vs {}", total_tx, calculated_tx);

        // Calculate expected availability
        const MAX_BANDWIDTH: f64 = 125.0 * 1024.0 * 1024.0;
        let expected_usage = (total_bandwidth / MAX_BANDWIDTH * 100.0).min(100.0);
        let expected_availability = 100.0 - expected_usage;

        println!("Aggregated availability: {:.2}% (from {} interfaces)",
            availability_value, network_metrics.interfaces.len());
        println!("Total bandwidth: RX={:.2} MB/s, TX={:.2} MB/s",
            total_rx / 1024.0 / 1024.0, total_tx / 1024.0 / 1024.0);

        // Check for network errors across all interfaces
        let has_errors = network_metrics.interfaces.values()
            .any(|interface| interface.rx_errors > 0 || interface.tx_errors > 0);

        if has_errors {
            println!("Network errors detected, availability may be reduced");
            // With errors, availability should be reduced by error penalty (0.8 factor)
            let expected_with_penalty = expected_availability * 0.8;
            assert!((availability_value - expected_with_penalty).abs() <= 5.0,
                "Availability with errors should match expected: {} vs {}",
                availability_value, expected_with_penalty);
        } else {
            // Without errors, should match basic calculation (with some tolerance)
            assert!((availability_value - expected_availability).abs() <= 5.0,
                "Availability without errors should match expected: {} vs {}",
                availability_value, expected_availability);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Aggregated availability calculation test passed");
    }

    // Test 3: Per-interface bandwidth contribution analysis
    {
        println!("Testing per-interface bandwidth contribution analysis");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(400)).await;

        let network_metrics = monitor.get_network_metrics().await.unwrap();
        let total_bandwidth = network_metrics.total_rx_bytes_per_sec + network_metrics.total_tx_bytes_per_sec;

        println!("Analyzing bandwidth contribution per interface:");

        for (interface_name, interface_info) in &network_metrics.interfaces {
            let interface_bandwidth = interface_info.rx_bytes_per_sec + interface_info.tx_bytes_per_sec;
            let contribution_percent = if total_bandwidth > 0.0 {
                (interface_bandwidth / total_bandwidth) * 100.0
            } else {
                0.0
            };

            println!("  Interface {}: {:.2} bytes/sec ({:.1}% of total)",
                interface_name, interface_bandwidth, contribution_percent);

            assert!(interface_bandwidth >= 0.0,
                "Interface bandwidth should be non-negative: {}", interface_bandwidth);
            assert!(contribution_percent >= 0.0 && contribution_percent <= 100.0,
                "Contribution percent should be 0-100%: {}", contribution_percent);

            // Individual interface availability simulation
            const MAX_BANDWIDTH: f64 = 125.0 * 1024.0 * 1024.0;
            let interface_usage = (interface_bandwidth / MAX_BANDWIDTH * 100.0).min(100.0);
            let interface_availability = 100.0 - interface_usage;

            // Apply error penalty if interface has errors
            let adjusted_availability = if interface_info.rx_errors > 0 || interface_info.tx_errors > 0 {
                interface_availability * 0.8
            } else {
                interface_availability
            };

            println!("    Usage: {:.4}%, Availability: {:.2}%{}",
                interface_usage, adjusted_availability,
                if interface_info.rx_errors > 0 || interface_info.tx_errors > 0 { " (with error penalty)" } else { "" });

            assert!(adjusted_availability >= 0.0 && adjusted_availability <= 100.0,
                "Interface availability should be 0-100%: {}", adjusted_availability);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Per-interface bandwidth contribution analysis test passed");
    }

    // Test 4: Interface error impact on overall availability
    {
        println!("Testing interface error impact on overall availability");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(300)).await;

        let network_metrics = monitor.get_network_metrics().await.unwrap();
        let availability = monitor.get_availability().await.unwrap().0;

        // Analyze error distribution across interfaces
        let mut interfaces_with_errors = 0;
        let mut total_rx_errors = 0;
        let mut total_tx_errors = 0;

        for (interface_name, interface_info) in &network_metrics.interfaces {
            if interface_info.rx_errors > 0 || interface_info.tx_errors > 0 {
                interfaces_with_errors += 1;
                total_rx_errors += interface_info.rx_errors;
                total_tx_errors += interface_info.tx_errors;

                println!("Interface {} has errors: RX={}, TX={}",
                    interface_name, interface_info.rx_errors, interface_info.tx_errors);
            }
        }

        println!("Error summary: {} interfaces with errors, Total RX errors: {}, Total TX errors: {}",
            interfaces_with_errors, total_rx_errors, total_tx_errors);

        // Calculate baseline availability without error penalty
        let total_bandwidth = network_metrics.total_rx_bytes_per_sec + network_metrics.total_tx_bytes_per_sec;
        const MAX_BANDWIDTH: f64 = 125.0 * 1024.0 * 1024.0;
        let usage_percent = (total_bandwidth / MAX_BANDWIDTH * 100.0).min(100.0);
        let baseline_availability = 100.0 - usage_percent;

        if interfaces_with_errors > 0 {
            // With errors, availability should be reduced
            let expected_with_penalty = baseline_availability * 0.8;
            println!("Baseline availability: {:.2}%, With error penalty: {:.2}%, Actual: {:.2}%",
                baseline_availability, expected_with_penalty, availability);

            // Allow some tolerance for timing and other factors
            assert!(availability <= baseline_availability,
                "Availability with errors should not exceed baseline: {} vs {}",
                availability, baseline_availability);
        } else {
            println!("No errors detected, availability should match baseline: {:.2}%", availability);
            // Without errors, should be close to baseline (with small tolerance)
            assert!((availability - baseline_availability).abs() <= 5.0,
                "Availability without errors should match baseline: {} vs {}",
                availability, baseline_availability);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Interface error impact test passed");
    }

    println!("Network availability with multiple network interfaces test passed");
}

/// Test network availability calculation with network congestion
#[tokio::test]
async fn test_network_availability_calculation_with_network_congestion() {
    println!("Starting network availability calculation with network congestion test");

    // Test 1: Congestion detection through error rates
    {
        println!("Testing congestion detection through error rates");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(400)).await;

        let network_metrics = monitor.get_network_metrics().await.unwrap();
        let availability = monitor.get_availability().await.unwrap().0;

        // Analyze congestion indicators
        let mut total_errors = 0;
        let mut high_error_interfaces = 0;
        let mut congestion_indicators = Vec::new();

        for (interface_name, interface_info) in &network_metrics.interfaces {
            let interface_errors = interface_info.rx_errors + interface_info.tx_errors;
            total_errors += interface_errors;

            let interface_bandwidth = interface_info.rx_bytes_per_sec + interface_info.tx_bytes_per_sec;
            let error_rate = if interface_bandwidth > 0.0 {
                interface_errors as f64 / interface_bandwidth * 1000.0 // errors per KB
            } else {
                0.0
            };

            if interface_errors > 0 {
                high_error_interfaces += 1;
                congestion_indicators.push((interface_name.clone(), interface_errors, error_rate));
            }

            println!("Interface {}: {} errors, {:.4} errors/KB, {:.2} MB/s",
                interface_name, interface_errors, error_rate, interface_bandwidth / 1024.0 / 1024.0);
        }

        println!("Congestion analysis: {} total errors, {} interfaces with errors",
            total_errors, high_error_interfaces);

        // Test congestion impact on availability
        let total_bandwidth = network_metrics.total_rx_bytes_per_sec + network_metrics.total_tx_bytes_per_sec;
        const MAX_BANDWIDTH: f64 = 125.0 * 1024.0 * 1024.0;
        let baseline_usage = (total_bandwidth / MAX_BANDWIDTH * 100.0).min(100.0);
        let baseline_availability = 100.0 - baseline_usage;

        if total_errors > 0 {
            // With errors (congestion indicator), availability should be reduced
            println!("Congestion detected (errors present), availability should be reduced");
            let expected_with_penalty = baseline_availability * 0.8;

            // Availability should be between the penalized value and baseline
            assert!(availability <= baseline_availability,
                "Availability with congestion should not exceed baseline: {} vs {}",
                availability, baseline_availability);

            // Should be close to the expected penalized value (with some tolerance)
            assert!((availability - expected_with_penalty).abs() <= 10.0,
                "Availability with congestion should be close to expected: {} vs {}",
                availability, expected_with_penalty);
        } else {
            println!("No congestion detected, availability should be close to baseline");
            assert!((availability - baseline_availability).abs() <= 5.0,
                "Availability without congestion should match baseline: {} vs {}",
                availability, baseline_availability);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Congestion detection through error rates test passed");
    }

    // Test 2: High bandwidth utilization as congestion indicator
    {
        println!("Testing high bandwidth utilization as congestion indicator");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(300)).await;

        // Collect multiple samples to analyze bandwidth patterns
        let mut bandwidth_samples = Vec::new();
        let mut availability_samples = Vec::new();

        for i in 0..8 {
            let (rx_bandwidth, tx_bandwidth) = monitor.get_network_bandwidth_usage().await.unwrap();
            let total_bandwidth = rx_bandwidth + tx_bandwidth;
            let availability = monitor.get_availability().await.unwrap().0;

            bandwidth_samples.push(total_bandwidth);
            availability_samples.push(availability);

            const MAX_BANDWIDTH: f64 = 125.0 * 1024.0 * 1024.0;
            let utilization_percent = (total_bandwidth / MAX_BANDWIDTH * 100.0).min(100.0);

            println!("Sample {}: {:.4}% utilization, {:.2}% availability, {:.2} MB/s",
                i + 1, utilization_percent, availability, total_bandwidth / 1024.0 / 1024.0);

            // Classify congestion level based on utilization
            let congestion_level = if utilization_percent > 80.0 {
                "High"
            } else if utilization_percent > 50.0 {
                "Medium"
            } else if utilization_percent > 20.0 {
                "Low"
            } else {
                "None"
            };

            println!("  Congestion level: {}", congestion_level);

            sleep(Duration::from_millis(150)).await;
        }

        // Analyze correlation between utilization and availability
        for (i, (&bandwidth, &availability)) in bandwidth_samples.iter().zip(availability_samples.iter()).enumerate() {
            const MAX_BANDWIDTH: f64 = 125.0 * 1024.0 * 1024.0;
            let utilization = (bandwidth / MAX_BANDWIDTH * 100.0).min(100.0);
            let expected_availability = 100.0 - utilization;

            // Higher utilization should generally correlate with lower availability
            if utilization > 10.0 {
                assert!(availability <= 95.0,
                    "Sample {} with high utilization should have reduced availability: {}% util, {}% avail",
                    i + 1, utilization, availability);
            }

            // Verify availability is within reasonable bounds of expected
            let tolerance = 25.0; // Allow for error penalties and timing variance
            assert!((availability - expected_availability).abs() <= tolerance,
                "Sample {} availability should correlate with utilization: {}% util, {}% avail (expected ~{}%)",
                i + 1, utilization, availability, expected_availability);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("High bandwidth utilization as congestion indicator test passed");
    }

    // Test 3: Packet rate analysis for congestion detection
    {
        println!("Testing packet rate analysis for congestion detection");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(400)).await;

        let network_metrics = monitor.get_network_metrics().await.unwrap();

        // Analyze packet rates across interfaces
        let mut total_packet_rate = 0.0;
        let mut high_packet_rate_interfaces = 0;

        for (interface_name, interface_info) in &network_metrics.interfaces {
            let packet_rate = interface_info.rx_packets_per_sec + interface_info.tx_packets_per_sec;
            total_packet_rate += packet_rate;

            // Calculate bytes per packet (efficiency indicator)
            let bytes_per_packet = if packet_rate > 0.0 {
                (interface_info.rx_bytes_per_sec + interface_info.tx_bytes_per_sec) / packet_rate
            } else {
                0.0
            };

            println!("Interface {}: {:.2} packets/sec, {:.2} bytes/packet",
                interface_name, packet_rate, bytes_per_packet);

            // High packet rate with low bytes per packet might indicate congestion
            if packet_rate > 100.0 && bytes_per_packet < 100.0 {
                high_packet_rate_interfaces += 1;
                println!("  Potential congestion: high packet rate with small packets");
            }

            assert!(packet_rate >= 0.0, "Packet rate should be non-negative: {}", packet_rate);
            assert!(bytes_per_packet >= 0.0, "Bytes per packet should be non-negative: {}", bytes_per_packet);
        }

        println!("Total packet rate: {:.2} packets/sec across {} interfaces",
            total_packet_rate, network_metrics.interfaces.len());

        // Correlate packet patterns with availability
        let availability = monitor.get_availability().await.unwrap().0;

        if high_packet_rate_interfaces > 0 {
            println!("High packet rate detected on {} interfaces, may indicate congestion",
                high_packet_rate_interfaces);
        }

        // Verify packet rate metrics are consistent
        let calculated_total: f64 = network_metrics.interfaces.values()
            .map(|i| i.rx_packets_per_sec + i.tx_packets_per_sec)
            .sum();

        assert!((total_packet_rate - calculated_total).abs() < 0.1,
            "Total packet rate should match sum of interfaces: {} vs {}",
            total_packet_rate, calculated_total);

        monitor.stop().await.expect("Network monitor should stop");
        println!("Packet rate analysis for congestion detection test passed");
    }

    // Test 4: Congestion impact on availability over time
    {
        println!("Testing congestion impact on availability over time");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(300)).await;

        // Monitor availability trends over time
        let mut availability_trend = Vec::new();
        let mut error_trend = Vec::new();
        let mut bandwidth_trend = Vec::new();

        for i in 0..12 {
            let availability = monitor.get_availability().await.unwrap().0;
            let network_metrics = monitor.get_network_metrics().await.unwrap();
            let total_bandwidth = network_metrics.total_rx_bytes_per_sec + network_metrics.total_tx_bytes_per_sec;

            let total_errors: u64 = network_metrics.interfaces.values()
                .map(|i| i.rx_errors + i.tx_errors)
                .sum();

            availability_trend.push(availability);
            error_trend.push(total_errors);
            bandwidth_trend.push(total_bandwidth);

            println!("Time {}: {:.2}% availability, {} errors, {:.2} MB/s",
                i + 1, availability, total_errors, total_bandwidth / 1024.0 / 1024.0);

            sleep(Duration::from_millis(100)).await;
        }

        // Analyze trends
        let avg_availability = availability_trend.iter().sum::<f64>() / availability_trend.len() as f64;
        let max_availability = availability_trend.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let min_availability = availability_trend.iter().fold(f64::INFINITY, |a, &b| a.min(b));

        println!("Availability trend: avg={:.2}%, min={:.2}%, max={:.2}%",
            avg_availability, min_availability, max_availability);

        // Verify availability remains within valid bounds
        assert!(min_availability >= 0.0, "Minimum availability should be non-negative: {}", min_availability);
        assert!(max_availability <= 100.0, "Maximum availability should not exceed 100%: {}", max_availability);

        // Check for correlation between errors and availability
        let has_errors = error_trend.iter().any(|&errors| errors > 0);
        if has_errors {
            println!("Errors detected during monitoring period, availability may be impacted");
            // With errors, average availability should be somewhat reduced
            assert!(avg_availability <= 95.0,
                "Average availability with errors should be reduced: {}", avg_availability);
        }

        // Verify availability stability (shouldn't fluctuate wildly)
        let availability_range = max_availability - min_availability;
        assert!(availability_range <= 50.0,
            "Availability should be relatively stable: range={}", availability_range);

        monitor.stop().await.expect("Network monitor should stop");
        println!("Congestion impact on availability over time test passed");
    }

    println!("Network availability calculation with network congestion test passed");
}

/// Test network availability estimation with unknown bandwidth limits
#[tokio::test]
async fn test_network_availability_estimation_with_unknown_bandwidth_limits() {
    println!("Starting network availability estimation with unknown bandwidth limits test");

    // Test 1: Default bandwidth assumption behavior
    {
        println!("Testing default bandwidth assumption behavior");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(400)).await;

        let availability = monitor.get_availability().await.unwrap().0;
        let (rx_bandwidth, tx_bandwidth) = monitor.get_network_bandwidth_usage().await.unwrap();
        let total_bandwidth = rx_bandwidth + tx_bandwidth;

        // The implementation uses a hardcoded MAX_BANDWIDTH of 1 Gbps (125 MB/s)
        const DEFAULT_MAX_BANDWIDTH: f64 = 125.0 * 1024.0 * 1024.0;
        let expected_usage = (total_bandwidth / DEFAULT_MAX_BANDWIDTH * 100.0).min(100.0);
        let expected_availability = 100.0 - expected_usage;

        println!("Default max bandwidth assumption: {:.2} MB/s", DEFAULT_MAX_BANDWIDTH / 1024.0 / 1024.0);
        println!("Current bandwidth: {:.2} MB/s ({:.4}% of assumed max)",
            total_bandwidth / 1024.0 / 1024.0, expected_usage);
        println!("Expected availability: {:.2}%, Actual: {:.2}%", expected_availability, availability);

        // Verify the calculation uses the default assumption
        // Allow for error penalties (0.8 factor) and timing variance
        let tolerance = 25.0;
        assert!((availability - expected_availability).abs() <= tolerance,
            "Availability should be based on default bandwidth assumption: {} vs {}",
            availability, expected_availability);

        // For most systems, current usage should be well below the 1 Gbps assumption
        if expected_usage < 10.0 {
            assert!(availability > 85.0,
                "Low utilization should result in high availability: {}% util, {}% avail",
                expected_usage, availability);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Default bandwidth assumption behavior test passed");
    }

    // Test 2: Bandwidth limit estimation scenarios
    {
        println!("Testing bandwidth limit estimation scenarios");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(300)).await;

        let network_metrics = monitor.get_network_metrics().await.unwrap();
        let total_bandwidth = network_metrics.total_rx_bytes_per_sec + network_metrics.total_tx_bytes_per_sec;

        // Test different bandwidth limit scenarios
        let bandwidth_scenarios = vec![
            ("10 Mbps (slow)", 10.0 * 1024.0 * 1024.0 / 8.0),      // 1.25 MB/s
            ("100 Mbps (fast)", 100.0 * 1024.0 * 1024.0 / 8.0),    // 12.5 MB/s
            ("1 Gbps (default)", 1000.0 * 1024.0 * 1024.0 / 8.0),  // 125 MB/s
            ("10 Gbps (enterprise)", 10000.0 * 1024.0 * 1024.0 / 8.0), // 1250 MB/s
        ];

        for (scenario_name, max_bandwidth) in bandwidth_scenarios {
            let utilization = (total_bandwidth / max_bandwidth * 100.0).min(100.0);
            let estimated_availability = 100.0 - utilization;

            println!("Scenario {}: {:.4}% utilization, {:.2}% estimated availability",
                scenario_name, utilization, estimated_availability);

            // Verify calculations are reasonable
            assert!(utilization >= 0.0 && utilization <= 100.0,
                "Utilization should be 0-100% for {}: {}", scenario_name, utilization);
            assert!(estimated_availability >= 0.0 && estimated_availability <= 100.0,
                "Estimated availability should be 0-100% for {}: {}", scenario_name, estimated_availability);

            // For very high bandwidth limits, utilization should be very low
            if max_bandwidth > 1000.0 * 1024.0 * 1024.0 {
                assert!(utilization < 1.0,
                    "Utilization should be very low for high bandwidth scenario {}: {}%",
                    scenario_name, utilization);
            }
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Bandwidth limit estimation scenarios test passed");
    }

    // Test 3: Adaptive bandwidth estimation based on interface types
    {
        println!("Testing adaptive bandwidth estimation based on interface types");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(400)).await;

        let network_metrics = monitor.get_network_metrics().await.unwrap();

        // Analyze interfaces and estimate their likely bandwidth limits
        for (interface_name, interface_info) in &network_metrics.interfaces {
            let interface_bandwidth = interface_info.rx_bytes_per_sec + interface_info.tx_bytes_per_sec;

            // Estimate bandwidth based on interface name patterns (heuristic)
            let estimated_max_bandwidth = if interface_name.contains("lo") || interface_name.contains("loopback") {
                // Loopback interface - very high bandwidth
                10000.0 * 1024.0 * 1024.0 // 10 GB/s
            } else if interface_name.contains("eth") || interface_name.contains("en") {
                // Ethernet interface - assume 1 Gbps
                125.0 * 1024.0 * 1024.0 // 125 MB/s
            } else if interface_name.contains("wlan") || interface_name.contains("wifi") {
                // WiFi interface - assume 100 Mbps
                12.5 * 1024.0 * 1024.0 // 12.5 MB/s
            } else {
                // Unknown interface type - use conservative estimate
                125.0 * 1024.0 * 1024.0 // 125 MB/s
            };

            let estimated_utilization = (interface_bandwidth / estimated_max_bandwidth * 100.0).min(100.0);
            let estimated_availability = 100.0 - estimated_utilization;

            println!("Interface {}: {:.2} MB/s, estimated max {:.2} MB/s, {:.4}% util, {:.2}% avail",
                interface_name,
                interface_bandwidth / 1024.0 / 1024.0,
                estimated_max_bandwidth / 1024.0 / 1024.0,
                estimated_utilization,
                estimated_availability);

            // Verify estimates are reasonable
            assert!(estimated_utilization >= 0.0 && estimated_utilization <= 100.0,
                "Estimated utilization should be 0-100% for {}: {}", interface_name, estimated_utilization);
            assert!(estimated_availability >= 0.0 && estimated_availability <= 100.0,
                "Estimated availability should be 0-100% for {}: {}", interface_name, estimated_availability);

            // For most interfaces, utilization should be quite low
            if !interface_name.contains("lo") && estimated_utilization > 50.0 {
                println!("  Warning: High utilization detected on {}", interface_name);
            }
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Adaptive bandwidth estimation test passed");
    }

    // Test 4: Fallback behavior with unknown bandwidth
    {
        println!("Testing fallback behavior with unknown bandwidth");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(300)).await;

        // Test the actual implementation's fallback behavior
        let availability = monitor.get_availability().await.unwrap().0;
        let network_metrics = monitor.get_network_metrics().await.unwrap();

        // The implementation should always provide a reasonable availability value
        assert!(availability >= 0.0 && availability <= 100.0,
            "Availability should always be 0-100% even with unknown bandwidth: {}", availability);

        // Test edge cases
        let total_bandwidth = network_metrics.total_rx_bytes_per_sec + network_metrics.total_tx_bytes_per_sec;

        // Simulate very low bandwidth scenario
        if total_bandwidth < 1024.0 { // Less than 1 KB/s
            assert!(availability > 99.0,
                "Very low bandwidth should result in very high availability: {:.2} bytes/s, {}% avail",
                total_bandwidth, availability);
        }

        // Verify the implementation handles zero bandwidth gracefully
        if total_bandwidth == 0.0 {
            assert!(availability == 100.0,
                "Zero bandwidth should result in 100% availability: {}", availability);
        }

        // Test consistency across multiple calls
        let mut availability_samples = Vec::new();
        for i in 0..5 {
            let sample_availability = monitor.get_availability().await.unwrap().0;
            availability_samples.push(sample_availability);
            println!("Availability sample {}: {:.2}%", i + 1, sample_availability);
            sleep(Duration::from_millis(100)).await;
        }

        // Verify consistency (should not vary wildly)
        let avg_availability = availability_samples.iter().sum::<f64>() / availability_samples.len() as f64;
        let max_deviation = availability_samples.iter()
            .map(|&a| (a - avg_availability).abs())
            .fold(0.0, f64::max);

        assert!(max_deviation <= 20.0,
            "Availability should be relatively consistent: avg={:.2}%, max_deviation={:.2}%",
            avg_availability, max_deviation);

        monitor.stop().await.expect("Network monitor should stop");
        println!("Fallback behavior with unknown bandwidth test passed");
    }

    // Test 5: Bandwidth limit discovery through observation
    {
        println!("Testing bandwidth limit discovery through observation");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(400)).await;

        // Collect bandwidth observations over time
        let mut bandwidth_observations = Vec::new();
        let mut availability_observations = Vec::new();

        for i in 0..10 {
            let (rx_bandwidth, tx_bandwidth) = monitor.get_network_bandwidth_usage().await.unwrap();
            let total_bandwidth = rx_bandwidth + tx_bandwidth;
            let availability = monitor.get_availability().await.unwrap().0;

            bandwidth_observations.push(total_bandwidth);
            availability_observations.push(availability);

            println!("Observation {}: {:.2} MB/s, {:.2}% availability",
                i + 1, total_bandwidth / 1024.0 / 1024.0, availability);

            sleep(Duration::from_millis(150)).await;
        }

        // Analyze observations to estimate actual bandwidth characteristics
        let max_observed_bandwidth = bandwidth_observations.iter().fold(0.0f64, |a, &b| a.max(b));
        let avg_bandwidth = bandwidth_observations.iter().sum::<f64>() / bandwidth_observations.len() as f64;
        let avg_availability = availability_observations.iter().sum::<f64>() / availability_observations.len() as f64;

        println!("Bandwidth analysis: max={:.2} MB/s, avg={:.2} MB/s, avg_availability={:.2}%",
            max_observed_bandwidth / 1024.0 / 1024.0,
            avg_bandwidth / 1024.0 / 1024.0,
            avg_availability);

        // Estimate effective bandwidth limit based on observations
        // If availability is consistently high, the assumed limit is probably reasonable
        if avg_availability > 95.0 {
            println!("High availability suggests bandwidth limit assumption is conservative");
            assert!(max_observed_bandwidth < 50.0 * 1024.0 * 1024.0,
                "High availability should correlate with low bandwidth usage: {:.2} MB/s",
                max_observed_bandwidth / 1024.0 / 1024.0);
        }

        // Verify observations are consistent
        let bandwidth_variance = bandwidth_observations.iter()
            .map(|&b| (b - avg_bandwidth).powi(2))
            .sum::<f64>() / bandwidth_observations.len() as f64;
        let bandwidth_std_dev = bandwidth_variance.sqrt();

        println!("Bandwidth stability: std_dev={:.2} MB/s", bandwidth_std_dev / 1024.0 / 1024.0);

        // For a stable system, bandwidth shouldn't vary too wildly
        // Handle edge case where bandwidth is zero
        if avg_bandwidth > 0.0 {
            assert!(bandwidth_std_dev < avg_bandwidth * 2.0,
                "Bandwidth should be relatively stable: avg={:.2}, std_dev={:.2}",
                avg_bandwidth / 1024.0 / 1024.0, bandwidth_std_dev / 1024.0 / 1024.0);
        } else {
            // When average bandwidth is zero, standard deviation should also be zero or very small
            assert!(bandwidth_std_dev <= 1024.0, // Allow up to 1 KB/s variance for zero bandwidth
                "Zero bandwidth should have minimal variance: std_dev={:.2} KB/s",
                bandwidth_std_dev / 1024.0);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Bandwidth limit discovery through observation test passed");
    }

    println!("Network availability estimation with unknown bandwidth limits test passed");
}

// ================================================================================================
// Disk Monitor Lifecycle Tests (Continued)
// ================================================================================================

/// Test disk monitor initialization with multiple disks
#[tokio::test]
async fn test_disk_monitor_initialization_with_multiple_disks() {
    println!("Starting disk monitor initialization with multiple disks test");

    // Test 1: Default configuration initialization
    {
        println!("Testing DiskMonitor initialization with default configuration");

        let monitor = create_default_disk_monitor();

        // Import ResourceMonitor trait locally to access get_poll_interval
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;
        let poll_interval = monitor.get_poll_interval();
        assert_eq!(poll_interval.as_millis(), 5000, "Default poll interval should be 5000ms");

        println!("DiskMonitor default initialization test passed");
    }

    // Test 2: Custom configuration initialization with different intervals
    {
        println!("Testing DiskMonitor initialization with custom configurations");

        let custom_intervals = vec![500, 1000, 2000, 5000, 10000];

        // Import ResourceMonitor trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        for (i, interval) in custom_intervals.into_iter().enumerate() {
            println!("Testing custom config {}: poll_interval_ms = {}", i + 1, interval);

            let monitor = create_custom_disk_monitor(interval);
            let poll_interval = monitor.get_poll_interval();

            assert_eq!(poll_interval.as_millis(), interval as u128,
                "Custom poll interval should be preserved for config {}", i + 1);

            println!("Custom config {} passed", i + 1);
        }

        println!("DiskMonitor custom initialization test passed");
    }

    // Test 3: Multiple disk detection during initialization
    {
        println!("Testing multiple disk detection during initialization");

        let mut monitor = create_default_disk_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, DiskMonitoring};

        // Start monitoring to trigger disk detection
        monitor.start().await.expect("Disk monitor should start");

        // Allow time for disk detection
        sleep(Duration::from_millis(300)).await;

        // Get disk metrics to verify multiple disk detection
        let disk_metrics = monitor.get_disk_metrics().await;
        assert!(disk_metrics.is_ok(), "Should get disk metrics successfully");

        let metrics = disk_metrics.unwrap();
        println!("Detected {} disk(s) during initialization", metrics.disks.len());

        // Verify each detected disk has valid properties
        for (mount_point, disk_info) in &metrics.disks {
            println!("Disk {}: {} bytes total, {} bytes available",
                mount_point, disk_info.total_bytes, disk_info.available_bytes);

            assert!(disk_info.total_bytes > 0, "Total bytes should be positive for disk {}", mount_point);
            assert!(disk_info.available_bytes <= disk_info.total_bytes,
                "Available bytes should not exceed total for disk {}", mount_point);
            assert!(disk_info.usage_percent >= 0.0 && disk_info.usage_percent <= 100.0,
                "Usage percent should be 0-100% for disk {}: {}", mount_point, disk_info.usage_percent);

            // Verify disk type is detected
            if let Some(disk_type) = &disk_info.disk_type {
                assert!(!disk_type.is_empty(), "Disk type should not be empty for disk {}", mount_point);
                println!("Disk {} type: {}", mount_point, disk_type);
            }
        }

        monitor.stop().await.expect("Disk monitor should stop");
        println!("Multiple disk detection test passed");
    }

    // Test 4: Configuration validation during initialization
    {
        println!("Testing configuration validation during initialization");

        // Test edge case configurations
        let edge_configs = vec![
            (0, "Zero poll interval"),
            (1, "Minimum poll interval"),
            (100, "Very short interval"),
            (60000, "One minute interval"),
            (u64::MAX, "Maximum poll interval"),
        ];

        // Import ResourceMonitor trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        for (i, (interval, description)) in edge_configs.into_iter().enumerate() {
            println!("Testing edge config {}: {} ({}ms)", i + 1, description, interval);

            let monitor = create_custom_disk_monitor(interval);
            let poll_interval = monitor.get_poll_interval();

            assert_eq!(poll_interval.as_millis(), interval as u128,
                "Edge case poll interval should be preserved for {}", description);

            println!("Edge config {} ({}) passed", i + 1, description);
        }

        println!("Configuration validation during initialization test passed");
    }

    // Test 5: Resource type verification
    {
        println!("Testing resource type verification");

        let monitor = create_default_disk_monitor();

        // Import ResourceMonitor trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;
        use prisma_ai::prisma::prisma_engine::types::ResourceType;

        let resource_type = monitor.get_resource_type();
        assert_eq!(resource_type, ResourceType::DiskIO, "Resource type should be DiskIO");

        println!("Resource type verification test passed");
    }

    println!("Disk monitor initialization with multiple disks test passed");
}

/// Test disk monitoring task management
#[tokio::test]
async fn test_disk_monitoring_task_management() {
    println!("Starting disk monitoring task management test");

    // Test 1: Start monitoring task
    {
        println!("Testing disk monitoring task start");

        let mut monitor = create_default_disk_monitor();

        // Import the trait locally to avoid conflicts
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        let start_result = monitor.start().await;
        assert!(start_result.is_ok(), "Disk monitor should start successfully");

        // Allow time for monitoring task to initialize
        sleep(Duration::from_millis(200)).await;

        // Stop the monitor
        let stop_result = monitor.stop().await;
        assert!(stop_result.is_ok(), "Disk monitor should stop successfully");

        println!("Disk monitoring task start test passed");
    }

    // Test 2: Multiple start/stop cycles
    {
        println!("Testing multiple disk monitoring start/stop cycles");

        let mut monitor = create_default_disk_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        for cycle in 1..=5 {
            println!("Disk monitoring cycle {}", cycle);

            let start_result = monitor.start().await;
            assert!(start_result.is_ok(), "Disk monitor should start in cycle {}", cycle);

            // Brief monitoring period
            sleep(Duration::from_millis(100)).await;

            let stop_result = monitor.stop().await;
            assert!(stop_result.is_ok(), "Disk monitor should stop in cycle {}", cycle);

            // Brief pause between cycles
            sleep(Duration::from_millis(50)).await;
        }

        println!("Multiple disk monitoring start/stop cycles test passed");
    }

    // Test 3: Idempotent operations
    {
        println!("Testing idempotent disk monitoring operations");

        let mut monitor = create_default_disk_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Multiple starts should be idempotent
        monitor.start().await.expect("First start should succeed");
        let second_start = monitor.start().await;
        assert!(second_start.is_ok(), "Second start should be idempotent");

        // Multiple stops should be idempotent
        monitor.stop().await.expect("First stop should succeed");
        let second_stop = monitor.stop().await;
        assert!(second_stop.is_ok(), "Second stop should be idempotent");

        println!("Idempotent disk monitoring operations test passed");
    }

    // Test 4: Task management with different poll intervals
    {
        println!("Testing task management with different poll intervals");

        let poll_intervals = vec![100, 500, 1000, 2000];

        for (_i, interval) in poll_intervals.into_iter().enumerate() {
            println!("Testing task management with poll interval: {}ms", interval);

            let mut monitor = create_custom_disk_monitor(interval);

            // Import the trait locally
            use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

            // Start monitoring
            let start_result = monitor.start().await;
            assert!(start_result.is_ok(), "Monitor should start with interval {}", interval);

            // Allow monitoring to run for a period
            sleep(Duration::from_millis(300)).await;

            // Stop monitoring
            let stop_result = monitor.stop().await;
            assert!(stop_result.is_ok(), "Monitor should stop with interval {}", interval);

            println!("Poll interval {} test passed", interval);
        }

        println!("Task management with different poll intervals test passed");
    }

    // Test 5: Concurrent task management
    {
        println!("Testing concurrent disk monitoring task management");

        let mut monitors = Vec::new();
        for i in 0..3 {
            let config = SystemMonitorConfig {
                poll_interval_ms: 1000 + (i * 200),
                monitor_cpu: true,
                monitor_memory: true,
                monitor_disk: true,
                monitor_network: true,
            };
            monitors.push(DiskMonitor::new(config));
        }

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Start all monitors concurrently
        for (i, monitor) in monitors.iter_mut().enumerate() {
            let start_result = monitor.start().await;
            assert!(start_result.is_ok(), "Monitor {} should start", i);
        }

        // Allow concurrent monitoring
        sleep(Duration::from_millis(400)).await;

        // Stop all monitors
        for (i, monitor) in monitors.iter_mut().enumerate() {
            let stop_result = monitor.stop().await;
            assert!(stop_result.is_ok(), "Monitor {} should stop", i);
        }

        println!("Concurrent task management test passed");
    }

    // Test 6: Task lifecycle during rapid start/stop operations
    {
        println!("Testing task lifecycle during rapid start/stop operations");

        let mut monitor = create_default_disk_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Perform rapid start/stop operations
        for i in 0..10 {
            monitor.start().await.expect(&format!("Rapid start {} should succeed", i));
            sleep(Duration::from_millis(20)).await;
            monitor.stop().await.expect(&format!("Rapid stop {} should succeed", i));
            sleep(Duration::from_millis(10)).await;
        }

        println!("Rapid start/stop operations test passed");
    }

    println!("Disk monitoring task management test passed");
}

/// Test disk monitor configuration updates
#[tokio::test]
async fn test_disk_monitor_configuration_updates() {
    println!("Starting disk monitor configuration updates test");

    // Test 1: Runtime poll interval updates
    {
        println!("Testing runtime poll interval updates");

        let mut monitor = create_default_disk_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Verify initial poll interval
        let initial_interval = monitor.get_poll_interval();
        assert_eq!(initial_interval.as_millis(), 5000, "Initial poll interval should be 5000ms");

        // Update poll interval
        let new_interval = Duration::from_millis(2000);
        monitor.set_poll_interval(new_interval);

        // Verify updated poll interval
        let updated_interval = monitor.get_poll_interval();
        assert_eq!(updated_interval.as_millis(), 2000, "Updated poll interval should be 2000ms");

        println!("Runtime poll interval updates test passed");
    }

    // Test 2: Configuration updates during monitoring
    {
        println!("Testing configuration updates during monitoring");

        let mut monitor = create_default_disk_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Start monitoring
        monitor.start().await.expect("Monitor should start");
        sleep(Duration::from_millis(200)).await;

        // Update configuration while monitoring
        let new_intervals = vec![500, 1000, 1500, 3000];

        for (i, interval_ms) in new_intervals.into_iter().enumerate() {
            println!("Updating to interval {} during monitoring: {}ms", i + 1, interval_ms);

            let new_interval = Duration::from_millis(interval_ms);
            monitor.set_poll_interval(new_interval);

            // Verify the update
            let current_interval = monitor.get_poll_interval();
            assert_eq!(current_interval.as_millis(), interval_ms as u128,
                "Poll interval should be updated to {}ms", interval_ms);

            // Allow monitoring to continue with new interval
            sleep(Duration::from_millis(300)).await;
        }

        monitor.stop().await.expect("Monitor should stop");
        println!("Configuration updates during monitoring test passed");
    }

    // Test 3: Configuration validation during updates
    {
        println!("Testing configuration validation during updates");

        let mut monitor = create_default_disk_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Test various edge case configurations
        let edge_intervals = vec![
            (0, "Zero interval"),
            (1, "Minimum interval"),
            (50, "Very short interval"),
            (100000, "Very long interval"),
            (u64::MAX, "Maximum interval"),
        ];

        for (interval_ms, description) in edge_intervals {
            println!("Testing configuration validation: {} ({}ms)", description, interval_ms);

            let new_interval = Duration::from_millis(interval_ms);
            monitor.set_poll_interval(new_interval);

            let current_interval = monitor.get_poll_interval();
            assert_eq!(current_interval.as_millis(), interval_ms as u128,
                "Configuration should accept {}: {}ms", description, interval_ms);

            println!("Configuration validation for {} passed", description);
        }

        println!("Configuration validation during updates test passed");
    }

    // Test 4: Configuration persistence across start/stop cycles
    {
        println!("Testing configuration persistence across start/stop cycles");

        let mut monitor = create_default_disk_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Set a custom configuration
        let custom_interval = Duration::from_millis(1500);
        monitor.set_poll_interval(custom_interval);

        // Verify configuration before starting
        let pre_start_interval = monitor.get_poll_interval();
        assert_eq!(pre_start_interval.as_millis(), 1500, "Configuration should persist before start");

        // Start and stop monitoring multiple times
        for cycle in 1..=3 {
            println!("Testing configuration persistence in cycle {}", cycle);

            monitor.start().await.expect(&format!("Monitor should start in cycle {}", cycle));
            sleep(Duration::from_millis(200)).await;

            // Verify configuration during monitoring
            let during_monitoring_interval = monitor.get_poll_interval();
            assert_eq!(during_monitoring_interval.as_millis(), 1500,
                "Configuration should persist during monitoring in cycle {}", cycle);

            monitor.stop().await.expect(&format!("Monitor should stop in cycle {}", cycle));

            // Verify configuration after stopping
            let post_stop_interval = monitor.get_poll_interval();
            assert_eq!(post_stop_interval.as_millis(), 1500,
                "Configuration should persist after stop in cycle {}", cycle);

            sleep(Duration::from_millis(100)).await;
        }

        println!("Configuration persistence test passed");
    }

    // Test 5: Multiple configuration updates in sequence
    {
        println!("Testing multiple configuration updates in sequence");

        let mut monitor = create_default_disk_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Perform rapid configuration updates
        let intervals = vec![1000, 2000, 500, 3000, 1500, 800, 2500];

        for (i, interval_ms) in intervals.into_iter().enumerate() {
            println!("Sequential update {}: {}ms", i + 1, interval_ms);

            let new_interval = Duration::from_millis(interval_ms);
            monitor.set_poll_interval(new_interval);

            let current_interval = monitor.get_poll_interval();
            assert_eq!(current_interval.as_millis(), interval_ms as u128,
                "Sequential update {} should be applied: {}ms", i + 1, interval_ms);
        }

        // Verify final configuration is preserved
        let final_interval = monitor.get_poll_interval();
        assert_eq!(final_interval.as_millis(), 2500, "Final configuration should be 2500ms");

        println!("Multiple configuration updates in sequence test passed");
    }

    // Test 6: Configuration updates with concurrent monitoring
    {
        println!("Testing configuration updates with concurrent monitoring");

        let mut monitor = create_default_disk_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Start monitoring
        monitor.start().await.expect("Monitor should start");

        // Perform configuration updates while monitoring is active
        let update_intervals = vec![800, 1200, 600, 1800, 1000];

        for (i, interval_ms) in update_intervals.into_iter().enumerate() {
            println!("Concurrent update {}: {}ms", i + 1, interval_ms);

            let new_interval = Duration::from_millis(interval_ms);
            monitor.set_poll_interval(new_interval);

            // Allow some monitoring activity with new configuration
            sleep(Duration::from_millis(250)).await;

            let current_interval = monitor.get_poll_interval();
            assert_eq!(current_interval.as_millis(), interval_ms as u128,
                "Concurrent update {} should be applied: {}ms", i + 1, interval_ms);
        }

        monitor.stop().await.expect("Monitor should stop");
        println!("Configuration updates with concurrent monitoring test passed");
    }

    println!("Disk monitor configuration updates test passed");
}

/// Test disk monitor error handling and recovery
#[tokio::test]
async fn test_disk_monitor_error_handling_and_recovery() {
    println!("Starting disk monitor error handling and recovery test");

    // Test 1: Error recovery during monitoring
    {
        println!("Testing disk monitor error recovery during monitoring");

        let mut monitor = create_default_disk_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Start monitoring
        monitor.start().await.expect("Monitor should start");

        // Allow some monitoring activity
        sleep(Duration::from_millis(300)).await;

        // Monitor should continue to function despite any internal errors
        // (The implementation should handle errors gracefully)
        // Verify monitor is still responsive
        let resource_type = monitor.get_resource_type();
        use prisma_ai::prisma::prisma_engine::types::ResourceType;
        assert_eq!(resource_type, ResourceType::DiskIO, "Monitor should remain responsive");

        // Stop monitoring
        monitor.stop().await.expect("Monitor should stop");

        println!("Disk monitor error recovery test passed");
    }

    // Test 2: Graceful degradation under system stress
    {
        println!("Testing graceful degradation under system stress");

        let mut monitor = create_custom_disk_monitor(100); // Very short interval

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, DiskMonitoring};

        monitor.start().await.expect("Monitor should start under stress");

        // Allow monitoring under stress conditions
        sleep(Duration::from_millis(500)).await;

        // Verify monitor continues to provide metrics
        let availability_result = monitor.get_availability().await;
        assert!(availability_result.is_ok(), "Monitor should provide availability under stress");

        let metrics_result = monitor.get_disk_metrics().await;
        assert!(metrics_result.is_ok(), "Monitor should provide metrics under stress");

        monitor.stop().await.expect("Monitor should stop gracefully under stress");

        println!("Graceful degradation under system stress test passed");
    }

    // Test 3: Resource cleanup on errors
    {
        println!("Testing resource cleanup on errors");

        let mut monitor = create_default_disk_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Start and stop monitoring multiple times to test cleanup
        for cycle in 1..=5 {
            println!("Testing resource cleanup cycle {}", cycle);

            monitor.start().await.expect(&format!("Monitor should start in cycle {}", cycle));
            sleep(Duration::from_millis(100)).await;

            // Abrupt stop to test cleanup
            monitor.stop().await.expect(&format!("Monitor should cleanup in cycle {}", cycle));

            // Brief pause to allow cleanup
            sleep(Duration::from_millis(50)).await;

            // Verify monitor can be restarted after cleanup
            let restart_result = monitor.start().await;
            assert!(restart_result.is_ok(), "Monitor should restart after cleanup in cycle {}", cycle);

            monitor.stop().await.expect(&format!("Monitor should stop after restart in cycle {}", cycle));
        }

        println!("Resource cleanup on errors test passed");
    }

    // Test 4: Monitoring continuation after configuration errors
    {
        println!("Testing monitoring continuation after configuration errors");

        let mut monitor = create_default_disk_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        monitor.start().await.expect("Monitor should start");

        // Apply potentially problematic configurations
        let problematic_configs = vec![
            (0, "Zero interval"),
            (u64::MAX, "Maximum interval"),
            (1, "Minimum interval"),
        ];

        for (interval_ms, description) in problematic_configs {
            println!("Testing continuation after {}: {}ms", description, interval_ms);

            let new_interval = Duration::from_millis(interval_ms);
            monitor.set_poll_interval(new_interval);

            // Allow monitoring to continue
            sleep(Duration::from_millis(200)).await;

            // Verify monitor is still functional
            let current_interval = monitor.get_poll_interval();
            assert_eq!(current_interval.as_millis(), interval_ms as u128,
                "Monitor should accept {} configuration", description);

            println!("Continuation after {} test passed", description);
        }

        monitor.stop().await.expect("Monitor should stop");
        println!("Monitoring continuation after configuration errors test passed");
    }

    // Test 5: Error handling during rapid operations
    {
        println!("Testing error handling during rapid operations");

        let mut monitor = create_default_disk_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Perform rapid start/stop/configure operations
        for i in 0..10 {
            // Rapid start
            monitor.start().await.expect(&format!("Rapid start {} should succeed", i));

            // Rapid configuration change
            let interval = Duration::from_millis(100 + (i * 50));
            monitor.set_poll_interval(interval);

            // Brief monitoring
            sleep(Duration::from_millis(30)).await;

            // Rapid stop
            monitor.stop().await.expect(&format!("Rapid stop {} should succeed", i));

            // Verify configuration persisted
            let current_interval = monitor.get_poll_interval();
            assert_eq!(current_interval.as_millis(), (100 + (i * 50)) as u128,
                "Configuration should persist after rapid operation {}", i);
        }

        println!("Error handling during rapid operations test passed");
    }

    // Test 6: Recovery from monitoring task failures
    {
        println!("Testing recovery from monitoring task failures");

        let mut monitor = create_default_disk_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, DiskMonitoring};

        // Start monitoring
        monitor.start().await.expect("Monitor should start");
        sleep(Duration::from_millis(200)).await;

        // Verify initial functionality
        let initial_metrics = monitor.get_disk_metrics().await;
        assert!(initial_metrics.is_ok(), "Initial metrics should be available");

        // Simulate recovery by stopping and restarting
        monitor.stop().await.expect("Monitor should stop for recovery");
        sleep(Duration::from_millis(100)).await;

        // Restart and verify recovery
        monitor.start().await.expect("Monitor should restart after recovery");
        sleep(Duration::from_millis(200)).await;

        // Verify functionality after recovery
        let recovery_metrics = monitor.get_disk_metrics().await;
        assert!(recovery_metrics.is_ok(), "Metrics should be available after recovery");

        let recovery_availability = monitor.get_availability().await;
        assert!(recovery_availability.is_ok(), "Availability should be available after recovery");

        monitor.stop().await.expect("Monitor should stop after recovery test");

        println!("Recovery from monitoring task failures test passed");
    }

    // Test 7: Error handling with concurrent monitors
    {
        println!("Testing error handling with concurrent monitors");

        let mut monitors = Vec::new();
        for i in 0..3 {
            let config = SystemMonitorConfig {
                poll_interval_ms: 500 + (i * 100),
                monitor_cpu: true,
                monitor_memory: true,
                monitor_disk: true,
                monitor_network: true,
            };
            monitors.push(DiskMonitor::new(config));
        }

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Start all monitors
        for (i, monitor) in monitors.iter_mut().enumerate() {
            monitor.start().await.expect(&format!("Monitor {} should start", i));
        }

        // Allow concurrent monitoring
        sleep(Duration::from_millis(400)).await;

        // Simulate error conditions by rapid configuration changes
        for (i, monitor) in monitors.iter_mut().enumerate() {
            let new_interval = Duration::from_millis(200 + (i as u64 * 150));
            monitor.set_poll_interval(new_interval);
        }

        // Allow monitoring to continue
        sleep(Duration::from_millis(300)).await;

        // Stop all monitors and verify cleanup
        for (i, monitor) in monitors.iter_mut().enumerate() {
            monitor.stop().await.expect(&format!("Monitor {} should stop cleanly", i));
        }

        println!("Error handling with concurrent monitors test passed");
    }

    println!("Disk monitor error handling and recovery test passed");
}

// ================================================================================================
// Network Metrics Collection Tests
// ================================================================================================

/// Test network interface discovery and monitoring
#[tokio::test]
async fn test_network_interface_discovery_and_monitoring() {
    println!("Starting network interface discovery and monitoring test");

    // Test 1: Network interface detection and enumeration
    {
        println!("Testing network interface detection and enumeration");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(500)).await;

        // Get network interfaces
        let interfaces_result = monitor.get_network_interfaces().await;
        assert!(interfaces_result.is_ok(), "Should get network interfaces successfully");

        let interfaces = interfaces_result.unwrap();
        println!("Detected {} network interface(s)", interfaces.len());

        // Verify we have at least one interface (loopback should always exist)
        assert!(!interfaces.is_empty(), "Should detect at least one network interface");

        // Verify interface names are valid
        for interface_name in &interfaces {
            assert!(!interface_name.is_empty(), "Interface name should not be empty");
            assert!(!interface_name.contains('\0'), "Interface name should not contain null bytes");
            println!("  Interface: {}", interface_name);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Network interface detection and enumeration test passed");
    }

    // Test 2: Interface properties validation
    {
        println!("Testing interface properties validation");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(400)).await;

        // Get detailed network metrics
        let metrics_result = monitor.get_network_metrics().await;
        assert!(metrics_result.is_ok(), "Should get network metrics successfully");

        let metrics = metrics_result.unwrap();
        println!("Network metrics contain {} interface(s)", metrics.interfaces.len());

        // Verify interface properties
        for (interface_name, interface_info) in &metrics.interfaces {
            println!("  Interface {}: {}", interface_name, interface_info.name);

            // Verify basic properties
            assert_eq!(interface_info.name, *interface_name, "Interface name should match key");
            assert!(!interface_info.name.is_empty(), "Interface name should not be empty");

            // Verify MAC address format if present
            if let Some(mac_address) = &interface_info.mac_address {
                assert!(!mac_address.is_empty(), "MAC address should not be empty if present");
                println!("    MAC: {}", mac_address);
            }

            // Verify IP addresses are valid if present
            for ip_address in &interface_info.ip_addresses {
                assert!(!ip_address.is_empty(), "IP address should not be empty");
                println!("    IP: {}", ip_address);
            }

            // Verify error counters are non-negative
            assert!(interface_info.rx_errors >= 0, "RX errors should be non-negative");
            assert!(interface_info.tx_errors >= 0, "TX errors should be non-negative");

            println!("    RX errors: {}, TX errors: {}", interface_info.rx_errors, interface_info.tx_errors);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Interface properties validation test passed");
    }

    // Test 3: Interface filtering and validation
    {
        println!("Testing interface filtering and validation");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(300)).await;

        let interfaces = monitor.get_network_interfaces().await.unwrap();
        let metrics = monitor.get_network_metrics().await.unwrap();

        // Verify consistency between interface list and metrics
        assert_eq!(interfaces.len(), metrics.interfaces.len(),
            "Interface list and metrics should have same count");

        for interface_name in &interfaces {
            assert!(metrics.interfaces.contains_key(interface_name),
                "Interface {} should be present in metrics", interface_name);
        }

        // Test interface filtering scenarios
        let active_interfaces: Vec<_> = metrics.interfaces.iter()
            .filter(|(_, info)| info.is_up)
            .collect();

        let interfaces_with_traffic: Vec<_> = metrics.interfaces.iter()
            .filter(|(_, info)| info.rx_bytes_per_sec > 0.0 || info.tx_bytes_per_sec > 0.0)
            .collect();

        let interfaces_with_errors: Vec<_> = metrics.interfaces.iter()
            .filter(|(_, info)| info.rx_errors > 0 || info.tx_errors > 0)
            .collect();

        println!("  Active interfaces: {}", active_interfaces.len());
        println!("  Interfaces with traffic: {}", interfaces_with_traffic.len());
        println!("  Interfaces with errors: {}", interfaces_with_errors.len());

        // Verify filtering results are reasonable
        assert!(active_interfaces.len() <= interfaces.len(),
            "Active interfaces should not exceed total interfaces");
        assert!(interfaces_with_traffic.len() <= interfaces.len(),
            "Interfaces with traffic should not exceed total interfaces");
        assert!(interfaces_with_errors.len() <= interfaces.len(),
            "Interfaces with errors should not exceed total interfaces");

        monitor.stop().await.expect("Network monitor should stop");
        println!("Interface filtering and validation test passed");
    }

    // Test 4: Interface discovery consistency across multiple polls
    {
        println!("Testing interface discovery consistency across multiple polls");

        let mut monitor = create_custom_network_monitor(200); // Fast polling

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");

        // Collect interface lists across multiple polls
        let mut interface_lists = Vec::new();
        for poll in 1..=5 {
            sleep(Duration::from_millis(250)).await;
            let interfaces = monitor.get_network_interfaces().await.unwrap();
            interface_lists.push(interfaces);
            println!("  Poll {}: {} interfaces", poll, interface_lists.last().unwrap().len());
        }

        // Verify consistency across polls
        let first_list = &interface_lists[0];
        for (i, interface_list) in interface_lists.iter().enumerate().skip(1) {
            assert_eq!(interface_list.len(), first_list.len(),
                "Interface count should be consistent across polls (poll {})", i + 1);

            // Verify same interfaces are present (order may vary)
            for interface_name in first_list {
                assert!(interface_list.contains(interface_name),
                    "Interface {} should be present in all polls", interface_name);
            }
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Interface discovery consistency test passed");
    }

    println!("Network interface discovery and monitoring test passed");
}

/// Test bandwidth usage calculation (RX/TX bytes per second)
#[tokio::test]
async fn test_bandwidth_usage_calculation() {
    println!("Starting bandwidth usage calculation test");

    // Test 1: RX/TX bytes per second calculation
    {
        println!("Testing RX/TX bytes per second calculation");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(600)).await; // Allow time for initial measurement

        // Get bandwidth usage
        let bandwidth_result = monitor.get_network_bandwidth_usage().await;
        assert!(bandwidth_result.is_ok(), "Should get bandwidth usage successfully");

        let (rx_bytes_per_sec, tx_bytes_per_sec) = bandwidth_result.unwrap();
        println!("Total bandwidth: RX={:.2} bytes/sec, TX={:.2} bytes/sec", rx_bytes_per_sec, tx_bytes_per_sec);

        // Verify bandwidth values are non-negative
        assert!(rx_bytes_per_sec >= 0.0, "RX bytes per second should be non-negative: {}", rx_bytes_per_sec);
        assert!(tx_bytes_per_sec >= 0.0, "TX bytes per second should be non-negative: {}", tx_bytes_per_sec);

        // Get detailed metrics to verify per-interface calculations
        let metrics = monitor.get_network_metrics().await.unwrap();
        let mut calculated_rx_total = 0.0;
        let mut calculated_tx_total = 0.0;

        for (interface_name, interface_info) in &metrics.interfaces {
            println!("  Interface {}: RX={:.2} bytes/sec, TX={:.2} bytes/sec",
                interface_name, interface_info.rx_bytes_per_sec, interface_info.tx_bytes_per_sec);

            // Verify per-interface values are non-negative
            assert!(interface_info.rx_bytes_per_sec >= 0.0,
                "Interface {} RX bytes per second should be non-negative", interface_name);
            assert!(interface_info.tx_bytes_per_sec >= 0.0,
                "Interface {} TX bytes per second should be non-negative", interface_name);

            calculated_rx_total += interface_info.rx_bytes_per_sec;
            calculated_tx_total += interface_info.tx_bytes_per_sec;
        }

        // Verify total bandwidth matches sum of interfaces
        let rx_diff = (rx_bytes_per_sec - calculated_rx_total).abs();
        let tx_diff = (tx_bytes_per_sec - calculated_tx_total).abs();
        assert!(rx_diff < 0.01, "Total RX should match sum of interfaces: {} vs {}", rx_bytes_per_sec, calculated_rx_total);
        assert!(tx_diff < 0.01, "Total TX should match sum of interfaces: {} vs {}", tx_bytes_per_sec, calculated_tx_total);

        monitor.stop().await.expect("Network monitor should stop");
        println!("RX/TX bytes per second calculation test passed");
    }

    // Test 2: Total bandwidth aggregation across interfaces
    {
        println!("Testing total bandwidth aggregation across interfaces");

        let mut monitor = create_custom_network_monitor(300); // Faster polling for better accuracy

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(500)).await;

        // Collect multiple bandwidth measurements
        let mut bandwidth_measurements = Vec::new();
        for measurement in 1..=5 {
            sleep(Duration::from_millis(400)).await;
            let (rx, tx) = monitor.get_network_bandwidth_usage().await.unwrap();
            bandwidth_measurements.push((rx, tx));
            println!("  Measurement {}: RX={:.2} bytes/sec, TX={:.2} bytes/sec", measurement, rx, tx);
        }

        // Verify measurements are consistent (allowing for normal network variation)
        for (i, (rx, tx)) in bandwidth_measurements.iter().enumerate() {
            assert!(*rx >= 0.0, "Measurement {} RX should be non-negative: {}", i + 1, rx);
            assert!(*tx >= 0.0, "Measurement {} TX should be non-negative: {}", i + 1, tx);

            // Verify reasonable bounds (not exceeding typical network speeds)
            const MAX_REASONABLE_BANDWIDTH: f64 = 10.0 * 1024.0 * 1024.0 * 1024.0; // 10 GB/s
            assert!(*rx < MAX_REASONABLE_BANDWIDTH, "RX bandwidth seems unreasonably high: {}", rx);
            assert!(*tx < MAX_REASONABLE_BANDWIDTH, "TX bandwidth seems unreasonably high: {}", tx);
        }

        // Test aggregation consistency
        let metrics = monitor.get_network_metrics().await.unwrap();
        let total_rx = metrics.total_rx_bytes_per_sec;
        let total_tx = metrics.total_tx_bytes_per_sec;

        let interface_rx_sum: f64 = metrics.interfaces.values()
            .map(|info| info.rx_bytes_per_sec)
            .sum();
        let interface_tx_sum: f64 = metrics.interfaces.values()
            .map(|info| info.tx_bytes_per_sec)
            .sum();

        println!("  Aggregation check: Total RX={:.2}, Sum RX={:.2}", total_rx, interface_rx_sum);
        println!("  Aggregation check: Total TX={:.2}, Sum TX={:.2}", total_tx, interface_tx_sum);

        let rx_aggregation_diff = (total_rx - interface_rx_sum).abs();
        let tx_aggregation_diff = (total_tx - interface_tx_sum).abs();
        assert!(rx_aggregation_diff < 0.01, "RX aggregation should be accurate");
        assert!(tx_aggregation_diff < 0.01, "TX aggregation should be accurate");

        monitor.stop().await.expect("Network monitor should stop");
        println!("Total bandwidth aggregation test passed");
    }

    // Test 3: Bandwidth rate calculation accuracy
    {
        println!("Testing bandwidth rate calculation accuracy");

        let mut monitor = create_custom_network_monitor(200); // Very fast polling

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");

        // Allow initial stabilization
        sleep(Duration::from_millis(400)).await;

        // Collect baseline measurement
        let baseline_metrics = monitor.get_network_metrics().await.unwrap();
        let baseline_time = baseline_metrics.timestamp;

        // Wait for rate calculation period
        sleep(Duration::from_millis(800)).await;

        // Collect follow-up measurement
        let followup_metrics = monitor.get_network_metrics().await.unwrap();
        let followup_time = followup_metrics.timestamp;

        // Verify time progression
        let time_diff = followup_time.duration_since(baseline_time).unwrap();
        assert!(time_diff.as_millis() > 0, "Time should progress between measurements");
        println!("  Time between measurements: {}ms", time_diff.as_millis());

        // Verify rate calculations are reasonable
        for (interface_name, interface_info) in &followup_metrics.interfaces {
            let rx_rate = interface_info.rx_bytes_per_sec;
            let tx_rate = interface_info.tx_bytes_per_sec;

            println!("  Interface {}: RX rate={:.2} bytes/sec, TX rate={:.2} bytes/sec",
                interface_name, rx_rate, tx_rate);

            // Rates should be finite and non-negative
            assert!(rx_rate.is_finite(), "RX rate should be finite for interface {}", interface_name);
            assert!(tx_rate.is_finite(), "TX rate should be finite for interface {}", interface_name);
            assert!(rx_rate >= 0.0, "RX rate should be non-negative for interface {}", interface_name);
            assert!(tx_rate >= 0.0, "TX rate should be non-negative for interface {}", interface_name);

            // Rates should not be NaN
            assert!(!rx_rate.is_nan(), "RX rate should not be NaN for interface {}", interface_name);
            assert!(!tx_rate.is_nan(), "TX rate should not be NaN for interface {}", interface_name);
        }

        // Test rate calculation stability over multiple measurements
        let mut rate_measurements = Vec::new();
        for measurement in 1..=3 {
            sleep(Duration::from_millis(300)).await;
            let metrics = monitor.get_network_metrics().await.unwrap();
            let (total_rx, total_tx) = (metrics.total_rx_bytes_per_sec, metrics.total_tx_bytes_per_sec);
            rate_measurements.push((total_rx, total_tx));
            println!("  Rate measurement {}: RX={:.2}, TX={:.2}", measurement, total_rx, total_tx);
        }

        // Verify rate stability (rates should not fluctuate wildly)
        for (i, (rx, tx)) in rate_measurements.iter().enumerate() {
            assert!(rx.is_finite() && !rx.is_nan(), "Rate measurement {} RX should be valid", i + 1);
            assert!(tx.is_finite() && !tx.is_nan(), "Rate measurement {} TX should be valid", i + 1);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Bandwidth rate calculation accuracy test passed");
    }

    println!("Bandwidth usage calculation test passed");
}

/// Test packet rate monitoring (packets per second)
#[tokio::test]
async fn test_packet_rate_monitoring() {
    println!("Starting packet rate monitoring test");

    // Test 1: RX/TX packets per second calculation
    {
        println!("Testing RX/TX packets per second calculation");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(600)).await; // Allow time for initial measurement

        // Get network metrics to access packet rates
        let metrics_result = monitor.get_network_metrics().await;
        assert!(metrics_result.is_ok(), "Should get network metrics successfully");

        let metrics = metrics_result.unwrap();
        println!("Network metrics contain {} interface(s) for packet rate analysis", metrics.interfaces.len());

        // Verify packet rate calculations for each interface
        for (interface_name, interface_info) in &metrics.interfaces {
            let rx_packets_per_sec = interface_info.rx_packets_per_sec;
            let tx_packets_per_sec = interface_info.tx_packets_per_sec;

            println!("  Interface {}: RX={:.2} packets/sec, TX={:.2} packets/sec",
                interface_name, rx_packets_per_sec, tx_packets_per_sec);

            // Verify packet rates are non-negative
            assert!(rx_packets_per_sec >= 0.0,
                "Interface {} RX packets per second should be non-negative: {}", interface_name, rx_packets_per_sec);
            assert!(tx_packets_per_sec >= 0.0,
                "Interface {} TX packets per second should be non-negative: {}", interface_name, tx_packets_per_sec);

            // Verify packet rates are finite
            assert!(rx_packets_per_sec.is_finite(),
                "Interface {} RX packets per second should be finite", interface_name);
            assert!(tx_packets_per_sec.is_finite(),
                "Interface {} TX packets per second should be finite", interface_name);

            // Verify packet rates are not NaN
            assert!(!rx_packets_per_sec.is_nan(),
                "Interface {} RX packets per second should not be NaN", interface_name);
            assert!(!tx_packets_per_sec.is_nan(),
                "Interface {} TX packets per second should not be NaN", interface_name);

            // Verify reasonable bounds (not exceeding typical packet rates)
            const MAX_REASONABLE_PACKET_RATE: f64 = 10_000_000.0; // 10M packets/sec
            assert!(rx_packets_per_sec < MAX_REASONABLE_PACKET_RATE,
                "Interface {} RX packet rate seems unreasonably high: {}", interface_name, rx_packets_per_sec);
            assert!(tx_packets_per_sec < MAX_REASONABLE_PACKET_RATE,
                "Interface {} TX packet rate seems unreasonably high: {}", interface_name, tx_packets_per_sec);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("RX/TX packets per second calculation test passed");
    }

    // Test 2: Packet rate aggregation
    {
        println!("Testing packet rate aggregation");

        let mut monitor = create_custom_network_monitor(300); // Faster polling

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(500)).await;

        // Collect multiple packet rate measurements
        let mut packet_rate_measurements = Vec::new();
        for measurement in 1..=5 {
            sleep(Duration::from_millis(400)).await;
            let metrics = monitor.get_network_metrics().await.unwrap();

            // Calculate total packet rates across all interfaces
            let total_rx_packets: f64 = metrics.interfaces.values()
                .map(|info| info.rx_packets_per_sec)
                .sum();
            let total_tx_packets: f64 = metrics.interfaces.values()
                .map(|info| info.tx_packets_per_sec)
                .sum();

            packet_rate_measurements.push((total_rx_packets, total_tx_packets));
            println!("  Measurement {}: Total RX={:.2} packets/sec, Total TX={:.2} packets/sec",
                measurement, total_rx_packets, total_tx_packets);
        }

        // Verify aggregated packet rate measurements
        for (i, (total_rx, total_tx)) in packet_rate_measurements.iter().enumerate() {
            assert!(*total_rx >= 0.0, "Measurement {} total RX packets should be non-negative: {}", i + 1, total_rx);
            assert!(*total_tx >= 0.0, "Measurement {} total TX packets should be non-negative: {}", i + 1, total_tx);

            assert!(total_rx.is_finite(), "Measurement {} total RX packets should be finite", i + 1);
            assert!(total_tx.is_finite(), "Measurement {} total TX packets should be finite", i + 1);

            assert!(!total_rx.is_nan(), "Measurement {} total RX packets should not be NaN", i + 1);
            assert!(!total_tx.is_nan(), "Measurement {} total TX packets should not be NaN", i + 1);
        }

        // Test consistency of aggregation across measurements
        let metrics = monitor.get_network_metrics().await.unwrap();
        let interface_count = metrics.interfaces.len();
        println!("  Aggregating packet rates across {} interfaces", interface_count);

        let mut per_interface_rx_sum = 0.0;
        let mut per_interface_tx_sum = 0.0;

        for (interface_name, interface_info) in &metrics.interfaces {
            per_interface_rx_sum += interface_info.rx_packets_per_sec;
            per_interface_tx_sum += interface_info.tx_packets_per_sec;
            println!("    Interface {}: RX={:.2}, TX={:.2} packets/sec",
                interface_name, interface_info.rx_packets_per_sec, interface_info.tx_packets_per_sec);
        }

        println!("  Aggregated totals: RX={:.2}, TX={:.2} packets/sec", per_interface_rx_sum, per_interface_tx_sum);

        // Verify aggregation is mathematically consistent
        assert!(per_interface_rx_sum >= 0.0, "Aggregated RX packets should be non-negative");
        assert!(per_interface_tx_sum >= 0.0, "Aggregated TX packets should be non-negative");

        monitor.stop().await.expect("Network monitor should stop");
        println!("Packet rate aggregation test passed");
    }

    // Test 3: Packet rate accuracy over time
    {
        println!("Testing packet rate accuracy over time");

        let mut monitor = create_custom_network_monitor(200); // Very fast polling

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");

        // Allow initial stabilization
        sleep(Duration::from_millis(400)).await;

        // Collect baseline packet rate measurement
        let baseline_metrics = monitor.get_network_metrics().await.unwrap();
        let baseline_time = baseline_metrics.timestamp;

        // Wait for packet rate calculation period
        sleep(Duration::from_millis(800)).await;

        // Collect follow-up packet rate measurement
        let followup_metrics = monitor.get_network_metrics().await.unwrap();
        let followup_time = followup_metrics.timestamp;

        // Verify time progression for rate calculations
        let time_diff = followup_time.duration_since(baseline_time).unwrap();
        assert!(time_diff.as_millis() > 0, "Time should progress between packet rate measurements");
        println!("  Time between packet rate measurements: {}ms", time_diff.as_millis());

        // Verify packet rate calculations are reasonable over time
        for (interface_name, interface_info) in &followup_metrics.interfaces {
            let rx_packet_rate = interface_info.rx_packets_per_sec;
            let tx_packet_rate = interface_info.tx_packets_per_sec;

            println!("  Interface {}: RX packet rate={:.2} packets/sec, TX packet rate={:.2} packets/sec",
                interface_name, rx_packet_rate, tx_packet_rate);

            // Packet rates should be finite and non-negative
            assert!(rx_packet_rate.is_finite(), "RX packet rate should be finite for interface {}", interface_name);
            assert!(tx_packet_rate.is_finite(), "TX packet rate should be finite for interface {}", interface_name);
            assert!(rx_packet_rate >= 0.0, "RX packet rate should be non-negative for interface {}", interface_name);
            assert!(tx_packet_rate >= 0.0, "TX packet rate should be non-negative for interface {}", interface_name);

            // Packet rates should not be NaN
            assert!(!rx_packet_rate.is_nan(), "RX packet rate should not be NaN for interface {}", interface_name);
            assert!(!tx_packet_rate.is_nan(), "TX packet rate should not be NaN for interface {}", interface_name);
        }

        // Test packet rate stability over multiple measurements
        let mut packet_rate_measurements = Vec::new();
        for measurement in 1..=3 {
            sleep(Duration::from_millis(300)).await;
            let metrics = monitor.get_network_metrics().await.unwrap();

            // Calculate total packet rates
            let total_rx_packets: f64 = metrics.interfaces.values()
                .map(|info| info.rx_packets_per_sec)
                .sum();
            let total_tx_packets: f64 = metrics.interfaces.values()
                .map(|info| info.tx_packets_per_sec)
                .sum();

            packet_rate_measurements.push((total_rx_packets, total_tx_packets));
            println!("  Packet rate measurement {}: RX={:.2}, TX={:.2} packets/sec", measurement, total_rx_packets, total_tx_packets);
        }

        // Verify packet rate stability (rates should not fluctuate wildly)
        for (i, (rx_packets, tx_packets)) in packet_rate_measurements.iter().enumerate() {
            assert!(rx_packets.is_finite() && !rx_packets.is_nan(),
                "Packet rate measurement {} RX should be valid", i + 1);
            assert!(tx_packets.is_finite() && !tx_packets.is_nan(),
                "Packet rate measurement {} TX should be valid", i + 1);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Packet rate accuracy over time test passed");
    }

    println!("Packet rate monitoring test passed");
}

/// Test network error detection and reporting
#[tokio::test]
async fn test_network_error_detection_and_reporting() {
    println!("Starting network error detection and reporting test");

    // Test 1: RX/TX error counting
    {
        println!("Testing RX/TX error counting");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(600)).await; // Allow time for initial measurement

        // Get network metrics to access error counts
        let metrics_result = monitor.get_network_metrics().await;
        assert!(metrics_result.is_ok(), "Should get network metrics successfully");

        let metrics = metrics_result.unwrap();
        println!("Network metrics contain {} interface(s) for error analysis", metrics.interfaces.len());

        // Verify error counting for each interface
        for (interface_name, interface_info) in &metrics.interfaces {
            let rx_errors = interface_info.rx_errors;
            let tx_errors = interface_info.tx_errors;

            println!("  Interface {}: RX errors={}, TX errors={}",
                interface_name, rx_errors, tx_errors);

            // Verify error counts are non-negative
            assert!(rx_errors >= 0, "Interface {} RX errors should be non-negative: {}", interface_name, rx_errors);
            assert!(tx_errors >= 0, "Interface {} TX errors should be non-negative: {}", interface_name, tx_errors);

            // Verify error counts are reasonable (not exceeding typical bounds)
            const MAX_REASONABLE_ERROR_COUNT: u64 = 1_000_000; // 1M errors
            assert!(rx_errors < MAX_REASONABLE_ERROR_COUNT,
                "Interface {} RX error count seems unreasonably high: {}", interface_name, rx_errors);
            assert!(tx_errors < MAX_REASONABLE_ERROR_COUNT,
                "Interface {} TX error count seems unreasonably high: {}", interface_name, tx_errors);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("RX/TX error counting test passed");
    }

    // Test 2: Error rate calculation
    {
        println!("Testing error rate calculation");

        let mut monitor = create_custom_network_monitor(300); // Faster polling

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(500)).await;

        // Collect baseline error measurements
        let baseline_metrics = monitor.get_network_metrics().await.unwrap();
        let baseline_time = baseline_metrics.timestamp;

        // Wait for error rate calculation period
        sleep(Duration::from_millis(800)).await;

        // Collect follow-up error measurements
        let followup_metrics = monitor.get_network_metrics().await.unwrap();
        let followup_time = followup_metrics.timestamp;

        // Calculate time difference for rate calculations
        let time_diff = followup_time.duration_since(baseline_time).unwrap();
        let time_diff_secs = time_diff.as_secs_f64();
        assert!(time_diff_secs > 0.0, "Time should progress between error measurements");
        println!("  Time between error measurements: {:.2} seconds", time_diff_secs);

        // Calculate and verify error rates for each interface
        for (interface_name, followup_info) in &followup_metrics.interfaces {
            if let Some(baseline_info) = baseline_metrics.interfaces.get(interface_name) {
                // Calculate error rate (errors per second)
                let rx_error_diff = followup_info.rx_errors.saturating_sub(baseline_info.rx_errors);
                let tx_error_diff = followup_info.tx_errors.saturating_sub(baseline_info.tx_errors);

                let rx_error_rate = rx_error_diff as f64 / time_diff_secs;
                let tx_error_rate = tx_error_diff as f64 / time_diff_secs;

                println!("  Interface {}: RX error rate={:.2} errors/sec, TX error rate={:.2} errors/sec",
                    interface_name, rx_error_rate, tx_error_rate);

                // Verify error rates are non-negative and finite
                assert!(rx_error_rate >= 0.0, "RX error rate should be non-negative for interface {}", interface_name);
                assert!(tx_error_rate >= 0.0, "TX error rate should be non-negative for interface {}", interface_name);
                assert!(rx_error_rate.is_finite(), "RX error rate should be finite for interface {}", interface_name);
                assert!(tx_error_rate.is_finite(), "TX error rate should be finite for interface {}", interface_name);

                // Verify error rates are not NaN
                assert!(!rx_error_rate.is_nan(), "RX error rate should not be NaN for interface {}", interface_name);
                assert!(!tx_error_rate.is_nan(), "TX error rate should not be NaN for interface {}", interface_name);

                // Verify reasonable error rate bounds
                const MAX_REASONABLE_ERROR_RATE: f64 = 1000.0; // 1000 errors/sec
                assert!(rx_error_rate < MAX_REASONABLE_ERROR_RATE,
                    "Interface {} RX error rate seems unreasonably high: {}", interface_name, rx_error_rate);
                assert!(tx_error_rate < MAX_REASONABLE_ERROR_RATE,
                    "Interface {} TX error rate seems unreasonably high: {}", interface_name, tx_error_rate);
            }
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Error rate calculation test passed");
    }

    // Test 3: Error reporting and thresholds
    {
        println!("Testing error reporting and thresholds");

        let mut monitor = create_custom_network_monitor(200); // Fast polling for error detection

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(400)).await;

        // Collect multiple error measurements to analyze trends
        let mut error_measurements = Vec::new();
        for measurement in 1..=5 {
            sleep(Duration::from_millis(300)).await;
            let metrics = monitor.get_network_metrics().await.unwrap();

            // Calculate total errors across all interfaces
            let total_rx_errors: u64 = metrics.interfaces.values()
                .map(|info| info.rx_errors)
                .sum();
            let total_tx_errors: u64 = metrics.interfaces.values()
                .map(|info| info.tx_errors)
                .sum();

            error_measurements.push((total_rx_errors, total_tx_errors));
            println!("  Measurement {}: Total RX errors={}, Total TX errors={}",
                measurement, total_rx_errors, total_tx_errors);
        }

        // Analyze error trends and thresholds
        for (i, (total_rx, total_tx)) in error_measurements.iter().enumerate() {
            // Verify error counts are reasonable
            assert!(*total_rx >= 0, "Measurement {} total RX errors should be non-negative: {}", i + 1, total_rx);
            assert!(*total_tx >= 0, "Measurement {} total TX errors should be non-negative: {}", i + 1, total_tx);

            // Define error thresholds for reporting
            const LOW_ERROR_THRESHOLD: u64 = 10;
            const MEDIUM_ERROR_THRESHOLD: u64 = 100;
            const HIGH_ERROR_THRESHOLD: u64 = 1000;

            let rx_error_level = if *total_rx == 0 {
                "None"
            } else if *total_rx < LOW_ERROR_THRESHOLD {
                "Low"
            } else if *total_rx < MEDIUM_ERROR_THRESHOLD {
                "Medium"
            } else if *total_rx < HIGH_ERROR_THRESHOLD {
                "High"
            } else {
                "Critical"
            };

            let tx_error_level = if *total_tx == 0 {
                "None"
            } else if *total_tx < LOW_ERROR_THRESHOLD {
                "Low"
            } else if *total_tx < MEDIUM_ERROR_THRESHOLD {
                "Medium"
            } else if *total_tx < HIGH_ERROR_THRESHOLD {
                "High"
            } else {
                "Critical"
            };

            println!("    Error levels - RX: {} ({} errors), TX: {} ({} errors)",
                rx_error_level, total_rx, tx_error_level, total_tx);

            // Verify error levels are reasonable (most systems should have low error rates)
            assert!(*total_rx < 10000, "Total RX errors seem excessive: {}", total_rx);
            assert!(*total_tx < 10000, "Total TX errors seem excessive: {}", total_tx);
        }

        // Test error reporting consistency
        let final_metrics = monitor.get_network_metrics().await.unwrap();
        let mut interfaces_with_errors = 0;
        let mut total_error_count = 0;

        for (interface_name, interface_info) in &final_metrics.interfaces {
            let interface_total_errors = interface_info.rx_errors + interface_info.tx_errors;
            total_error_count += interface_total_errors;

            if interface_total_errors > 0 {
                interfaces_with_errors += 1;
                println!("  Interface {} has {} total errors", interface_name, interface_total_errors);
            }
        }

        println!("  Summary: {} interface(s) with errors, {} total errors across all interfaces",
            interfaces_with_errors, total_error_count);

        // Verify error reporting consistency
        assert!(interfaces_with_errors <= final_metrics.interfaces.len(),
            "Interfaces with errors should not exceed total interfaces");
        assert!(total_error_count >= 0, "Total error count should be non-negative");

        monitor.stop().await.expect("Network monitor should stop");
        println!("Error reporting and thresholds test passed");
    }

    // Test 4: Error detection across multiple interfaces
    {
        println!("Testing error detection across multiple interfaces");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(500)).await;

        let metrics = monitor.get_network_metrics().await.unwrap();
        let interface_count = metrics.interfaces.len();
        println!("  Analyzing error detection across {} interfaces", interface_count);

        // Analyze error distribution across interfaces
        let mut error_distribution = std::collections::HashMap::new();
        let mut max_rx_errors = 0;
        let mut max_tx_errors = 0;
        let mut total_interfaces_with_rx_errors = 0;
        let mut total_interfaces_with_tx_errors = 0;

        for (interface_name, interface_info) in &metrics.interfaces {
            let rx_errors = interface_info.rx_errors;
            let tx_errors = interface_info.tx_errors;

            error_distribution.insert(interface_name.clone(), (rx_errors, tx_errors));

            max_rx_errors = max_rx_errors.max(rx_errors);
            max_tx_errors = max_tx_errors.max(tx_errors);

            if rx_errors > 0 {
                total_interfaces_with_rx_errors += 1;
            }
            if tx_errors > 0 {
                total_interfaces_with_tx_errors += 1;
            }

            println!("    Interface {}: RX errors={}, TX errors={}", interface_name, rx_errors, tx_errors);
        }

        println!("  Error distribution summary:");
        println!("    Max RX errors on any interface: {}", max_rx_errors);
        println!("    Max TX errors on any interface: {}", max_tx_errors);
        println!("    Interfaces with RX errors: {}/{}", total_interfaces_with_rx_errors, interface_count);
        println!("    Interfaces with TX errors: {}/{}", total_interfaces_with_tx_errors, interface_count);

        // Verify error detection consistency across interfaces
        assert!(total_interfaces_with_rx_errors <= interface_count,
            "Interfaces with RX errors should not exceed total interfaces");
        assert!(total_interfaces_with_tx_errors <= interface_count,
            "Interfaces with TX errors should not exceed total interfaces");

        // Verify error distribution is reasonable
        for (interface_name, (rx_errors, tx_errors)) in &error_distribution {
            assert!(*rx_errors <= max_rx_errors, "Interface {} RX errors should not exceed maximum", interface_name);
            assert!(*tx_errors <= max_tx_errors, "Interface {} TX errors should not exceed maximum", interface_name);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Error detection across multiple interfaces test passed");
    }

    println!("Network error detection and reporting test passed");
}

// ================================================================================================
// Network Monitor Lifecycle Tests
// ================================================================================================

/// Test network monitor initialization and interface detection
#[tokio::test]
async fn test_network_monitor_initialization_and_interface_detection() {
    println!("Starting network monitor initialization and interface detection test");

    // Test 1: Default configuration initialization
    {
        println!("Testing NetworkMonitor initialization with default configuration");

        let monitor = create_default_network_monitor();

        // Import ResourceMonitor trait locally to access get_poll_interval
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;
        let poll_interval = monitor.get_poll_interval();
        assert_eq!(poll_interval.as_millis(), 5000, "Default poll interval should be 5000ms");

        println!("NetworkMonitor default initialization test passed");
    }

    // Test 2: Custom configuration initialization
    {
        println!("Testing NetworkMonitor initialization with custom configuration");

        let custom_intervals = vec![500, 1000, 2000, 10000];

        // Import ResourceMonitor trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        for (i, interval) in custom_intervals.into_iter().enumerate() {
            println!("Testing custom config {}: poll_interval_ms = {}", i + 1, interval);

            let monitor = create_custom_network_monitor(interval);
            let poll_interval = monitor.get_poll_interval();

            assert_eq!(poll_interval.as_millis(), interval as u128,
                "Custom poll interval should be preserved for config {}", i + 1);

            println!("Custom config {} passed", i + 1);
        }

        println!("NetworkMonitor custom initialization test passed");
    }

    // Test 3: Interface detection during initialization
    {
        println!("Testing interface detection during NetworkMonitor initialization");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        // Start monitor to trigger interface detection
        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(300)).await;

        // Verify interface detection
        let interfaces_result = monitor.get_network_interfaces().await;
        assert!(interfaces_result.is_ok(), "Should detect network interfaces successfully");

        let interfaces = interfaces_result.unwrap();
        println!("Detected {} network interface(s) during initialization", interfaces.len());

        // Verify we have at least one interface (loopback should always exist)
        assert!(!interfaces.is_empty(), "Should detect at least one network interface");

        // Verify interface names are valid
        for interface_name in &interfaces {
            assert!(!interface_name.is_empty(), "Interface name should not be empty");
            assert!(!interface_name.contains('\0'), "Interface name should not contain null bytes");
            println!("  Detected interface: {}", interface_name);
        }

        // Get detailed metrics to verify interface properties
        let metrics = monitor.get_network_metrics().await.unwrap();
        assert_eq!(interfaces.len(), metrics.interfaces.len(),
            "Interface list and metrics should have same count");

        for (interface_name, interface_info) in &metrics.interfaces {
            assert_eq!(interface_info.name, *interface_name, "Interface name should match key");
            println!("  Interface {}: MAC={:?}, Up={}",
                interface_name, interface_info.mac_address, interface_info.is_up);
        }

        monitor.stop().await.expect("Network monitor should stop");
        println!("Interface detection during initialization test passed");
    }

    // Test 4: Configuration validation
    {
        println!("Testing NetworkMonitor configuration validation");

        // Test edge case configurations
        let edge_configs = vec![
            (0, "Zero poll interval"),
            (1, "Minimum poll interval"),
            (u64::MAX, "Maximum poll interval"),
        ];

        // Import ResourceMonitor trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        for (i, (interval, description)) in edge_configs.into_iter().enumerate() {
            println!("Testing edge config {}: {} ({}ms)", i + 1, description, interval);

            let monitor = create_custom_network_monitor(interval);
            let poll_interval = monitor.get_poll_interval();

            assert_eq!(poll_interval.as_millis(), interval as u128,
                "Edge case poll interval should be preserved for {}", description);

            println!("Edge config {} ({}) passed", i + 1, description);
        }

        println!("NetworkMonitor configuration validation test passed");
    }

    println!("Network monitor initialization and interface detection test passed");
}

/// Test network monitoring task management
#[tokio::test]
async fn test_network_monitoring_task_management() {
    println!("Starting network monitoring task management test");

    // Test 1: Start monitoring task
    {
        println!("Testing network monitoring task start");

        let mut monitor = create_default_network_monitor();

        // Import the trait locally to avoid conflicts
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        let start_result = monitor.start().await;
        assert!(start_result.is_ok(), "Network monitor should start successfully");

        // Allow time for monitoring task to initialize
        sleep(Duration::from_millis(100)).await;

        // Stop the monitor
        let stop_result = monitor.stop().await;
        assert!(stop_result.is_ok(), "Network monitor should stop successfully");

        println!("Network monitoring task start test passed");
    }

    // Test 2: Multiple start/stop cycles
    {
        println!("Testing multiple network monitoring start/stop cycles");

        let mut monitor = create_default_network_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        for cycle in 1..=3 {
            println!("Network monitoring cycle {}", cycle);

            let start_result = monitor.start().await;
            assert!(start_result.is_ok(), "Network monitor should start in cycle {}", cycle);

            // Brief monitoring period
            sleep(Duration::from_millis(50)).await;

            let stop_result = monitor.stop().await;
            assert!(stop_result.is_ok(), "Network monitor should stop in cycle {}", cycle);

            // Brief pause between cycles
            sleep(Duration::from_millis(20)).await;
        }

        println!("Multiple network monitoring start/stop cycles test passed");
    }

    // Test 3: Idempotent operations
    {
        println!("Testing idempotent network monitoring operations");

        let mut monitor = create_default_network_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Multiple starts should be idempotent
        monitor.start().await.expect("First start should succeed");
        let second_start = monitor.start().await;
        assert!(second_start.is_ok(), "Second start should be idempotent");

        // Multiple stops should be idempotent
        monitor.stop().await.expect("First stop should succeed");
        let second_stop = monitor.stop().await;
        assert!(second_stop.is_ok(), "Second stop should be idempotent");

        println!("Idempotent network monitoring operations test passed");
    }

    // Test 4: Concurrent task management
    {
        println!("Testing concurrent network monitoring task management");

        let mut monitors = Vec::new();
        for i in 0..3 {
            let config = SystemMonitorConfig {
                poll_interval_ms: 500 + (i * 100),
                monitor_cpu: true,
                monitor_memory: true,
                monitor_disk: true,
                monitor_network: true,
            };
            monitors.push(NetworkMonitor::new(config));
        }

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Start all monitors
        for (i, monitor) in monitors.iter_mut().enumerate() {
            monitor.start().await.expect(&format!("Monitor {} should start", i));
        }

        // Allow concurrent monitoring
        sleep(Duration::from_millis(300)).await;

        // Stop all monitors
        for (i, monitor) in monitors.iter_mut().enumerate() {
            monitor.stop().await.expect(&format!("Monitor {} should stop cleanly", i));
        }

        println!("Concurrent network monitoring task management test passed");
    }

    println!("Network monitoring task management test passed");
}

/// Test network monitor configuration updates
#[tokio::test]
async fn test_network_monitor_configuration_updates() {
    println!("Starting network monitor configuration updates test");

    // Test 1: Poll interval updates
    {
        println!("Testing network monitor poll interval updates");

        let mut monitor = create_default_network_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Verify initial poll interval
        let initial_interval = monitor.get_poll_interval();
        assert_eq!(initial_interval.as_millis(), 5000, "Initial poll interval should be 5000ms");

        // Test various poll interval updates
        let test_intervals = vec![
            Duration::from_millis(500),
            Duration::from_millis(1000),
            Duration::from_millis(2000),
            Duration::from_millis(10000),
        ];

        for (i, new_interval) in test_intervals.into_iter().enumerate() {
            println!("Testing poll interval update {}: {}ms", i + 1, new_interval.as_millis());

            monitor.set_poll_interval(new_interval);
            let updated_interval = monitor.get_poll_interval();

            assert_eq!(updated_interval, new_interval,
                "Poll interval should be updated to {}ms", new_interval.as_millis());

            println!("Poll interval update {} passed", i + 1);
        }

        println!("Network monitor poll interval updates test passed");
    }

    // Test 2: Configuration changes during runtime
    {
        println!("Testing configuration changes during runtime");

        let mut monitor = create_default_network_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Start monitoring
        monitor.start().await.expect("Network monitor should start");
        sleep(Duration::from_millis(200)).await;

        // Change configuration while running
        let new_interval = Duration::from_millis(300);
        monitor.set_poll_interval(new_interval);

        // Verify configuration change took effect
        let updated_interval = monitor.get_poll_interval();
        assert_eq!(updated_interval, new_interval,
            "Poll interval should be updated during runtime");

        // Allow monitoring to continue with new configuration
        sleep(Duration::from_millis(400)).await;

        // Verify monitor still functions correctly
        // Import NetworkMonitoring trait locally for this test
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::NetworkMonitoring;
        let interfaces_result = monitor.get_network_interfaces().await;
        assert!(interfaces_result.is_ok(), "Monitor should still function after config change");

        monitor.stop().await.expect("Network monitor should stop");
        println!("Configuration changes during runtime test passed");
    }

    // Test 3: Configuration persistence
    {
        println!("Testing configuration persistence");

        let custom_interval = 1500;
        let mut monitor = create_custom_network_monitor(custom_interval);

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Verify configuration persists through start/stop cycles
        for cycle in 1..=3 {
            println!("Configuration persistence cycle {}", cycle);

            let poll_interval = monitor.get_poll_interval();
            assert_eq!(poll_interval.as_millis(), custom_interval as u128,
                "Configuration should persist through cycle {}", cycle);

            monitor.start().await.expect(&format!("Monitor should start in cycle {}", cycle));
            sleep(Duration::from_millis(100)).await;

            // Configuration should remain the same while running
            let running_interval = monitor.get_poll_interval();
            assert_eq!(running_interval.as_millis(), custom_interval as u128,
                "Configuration should persist while running in cycle {}", cycle);

            monitor.stop().await.expect(&format!("Monitor should stop in cycle {}", cycle));

            // Configuration should persist after stopping
            let stopped_interval = monitor.get_poll_interval();
            assert_eq!(stopped_interval.as_millis(), custom_interval as u128,
                "Configuration should persist after stopping in cycle {}", cycle);
        }

        println!("Configuration persistence test passed");
    }

    // Test 4: Multiple configuration updates
    {
        println!("Testing multiple configuration updates");

        let mut monitor = create_default_network_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Perform rapid configuration updates
        let intervals = vec![100, 200, 500, 1000, 2000, 5000];

        for (i, interval_ms) in intervals.into_iter().enumerate() {
            let new_interval = Duration::from_millis(interval_ms);
            monitor.set_poll_interval(new_interval);

            let current_interval = monitor.get_poll_interval();
            assert_eq!(current_interval, new_interval,
                "Configuration update {} should be applied immediately", i + 1);

            println!("Configuration update {}: {}ms applied", i + 1, interval_ms);
        }

        // Test configuration updates during monitoring
        monitor.start().await.expect("Monitor should start");

        for (i, interval_ms) in vec![800, 1200, 1600].into_iter().enumerate() {
            sleep(Duration::from_millis(100)).await;

            let new_interval = Duration::from_millis(interval_ms);
            monitor.set_poll_interval(new_interval);

            let current_interval = monitor.get_poll_interval();
            assert_eq!(current_interval, new_interval,
                "Runtime configuration update {} should be applied", i + 1);

            println!("Runtime configuration update {}: {}ms applied", i + 1, interval_ms);
        }

        monitor.stop().await.expect("Monitor should stop");
        println!("Multiple configuration updates test passed");
    }

    // Test 5: Configuration validation during updates
    {
        println!("Testing configuration validation during updates");

        let mut monitor = create_default_network_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Test edge case configurations
        let edge_intervals = vec![
            (0, "Zero interval"),
            (1, "Minimum interval"),
            (u64::MAX, "Maximum interval"),
        ];

        for (interval_ms, description) in edge_intervals {
            println!("Testing configuration validation: {}", description);

            let new_interval = Duration::from_millis(interval_ms);
            monitor.set_poll_interval(new_interval);

            let current_interval = monitor.get_poll_interval();
            assert_eq!(current_interval.as_millis(), interval_ms as u128,
                "Edge case configuration should be accepted: {}", description);

            println!("Configuration validation for {} passed", description);
        }

        println!("Configuration validation during updates test passed");
    }

    println!("Network monitor configuration updates test passed");
}

/// Test network monitor error handling and recovery
#[tokio::test]
async fn test_network_monitor_error_handling_and_recovery() {
    println!("Starting network monitor error handling and recovery test");

    // Test 1: Error recovery during monitoring
    {
        println!("Testing network monitor error recovery");

        let mut monitor = create_default_network_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Start monitoring
        monitor.start().await.expect("Monitor should start");

        // Allow some monitoring activity
        sleep(Duration::from_millis(200)).await;

        // Monitor should continue to function despite any internal errors
        // (The implementation should handle errors gracefully)

        // Stop monitoring
        monitor.stop().await.expect("Monitor should stop");

        println!("Network monitor error recovery test passed");
    }

    // Test 2: Configuration error handling
    {
        println!("Testing network monitor configuration error handling");

        // Test with potentially problematic configurations
        let problematic_configs = vec![
            (0, "Zero interval"),
            (u64::MAX, "Maximum interval"),
        ];

        for (interval, description) in problematic_configs {
            println!("Testing configuration error handling: {}", description);

            let monitor = create_custom_network_monitor(interval);

            // Monitor should be created successfully even with edge case configs
            // Import ResourceMonitor trait locally to access get_poll_interval
            use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;
            let poll_interval = monitor.get_poll_interval();
            assert_eq!(poll_interval.as_millis(), interval as u128,
                "Configuration should be preserved for {}", description);

            println!("Configuration error handling for {} passed", description);
        }

        println!("Network monitor configuration error handling test passed");
    }

    // Test 3: Resource cleanup after errors
    {
        println!("Testing network monitor resource cleanup after errors");

        let mut monitor = create_default_network_monitor();

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Start and run monitoring
        monitor.start().await.expect("Monitor should start");
        sleep(Duration::from_millis(100)).await;

        // Stop monitoring and verify cleanup
        let stop_result = monitor.stop().await;
        assert!(stop_result.is_ok(), "Monitor should stop and cleanup successfully");

        // Monitor should be in a clean state after stop
        // (Implementation-specific cleanup verification would go here)

        println!("Network monitor resource cleanup test passed");
    }

    // Test 4: Recovery from monitoring task failures
    {
        println!("Testing recovery from monitoring task failures");

        let mut monitor = create_custom_network_monitor(200); // Fast polling

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Start monitoring
        monitor.start().await.expect("Monitor should start");
        sleep(Duration::from_millis(300)).await;

        // Simulate recovery by stopping and restarting
        monitor.stop().await.expect("Monitor should stop for recovery");
        sleep(Duration::from_millis(100)).await;

        // Restart and verify recovery
        monitor.start().await.expect("Monitor should restart after recovery");
        sleep(Duration::from_millis(200)).await;

        // Verify functionality after recovery
        // Import NetworkMonitoring trait locally for this test
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::NetworkMonitoring;
        let recovery_interfaces = monitor.get_network_interfaces().await;
        assert!(recovery_interfaces.is_ok(), "Interfaces should be available after recovery");

        let recovery_metrics = monitor.get_network_metrics().await;
        assert!(recovery_metrics.is_ok(), "Metrics should be available after recovery");

        monitor.stop().await.expect("Monitor should stop after recovery test");

        println!("Recovery from monitoring task failures test passed");
    }

    // Test 5: Error handling with concurrent monitors
    {
        println!("Testing error handling with concurrent monitors");

        let mut monitors = Vec::new();
        for i in 0..3 {
            let config = SystemMonitorConfig {
                poll_interval_ms: 500 + (i * 100),
                monitor_cpu: true,
                monitor_memory: true,
                monitor_disk: true,
                monitor_network: true,
            };
            monitors.push(NetworkMonitor::new(config));
        }

        // Import the trait locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::ResourceMonitor;

        // Start all monitors
        for (i, monitor) in monitors.iter_mut().enumerate() {
            monitor.start().await.expect(&format!("Monitor {} should start", i));
        }

        // Allow concurrent monitoring
        sleep(Duration::from_millis(400)).await;

        // Simulate error conditions by rapid configuration changes
        for (i, monitor) in monitors.iter_mut().enumerate() {
            let new_interval = Duration::from_millis(200 + (i as u64 * 150));
            monitor.set_poll_interval(new_interval);
        }

        // Allow monitoring to continue
        sleep(Duration::from_millis(300)).await;

        // Stop all monitors and verify cleanup
        for (i, monitor) in monitors.iter_mut().enumerate() {
            monitor.stop().await.expect(&format!("Monitor {} should stop cleanly", i));
        }

        println!("Error handling with concurrent monitors test passed");
    }

    // Test 6: Interface detection error handling
    {
        println!("Testing interface detection error handling");

        let mut monitor = create_default_network_monitor();

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        // Start monitoring
        monitor.start().await.expect("Monitor should start");
        sleep(Duration::from_millis(300)).await;

        // Multiple interface detection calls should be robust
        for attempt in 1..=5 {
            let interfaces_result = monitor.get_network_interfaces().await;
            assert!(interfaces_result.is_ok(),
                "Interface detection attempt {} should succeed", attempt);

            let interfaces = interfaces_result.unwrap();
            assert!(!interfaces.is_empty(),
                "Should detect interfaces in attempt {}", attempt);

            println!("Interface detection attempt {} succeeded: {} interfaces",
                attempt, interfaces.len());
        }

        monitor.stop().await.expect("Monitor should stop");
        println!("Interface detection error handling test passed");
    }

    // Test 7: Metrics collection error handling
    {
        println!("Testing metrics collection error handling");

        let mut monitor = create_custom_network_monitor(100); // Very fast polling

        // Import traits locally
        use prisma_ai::prisma::prisma_engine::monitor::system::traits::{ResourceMonitor, NetworkMonitoring};

        monitor.start().await.expect("Monitor should start");
        sleep(Duration::from_millis(400)).await;

        // Rapid metrics collection should be robust
        for attempt in 1..=10 {
            let metrics_result = monitor.get_network_metrics().await;
            assert!(metrics_result.is_ok(),
                "Metrics collection attempt {} should succeed", attempt);

            let metrics = metrics_result.unwrap();
            assert!(!metrics.interfaces.is_empty(),
                "Should have interface metrics in attempt {}", attempt);

            // Brief delay between attempts
            sleep(Duration::from_millis(50)).await;
        }

        monitor.stop().await.expect("Monitor should stop");
        println!("Metrics collection error handling test passed");
    }

    println!("Network monitor error handling and recovery test passed");
}

/// Test queue length monitoring for multiple queues
#[tokio::test]
async fn test_queue_length_monitoring_for_multiple_queues() {
    println!("Starting queue length monitoring for multiple queues test");

    // Test 1: Basic queue length monitoring
    {
        println!("Testing basic queue length monitoring");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        // Start the queue monitor
        monitor.start().await.expect("Queue monitor should start");

        // Allow time for monitor initialization
        sleep(Duration::from_millis(100)).await;

        // Create multiple queues with different lengths
        let queue_configs = vec![
            ("high_priority_queue", 5),
            ("medium_priority_queue", 12),
            ("low_priority_queue", 3),
            ("background_queue", 0),
        ];

        // Update queue metrics for each queue
        for (queue_name, length) in &queue_configs {
            let update_result = monitor.update_queue_metrics(queue_name, *length).await;
            assert!(update_result.is_ok(), "Should update queue metrics for {}", queue_name);
            println!("Updated queue {}: length = {}", queue_name, length);
        }

        // Allow time for metrics to be processed
        sleep(Duration::from_millis(200)).await;

        // Verify queue metrics
        let metrics = monitor.get_metrics().await.expect("Should get queue metrics");
        assert_eq!(metrics.queue_metrics.len(), queue_configs.len(),
            "Should have metrics for all {} queues", queue_configs.len());

        // Verify each queue's metrics
        for (queue_name, expected_length) in &queue_configs {
            let queue_metrics = metrics.queue_metrics.get(*queue_name)
                .expect(&format!("Should have metrics for queue {}", queue_name));

            assert_eq!(queue_metrics.length, *expected_length,
                "Queue {} should have length {}", queue_name, expected_length);
            assert_eq!(queue_metrics.max_length, *expected_length,
                "Queue {} should have max_length {}", queue_name, expected_length);

            println!("Verified queue {}: length={}, max_length={}",
                queue_name, queue_metrics.length, queue_metrics.max_length);
        }

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Basic queue length monitoring test passed");
    }

    // Test 2: Dynamic queue length updates
    {
        println!("Testing dynamic queue length updates");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_name = "dynamic_queue";
        let length_sequence = vec![0, 5, 10, 15, 8, 3, 0];

        // Update queue length multiple times
        for (i, length) in length_sequence.iter().enumerate() {
            monitor.update_queue_metrics(queue_name, *length).await
                .expect("Should update queue metrics");

            sleep(Duration::from_millis(50)).await;

            // Verify current length
            let metrics = monitor.get_metrics().await.expect("Should get metrics");
            let queue_metrics = metrics.queue_metrics.get(queue_name)
                .expect("Should have queue metrics");

            assert_eq!(queue_metrics.length, *length,
                "Update {}: queue length should be {}", i + 1, length);

            // Verify max_length is correctly tracked
            let expected_max = length_sequence[0..=i].iter().max().unwrap();
            assert_eq!(queue_metrics.max_length, *expected_max,
                "Update {}: max_length should be {}", i + 1, expected_max);

            println!("Update {}: length={}, max_length={}",
                i + 1, queue_metrics.length, queue_metrics.max_length);
        }

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Dynamic queue length updates test passed");
    }

    // Test 3: Multiple queues with concurrent updates
    {
        println!("Testing multiple queues with concurrent updates");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queues = vec![
            ("queue_a", vec![1, 3, 5, 2]),
            ("queue_b", vec![0, 8, 4, 6]),
            ("queue_c", vec![2, 1, 9, 3]),
        ];

        // Perform concurrent updates
        for round in 0..4 {
            println!("Round {} of concurrent updates", round + 1);

            for (queue_name, lengths) in &queues {
                let length = lengths[round];
                monitor.update_queue_metrics(queue_name, length).await
                    .expect("Should update queue metrics");
                println!("  Updated {}: length = {}", queue_name, length);
            }

            sleep(Duration::from_millis(100)).await;

            // Verify all queues after this round
            let metrics = monitor.get_metrics().await.expect("Should get metrics");

            for (queue_name, lengths) in &queues {
                let queue_metrics = metrics.queue_metrics.get(*queue_name)
                    .expect("Should have queue metrics");

                let current_length = lengths[round];
                let expected_max = lengths[0..=round].iter().max().unwrap();

                assert_eq!(queue_metrics.length, current_length,
                    "Round {}: {} should have length {}", round + 1, queue_name, current_length);
                assert_eq!(queue_metrics.max_length, *expected_max,
                    "Round {}: {} should have max_length {}", round + 1, queue_name, expected_max);
            }
        }

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Multiple queues with concurrent updates test passed");
    }

    println!("Queue length monitoring for multiple queues test passed");
}

/// Test queue processing rate calculation
#[tokio::test]
async fn test_queue_processing_rate_calculation() {
    println!("Starting queue processing rate calculation test");

    // Test 1: Basic processing rate calculation
    {
        println!("Testing basic processing rate calculation");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_name = "processing_queue";
        let processing_times = vec![100.0, 150.0, 200.0, 120.0, 180.0]; // milliseconds

        // Record multiple task processing events
        for (i, processing_time) in processing_times.iter().enumerate() {
            monitor.record_task_processed(queue_name, *processing_time, true).await
                .expect("Should record task processed");

            println!("Recorded task {}: processing_time = {}ms", i + 1, processing_time);
            sleep(Duration::from_millis(50)).await;
        }

        // Allow time for metrics to be updated
        sleep(Duration::from_millis(200)).await;

        // Verify processing metrics
        let metrics = monitor.get_metrics().await.expect("Should get metrics");
        let queue_metrics = metrics.queue_metrics.get(queue_name)
            .expect("Should have queue metrics");

        assert_eq!(queue_metrics.tasks_processed, processing_times.len(),
            "Should have processed {} tasks", processing_times.len());
        assert_eq!(queue_metrics.tasks_failed, 0,
            "Should have no failed tasks");

        // Verify average processing time calculation
        let expected_avg = processing_times.iter().sum::<f64>() / processing_times.len() as f64;
        assert!((queue_metrics.avg_processing_time_ms - expected_avg).abs() < 0.1,
            "Average processing time should be {:.2}ms, got {:.2}ms",
            expected_avg, queue_metrics.avg_processing_time_ms);

        println!("Verified processing rate: {} tasks, avg_time = {:.2}ms",
            queue_metrics.tasks_processed, queue_metrics.avg_processing_time_ms);

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Basic processing rate calculation test passed");
    }

    // Test 2: Processing rate with multiple queues
    {
        println!("Testing processing rate with multiple queues");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_configs = vec![
            ("fast_queue", vec![50.0, 60.0, 55.0]),
            ("medium_queue", vec![150.0, 140.0, 160.0, 145.0]),
            ("slow_queue", vec![300.0, 350.0]),
        ];

        // Record tasks for each queue
        for (queue_name, processing_times) in &queue_configs {
            println!("Recording tasks for {}", queue_name);

            for (i, processing_time) in processing_times.iter().enumerate() {
                monitor.record_task_processed(queue_name, *processing_time, true).await
                    .expect("Should record task processed");
                println!("  Task {}: {}ms", i + 1, processing_time);
            }

            sleep(Duration::from_millis(100)).await;
        }

        // Allow time for metrics to be updated
        sleep(Duration::from_millis(200)).await;

        // Verify metrics for each queue
        let metrics = monitor.get_metrics().await.expect("Should get metrics");

        for (queue_name, processing_times) in &queue_configs {
            let queue_metrics = metrics.queue_metrics.get(*queue_name)
                .expect("Should have queue metrics");

            assert_eq!(queue_metrics.tasks_processed, processing_times.len(),
                "Queue {} should have processed {} tasks", queue_name, processing_times.len());

            let expected_avg = processing_times.iter().sum::<f64>() / processing_times.len() as f64;
            assert!((queue_metrics.avg_processing_time_ms - expected_avg).abs() < 0.1,
                "Queue {} average should be {:.2}ms, got {:.2}ms",
                queue_name, expected_avg, queue_metrics.avg_processing_time_ms);

            println!("Queue {}: {} tasks, avg_time = {:.2}ms",
                queue_name, queue_metrics.tasks_processed, queue_metrics.avg_processing_time_ms);
        }

        // Verify total metrics
        let total_tasks: usize = queue_configs.iter().map(|(_, times)| times.len()).sum();
        assert_eq!(metrics.total_tasks, total_tasks,
            "Total tasks should be {}", total_tasks);

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Processing rate with multiple queues test passed");
    }

    // Test 3: Processing rate calculation over time
    {
        println!("Testing processing rate calculation over time");

        let mut monitor = create_custom_queue_monitor(500, 500); // Faster polling

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_name = "time_based_queue";

        // Record tasks in batches over time
        let batches = vec![
            vec![100.0, 110.0, 90.0],      // Batch 1: avg = 100ms
            vec![200.0, 180.0, 220.0],     // Batch 2: avg = 200ms
            vec![150.0, 160.0, 140.0],     // Batch 3: avg = 150ms
        ];

        let mut all_times = Vec::new();

        for (batch_num, batch) in batches.iter().enumerate() {
            println!("Processing batch {}", batch_num + 1);

            for processing_time in batch {
                monitor.record_task_processed(queue_name, *processing_time, true).await
                    .expect("Should record task processed");
                all_times.push(*processing_time);
                sleep(Duration::from_millis(100)).await;
            }

            // Check metrics after each batch
            let metrics = monitor.get_metrics().await.expect("Should get metrics");
            let queue_metrics = metrics.queue_metrics.get(queue_name)
                .expect("Should have queue metrics");

            let expected_avg = all_times.iter().sum::<f64>() / all_times.len() as f64;
            assert!((queue_metrics.avg_processing_time_ms - expected_avg).abs() < 0.1,
                "After batch {}: average should be {:.2}ms, got {:.2}ms",
                batch_num + 1, expected_avg, queue_metrics.avg_processing_time_ms);

            println!("After batch {}: {} tasks, avg_time = {:.2}ms",
                batch_num + 1, queue_metrics.tasks_processed, queue_metrics.avg_processing_time_ms);
        }

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Processing rate calculation over time test passed");
    }

    println!("Queue processing rate calculation test passed");
}

/// Test average processing time tracking
#[tokio::test]
async fn test_average_processing_time_tracking() {
    println!("Starting average processing time tracking test");

    // Test 1: Basic average processing time calculation
    {
        println!("Testing basic average processing time calculation");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_name = "avg_time_queue";

        // Test with known processing times
        let test_cases = vec![
            (vec![100.0], 100.0),                           // Single task
            (vec![100.0, 200.0], 150.0),                    // Two tasks
            (vec![100.0, 200.0, 300.0], 200.0),             // Three tasks
            (vec![50.0, 100.0, 150.0, 200.0], 125.0),       // Four tasks
        ];

        for (case_num, (times, expected_avg)) in test_cases.iter().enumerate() {
            println!("Test case {}: processing times = {:?}", case_num + 1, times);

            // Create a fresh monitor for each test case
            let mut case_monitor = create_default_queue_monitor();
            case_monitor.start().await.expect("Monitor should start");
            sleep(Duration::from_millis(50)).await;

            // Record all tasks for this case
            for processing_time in times {
                case_monitor.record_task_processed(queue_name, *processing_time, true).await
                    .expect("Should record task processed");
            }

            sleep(Duration::from_millis(100)).await;

            // Verify average calculation
            let metrics = case_monitor.get_metrics().await.expect("Should get metrics");
            let queue_metrics = metrics.queue_metrics.get(queue_name)
                .expect("Should have queue metrics");

            assert!((queue_metrics.avg_processing_time_ms - expected_avg).abs() < 0.1,
                "Case {}: expected avg {:.1}ms, got {:.1}ms",
                case_num + 1, expected_avg, queue_metrics.avg_processing_time_ms);

            println!("Case {}: avg_time = {:.1}ms ✓", case_num + 1, queue_metrics.avg_processing_time_ms);

            case_monitor.stop().await.expect("Monitor should stop");
        }

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Basic average processing time calculation test passed");
    }

    // Test 2: Average processing time with incremental updates
    {
        println!("Testing average processing time with incremental updates");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_name = "incremental_queue";
        let processing_times = vec![100.0, 200.0, 150.0, 250.0, 175.0];
        let mut running_sum = 0.0;

        // Add tasks one by one and verify running average
        for (i, processing_time) in processing_times.iter().enumerate() {
            monitor.record_task_processed(queue_name, *processing_time, true).await
                .expect("Should record task processed");

            running_sum += processing_time;
            let expected_avg = running_sum / (i + 1) as f64;

            sleep(Duration::from_millis(100)).await;

            let metrics = monitor.get_metrics().await.expect("Should get metrics");
            let queue_metrics = metrics.queue_metrics.get(queue_name)
                .expect("Should have queue metrics");

            assert!((queue_metrics.avg_processing_time_ms - expected_avg).abs() < 0.1,
                "After task {}: expected avg {:.2}ms, got {:.2}ms",
                i + 1, expected_avg, queue_metrics.avg_processing_time_ms);

            println!("After task {}: processing_time = {}ms, avg = {:.2}ms",
                i + 1, processing_time, queue_metrics.avg_processing_time_ms);
        }

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Average processing time with incremental updates test passed");
    }

    // Test 3: Average processing time across multiple queues
    {
        println!("Testing average processing time across multiple queues");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_data = vec![
            ("fast_queue", vec![10.0, 20.0, 15.0, 25.0]),      // avg = 17.5ms
            ("medium_queue", vec![100.0, 120.0, 80.0]),        // avg = 100.0ms
            ("slow_queue", vec![500.0, 600.0, 550.0, 450.0]),  // avg = 525.0ms
        ];

        // Record tasks for all queues
        for (queue_name, processing_times) in &queue_data {
            println!("Recording tasks for {}", queue_name);

            for processing_time in processing_times {
                monitor.record_task_processed(queue_name, *processing_time, true).await
                    .expect("Should record task processed");
                sleep(Duration::from_millis(50)).await;
            }
        }

        sleep(Duration::from_millis(200)).await;

        // Verify average for each queue
        let metrics = monitor.get_metrics().await.expect("Should get metrics");

        for (queue_name, processing_times) in &queue_data {
            let queue_metrics = metrics.queue_metrics.get(*queue_name)
                .expect("Should have queue metrics");

            let expected_avg = processing_times.iter().sum::<f64>() / processing_times.len() as f64;
            assert!((queue_metrics.avg_processing_time_ms - expected_avg).abs() < 0.1,
                "Queue {} expected avg {:.2}ms, got {:.2}ms",
                queue_name, expected_avg, queue_metrics.avg_processing_time_ms);

            println!("Queue {}: {} tasks, avg = {:.2}ms",
                queue_name, queue_metrics.tasks_processed, queue_metrics.avg_processing_time_ms);
        }

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Average processing time across multiple queues test passed");
    }

    // Test 4: Average processing time with edge cases
    {
        println!("Testing average processing time with edge cases");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        // Test edge cases
        let edge_cases = vec![
            ("zero_time_queue", vec![0.0, 0.0, 0.0], 0.0),
            ("very_small_queue", vec![0.1, 0.2, 0.1], 0.133333),
            ("very_large_queue", vec![10000.0, 20000.0], 15000.0),
            ("mixed_queue", vec![0.0, 1000.0, 0.0], 333.333333),
        ];

        for (queue_name, processing_times, expected_avg) in &edge_cases {
            println!("Testing edge case: {}", queue_name);

            for processing_time in processing_times {
                monitor.record_task_processed(queue_name, *processing_time, true).await
                    .expect("Should record task processed");
            }

            sleep(Duration::from_millis(100)).await;

            let metrics = monitor.get_metrics().await.expect("Should get metrics");
            let queue_metrics = metrics.queue_metrics.get(*queue_name)
                .expect("Should have queue metrics");

            assert!((queue_metrics.avg_processing_time_ms - expected_avg).abs() < 0.01,
                "Edge case {}: expected avg {:.6}ms, got {:.6}ms",
                queue_name, expected_avg, queue_metrics.avg_processing_time_ms);

            println!("Edge case {}: avg = {:.6}ms ✓", queue_name, queue_metrics.avg_processing_time_ms);
        }

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Average processing time with edge cases test passed");
    }

    println!("Average processing time tracking test passed");
}

/// Test queue failure rate monitoring
#[tokio::test]
async fn test_queue_failure_rate_monitoring() {
    println!("Starting queue failure rate monitoring test");

    // Test 1: Basic failure rate calculation
    {
        println!("Testing basic failure rate calculation");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_name = "failure_test_queue";

        // Record mix of successful and failed tasks
        let task_results = vec![
            (100.0, true),   // success
            (150.0, false),  // failure
            (120.0, true),   // success
            (200.0, false),  // failure
            (180.0, false),  // failure
            (110.0, true),   // success
        ];

        for (i, (processing_time, success)) in task_results.iter().enumerate() {
            monitor.record_task_processed(queue_name, *processing_time, *success).await
                .expect("Should record task processed");
            println!("Recorded task {}: {}ms, success = {}", i + 1, processing_time, success);
            sleep(Duration::from_millis(50)).await;
        }

        sleep(Duration::from_millis(200)).await;

        // Verify failure metrics
        let metrics = monitor.get_metrics().await.expect("Should get metrics");
        let queue_metrics = metrics.queue_metrics.get(queue_name)
            .expect("Should have queue metrics");

        let total_tasks = task_results.len();
        let failed_tasks = task_results.iter().filter(|(_, success)| !success).count();

        assert_eq!(queue_metrics.tasks_processed, total_tasks,
            "Should have processed {} tasks", total_tasks);
        assert_eq!(queue_metrics.tasks_failed, failed_tasks,
            "Should have {} failed tasks", failed_tasks);

        // Calculate failure rate
        let failure_rate = (failed_tasks as f64 / total_tasks as f64) * 100.0;
        println!("Failure rate: {:.1}% ({} failed out of {} total)",
            failure_rate, failed_tasks, total_tasks);

        // Verify total metrics
        assert_eq!(metrics.total_tasks, total_tasks,
            "Total tasks should be {}", total_tasks);
        assert_eq!(metrics.total_failed, failed_tasks,
            "Total failed should be {}", failed_tasks);

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Basic failure rate calculation test passed");
    }

    // Test 2: Failure rate across multiple queues
    {
        println!("Testing failure rate across multiple queues");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_scenarios = vec![
            ("reliable_queue", vec![
                (100.0, true), (110.0, true), (120.0, true), (105.0, true)
            ]), // 0% failure rate
            ("unreliable_queue", vec![
                (200.0, false), (150.0, false), (180.0, false), (160.0, false)
            ]), // 100% failure rate
            ("mixed_queue", vec![
                (100.0, true), (150.0, false), (120.0, true), (200.0, false),
                (110.0, true), (180.0, false)
            ]), // 50% failure rate
        ];

        // Record tasks for each queue
        for (queue_name, task_results) in &queue_scenarios {
            println!("Recording tasks for {}", queue_name);

            for (processing_time, success) in task_results {
                monitor.record_task_processed(queue_name, *processing_time, *success).await
                    .expect("Should record task processed");
                sleep(Duration::from_millis(30)).await;
            }
        }

        sleep(Duration::from_millis(200)).await;

        // Verify metrics for each queue
        let metrics = monitor.get_metrics().await.expect("Should get metrics");

        for (queue_name, task_results) in &queue_scenarios {
            let queue_metrics = metrics.queue_metrics.get(*queue_name)
                .expect("Should have queue metrics");

            let total_tasks = task_results.len();
            let failed_tasks = task_results.iter().filter(|(_, success)| !success).count();
            let failure_rate = (failed_tasks as f64 / total_tasks as f64) * 100.0;

            assert_eq!(queue_metrics.tasks_processed, total_tasks,
                "Queue {} should have processed {} tasks", queue_name, total_tasks);
            assert_eq!(queue_metrics.tasks_failed, failed_tasks,
                "Queue {} should have {} failed tasks", queue_name, failed_tasks);

            println!("Queue {}: {:.1}% failure rate ({}/{} failed)",
                queue_name, failure_rate, failed_tasks, total_tasks);
        }

        // Verify total metrics across all queues
        let total_all_tasks: usize = queue_scenarios.iter()
            .map(|(_, tasks)| tasks.len()).sum();
        let total_all_failed: usize = queue_scenarios.iter()
            .map(|(_, tasks)| tasks.iter().filter(|(_, success)| !success).count()).sum();

        assert_eq!(metrics.total_tasks, total_all_tasks,
            "Total tasks across all queues should be {}", total_all_tasks);
        assert_eq!(metrics.total_failed, total_all_failed,
            "Total failed across all queues should be {}", total_all_failed);

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Failure rate across multiple queues test passed");
    }

    // Test 3: Failure rate tracking over time
    {
        println!("Testing failure rate tracking over time");

        let mut monitor = create_custom_queue_monitor(500, 500); // Faster polling

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_name = "time_based_failure_queue";

        // Simulate changing failure rates over time
        let time_periods = vec![
            vec![(100.0, true), (110.0, true), (120.0, true)],           // Period 1: 0% failure
            vec![(150.0, false), (160.0, true), (170.0, false)],         // Period 2: 66% failure
            vec![(200.0, true), (210.0, true), (220.0, true), (230.0, true)], // Period 3: 0% failure
        ];

        let mut cumulative_total = 0;
        let mut cumulative_failed = 0;

        for (period_num, period_tasks) in time_periods.iter().enumerate() {
            println!("Processing time period {}", period_num + 1);

            for (processing_time, success) in period_tasks {
                monitor.record_task_processed(queue_name, *processing_time, *success).await
                    .expect("Should record task processed");

                cumulative_total += 1;
                if !success {
                    cumulative_failed += 1;
                }

                sleep(Duration::from_millis(100)).await;
            }

            // Check metrics after each period
            let metrics = monitor.get_metrics().await.expect("Should get metrics");
            let queue_metrics = metrics.queue_metrics.get(queue_name)
                .expect("Should have queue metrics");

            assert_eq!(queue_metrics.tasks_processed, cumulative_total,
                "After period {}: should have {} total tasks", period_num + 1, cumulative_total);
            assert_eq!(queue_metrics.tasks_failed, cumulative_failed,
                "After period {}: should have {} failed tasks", period_num + 1, cumulative_failed);

            let failure_rate = (cumulative_failed as f64 / cumulative_total as f64) * 100.0;
            println!("After period {}: {:.1}% cumulative failure rate ({}/{} failed)",
                period_num + 1, failure_rate, cumulative_failed, cumulative_total);
        }

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Failure rate tracking over time test passed");
    }

    // Test 4: Failure rate with edge cases
    {
        println!("Testing failure rate with edge cases");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let edge_cases = vec![
            ("all_success_queue", vec![(100.0, true)]),                    // 100% success
            ("all_failure_queue", vec![(100.0, false)]),                   // 100% failure
            ("single_success_queue", vec![(100.0, true)]),                 // Single success
            ("single_failure_queue", vec![(100.0, false)]),                // Single failure
        ];

        for (queue_name, task_results) in &edge_cases {
            println!("Testing edge case: {}", queue_name);

            for (processing_time, success) in task_results {
                monitor.record_task_processed(queue_name, *processing_time, *success).await
                    .expect("Should record task processed");
            }

            sleep(Duration::from_millis(100)).await;

            let metrics = monitor.get_metrics().await.expect("Should get metrics");
            let queue_metrics = metrics.queue_metrics.get(*queue_name)
                .expect("Should have queue metrics");

            let total_tasks = task_results.len();
            let failed_tasks = task_results.iter().filter(|(_, success)| !success).count();
            let failure_rate = (failed_tasks as f64 / total_tasks as f64) * 100.0;

            assert_eq!(queue_metrics.tasks_processed, total_tasks,
                "Edge case {}: should have {} total tasks", queue_name, total_tasks);
            assert_eq!(queue_metrics.tasks_failed, failed_tasks,
                "Edge case {}: should have {} failed tasks", queue_name, failed_tasks);

            println!("Edge case {}: {:.1}% failure rate ✓", queue_name, failure_rate);
        }

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Failure rate with edge cases test passed");
    }

    println!("Queue failure rate monitoring test passed");
}

/// Test dynamic queue creation and tracking
#[tokio::test]
async fn test_dynamic_queue_creation_and_tracking() {
    println!("Starting dynamic queue creation and tracking test");

    // Test 1: Automatic queue creation when first referenced
    {
        println!("Testing automatic queue creation when first referenced");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        // Initially no queues should exist
        let initial_metrics = monitor.get_metrics().await.expect("Should get initial metrics");
        assert_eq!(initial_metrics.queue_metrics.len(), 0, "Should start with no queues");

        // Reference a new queue by updating its metrics
        let queue_name = "auto_created_queue";
        monitor.update_queue_metrics(queue_name, 5).await
            .expect("Should update queue metrics");

        sleep(Duration::from_millis(100)).await;

        // Verify queue was automatically created
        let metrics = monitor.get_metrics().await.expect("Should get metrics");
        assert_eq!(metrics.queue_metrics.len(), 1, "Should have one queue after creation");
        assert!(metrics.queue_metrics.contains_key(queue_name),
            "Should contain the auto-created queue");

        let queue_metrics = metrics.queue_metrics.get(queue_name)
            .expect("Should have queue metrics");
        assert_eq!(queue_metrics.length, 5, "Queue should have correct length");
        assert_eq!(queue_metrics.max_length, 5, "Queue should have correct max_length");

        println!("Auto-created queue: length={}, max_length={}",
            queue_metrics.length, queue_metrics.max_length);

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Automatic queue creation test passed");
    }

    // Test 2: Multiple queues created dynamically
    {
        println!("Testing multiple queues created dynamically");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_operations = vec![
            ("queue_alpha", "update_metrics", 3),
            ("queue_beta", "record_task", 0),
            ("queue_gamma", "update_metrics", 7),
            ("queue_delta", "record_task", 0),
            ("queue_epsilon", "update_metrics", 1),
        ];

        // Create queues through different operations
        for (queue_name, operation, value) in &queue_operations {
            match *operation {
                "update_metrics" => {
                    monitor.update_queue_metrics(queue_name, *value).await
                        .expect("Should update queue metrics");
                    println!("Created {} via update_metrics with length {}", queue_name, value);
                }
                "record_task" => {
                    monitor.record_task_processed(queue_name, 100.0, true).await
                        .expect("Should record task processed");
                    println!("Created {} via record_task", queue_name);
                }
                _ => panic!("Unknown operation: {}", operation),
            }
            sleep(Duration::from_millis(50)).await;
        }

        sleep(Duration::from_millis(200)).await;

        // Verify all queues were created
        let metrics = monitor.get_metrics().await.expect("Should get metrics");
        assert_eq!(metrics.queue_metrics.len(), queue_operations.len(),
            "Should have {} queues", queue_operations.len());

        for (queue_name, operation, expected_length) in &queue_operations {
            assert!(metrics.queue_metrics.contains_key(*queue_name),
                "Should contain queue {}", queue_name);

            let queue_metrics = metrics.queue_metrics.get(*queue_name)
                .expect("Should have queue metrics");

            match *operation {
                "update_metrics" => {
                    assert_eq!(queue_metrics.length, *expected_length,
                        "Queue {} should have length {}", queue_name, expected_length);
                }
                "record_task" => {
                    assert_eq!(queue_metrics.tasks_processed, 1,
                        "Queue {} should have processed 1 task", queue_name);
                }
                _ => {}
            }

            println!("Verified queue {}: length={}, tasks_processed={}",
                queue_name, queue_metrics.length, queue_metrics.tasks_processed);
        }

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Multiple dynamic queue creation test passed");
    }

    // Test 3: Queue metadata persistence during operations
    {
        println!("Testing queue metadata persistence during operations");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_name = "persistent_queue";

        // Create queue and perform various operations
        monitor.update_queue_metrics(queue_name, 10).await
            .expect("Should update queue metrics");
        sleep(Duration::from_millis(50)).await;

        // Record some tasks
        let task_operations = vec![
            (150.0, true),   // success
            (200.0, false),  // failure
            (175.0, true),   // success
        ];

        for (processing_time, success) in &task_operations {
            monitor.record_task_processed(queue_name, *processing_time, *success).await
                .expect("Should record task processed");
            sleep(Duration::from_millis(50)).await;
        }

        // Update queue length again
        monitor.update_queue_metrics(queue_name, 15).await
            .expect("Should update queue metrics");
        sleep(Duration::from_millis(100)).await;

        // Verify all metadata is preserved
        let metrics = monitor.get_metrics().await.expect("Should get metrics");
        let queue_metrics = metrics.queue_metrics.get(queue_name)
            .expect("Should have queue metrics");

        assert_eq!(queue_metrics.length, 15, "Should have latest length");
        assert_eq!(queue_metrics.max_length, 15, "Should track max length");
        assert_eq!(queue_metrics.tasks_processed, task_operations.len(),
            "Should have processed {} tasks", task_operations.len());

        let failed_count = task_operations.iter().filter(|(_, success)| !success).count();
        assert_eq!(queue_metrics.tasks_failed, failed_count,
            "Should have {} failed tasks", failed_count);

        let expected_avg = task_operations.iter().map(|(time, _)| time).sum::<f64>()
            / task_operations.len() as f64;
        assert!((queue_metrics.avg_processing_time_ms - expected_avg).abs() < 0.1,
            "Should have correct average processing time");

        println!("Persistent queue metadata: length={}, max_length={}, tasks_processed={}, tasks_failed={}, avg_time={:.2}ms",
            queue_metrics.length, queue_metrics.max_length, queue_metrics.tasks_processed,
            queue_metrics.tasks_failed, queue_metrics.avg_processing_time_ms);

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Queue metadata persistence test passed");
    }

    println!("Dynamic queue creation and tracking test passed");
}

/// Test queue metrics updates and synchronization
#[tokio::test]
async fn test_queue_metrics_updates_and_synchronization() {
    println!("Starting queue metrics updates and synchronization test");

    // Test 1: Concurrent updates to queue metrics
    {
        println!("Testing concurrent updates to queue metrics");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_name = "concurrent_queue";

        // Perform rapid concurrent updates
        let update_sequence = vec![
            (0, 5),    // Initial length
            (50, 10),  // Increase
            (100, 3),  // Decrease
            (150, 15), // Large increase
            (200, 8),  // Decrease
            (250, 12), // Increase
        ];

        // Use tokio::spawn to simulate concurrent updates
        let mut handles = Vec::new();
        for (delay_ms, length) in update_sequence {
            let queue_name_clone = queue_name.to_string();
            let handle = tokio::spawn(async move {
                sleep(Duration::from_millis(delay_ms)).await;
                (length, Instant::now())
            });
            handles.push((handle, length));
        }

        // Apply updates as they complete
        for (handle, expected_length) in handles {
            let (length, timestamp) = handle.await.expect("Task should complete");
            monitor.update_queue_metrics(&queue_name, length).await
                .expect("Should update queue metrics");
            println!("Applied update: length={} at {:?}", length, timestamp);
            sleep(Duration::from_millis(25)).await;
        }

        sleep(Duration::from_millis(200)).await;

        // Verify final state
        let metrics = monitor.get_metrics().await.expect("Should get metrics");
        let queue_metrics = metrics.queue_metrics.get(queue_name)
            .expect("Should have queue metrics");

        // The final length should be the last update (12)
        assert_eq!(queue_metrics.length, 12, "Should have final length");
        // Max length should be the highest value seen (15)
        assert_eq!(queue_metrics.max_length, 15, "Should track maximum length");

        println!("Concurrent updates result: length={}, max_length={}",
            queue_metrics.length, queue_metrics.max_length);

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Concurrent updates test passed");
    }

    // Test 2: Synchronization between different metric types
    {
        println!("Testing synchronization between different metric types");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_name = "sync_queue";

        // Interleave different types of updates
        let operations = vec![
            ("update_length", 5, 0.0, true),
            ("record_task", 0, 100.0, true),
            ("update_length", 8, 0.0, true),
            ("record_task", 0, 150.0, false),
            ("update_length", 3, 0.0, true),
            ("record_task", 0, 120.0, true),
            ("update_length", 10, 0.0, true),
        ];

        for (operation, length, processing_time, success) in &operations {
            match *operation {
                "update_length" => {
                    monitor.update_queue_metrics(queue_name, *length).await
                        .expect("Should update queue metrics");
                    println!("Updated length to {}", length);
                }
                "record_task" => {
                    monitor.record_task_processed(queue_name, *processing_time, *success).await
                        .expect("Should record task processed");
                    println!("Recorded task: {}ms, success={}", processing_time, success);
                }
                _ => panic!("Unknown operation: {}", operation),
            }
            sleep(Duration::from_millis(100)).await;

            // Verify synchronization after each operation
            let metrics = monitor.get_metrics().await.expect("Should get metrics");
            let queue_metrics = metrics.queue_metrics.get(queue_name)
                .expect("Should have queue metrics");

            // Verify that last_updated timestamp is recent
            assert!(queue_metrics.last_updated.elapsed().as_millis() < 1000,
                "Queue metrics should have recent timestamp");
        }

        // Verify final synchronized state
        let final_metrics = monitor.get_metrics().await.expect("Should get final metrics");
        let final_queue_metrics = final_metrics.queue_metrics.get(queue_name)
            .expect("Should have final queue metrics");

        assert_eq!(final_queue_metrics.length, 10, "Should have final length");
        assert_eq!(final_queue_metrics.max_length, 10, "Should have correct max length");
        assert_eq!(final_queue_metrics.tasks_processed, 3, "Should have processed 3 tasks");
        assert_eq!(final_queue_metrics.tasks_failed, 1, "Should have 1 failed task");

        let expected_avg = (100.0 + 150.0 + 120.0) / 3.0;
        assert!((final_queue_metrics.avg_processing_time_ms - expected_avg).abs() < 0.1,
            "Should have correct average processing time");

        println!("Final synchronized state: length={}, max_length={}, tasks_processed={}, tasks_failed={}, avg_time={:.2}ms",
            final_queue_metrics.length, final_queue_metrics.max_length,
            final_queue_metrics.tasks_processed, final_queue_metrics.tasks_failed,
            final_queue_metrics.avg_processing_time_ms);

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Synchronization between metric types test passed");
    }

    // Test 3: Atomic updates to prevent race conditions
    {
        println!("Testing atomic updates to prevent race conditions");

        let mut monitor = create_custom_queue_monitor(100, 100); // Fast polling

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_name = "atomic_queue";

        // Simulate high-frequency updates that could cause race conditions
        let num_updates = 20;
        let mut update_handles = Vec::new();

        for i in 0..num_updates {
            let queue_name_clone = queue_name.to_string();
            let length = i + 1;
            let processing_time = 100.0 + (i as f64 * 10.0);

            // Alternate between length updates and task recording
            if i % 2 == 0 {
                let handle = tokio::spawn(async move {
                    ("update_length", length, processing_time, true)
                });
                update_handles.push(handle);
            } else {
                let handle = tokio::spawn(async move {
                    ("record_task", 0, processing_time, true)
                });
                update_handles.push(handle);
            }
        }

        // Apply all updates rapidly
        for handle in update_handles {
            let (operation, length, processing_time, success) = handle.await
                .expect("Update task should complete");

            match operation {
                "update_length" => {
                    monitor.update_queue_metrics(queue_name, length).await
                        .expect("Should update queue metrics");
                }
                "record_task" => {
                    monitor.record_task_processed(queue_name, processing_time, success).await
                        .expect("Should record task processed");
                }
                _ => {}
            }

            // Very short delay to increase chance of race conditions
            sleep(Duration::from_millis(5)).await;
        }

        // Allow time for all updates to be processed
        sleep(Duration::from_millis(500)).await;

        // Verify atomic consistency
        let metrics = monitor.get_metrics().await.expect("Should get metrics");
        let queue_metrics = metrics.queue_metrics.get(queue_name)
            .expect("Should have queue metrics");

        // Verify that all updates were applied atomically
        let expected_task_count = num_updates / 2; // Half were task recordings
        assert_eq!(queue_metrics.tasks_processed, expected_task_count,
            "Should have processed {} tasks atomically", expected_task_count);

        // Verify that the final length is from the last length update
        let expected_final_length = num_updates - 1; // Last even number
        assert_eq!(queue_metrics.length, expected_final_length,
            "Should have final length {} from atomic updates", expected_final_length);

        // Verify max_length is consistent
        assert!(queue_metrics.max_length >= queue_metrics.length,
            "Max length should be >= current length");

        println!("Atomic updates result: length={}, max_length={}, tasks_processed={}, consistency_verified=true",
            queue_metrics.length, queue_metrics.max_length, queue_metrics.tasks_processed);

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Atomic updates test passed");
    }

    println!("Queue metrics updates and synchronization test passed");
}

/// Test queue metrics aggregation across all queues
#[tokio::test]
async fn test_queue_metrics_aggregation_across_all_queues() {
    println!("Starting queue metrics aggregation across all queues test");

    // Test 1: Total task counting across all queues
    {
        println!("Testing total task counting across all queues");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_configs = vec![
            ("queue_a", vec![(100.0, true), (150.0, true), (120.0, false)]),
            ("queue_b", vec![(200.0, true), (180.0, false), (220.0, true), (190.0, true)]),
            ("queue_c", vec![(300.0, false), (350.0, true)]),
            ("queue_d", vec![(400.0, true), (450.0, true), (420.0, false), (480.0, true), (460.0, false)]),
        ];

        let mut expected_total_tasks = 0;
        let mut expected_total_failed = 0;

        // Record tasks for each queue
        for (queue_name, tasks) in &queue_configs {
            println!("Recording tasks for {}", queue_name);

            for (processing_time, success) in tasks {
                monitor.record_task_processed(queue_name, *processing_time, *success).await
                    .expect("Should record task processed");

                expected_total_tasks += 1;
                if !success {
                    expected_total_failed += 1;
                }

                sleep(Duration::from_millis(50)).await;
            }
        }

        // Allow time for aggregation
        sleep(Duration::from_millis(300)).await;

        // Verify aggregated totals
        let metrics = monitor.get_metrics().await.expect("Should get metrics");

        assert_eq!(metrics.total_tasks, expected_total_tasks,
            "Total tasks should be {}", expected_total_tasks);
        assert_eq!(metrics.total_failed, expected_total_failed,
            "Total failed should be {}", expected_total_failed);

        // Verify individual queue totals sum to global totals
        let sum_tasks: usize = metrics.queue_metrics.values()
            .map(|q| q.tasks_processed).sum();
        let sum_failed: usize = metrics.queue_metrics.values()
            .map(|q| q.tasks_failed).sum();

        assert_eq!(sum_tasks, expected_total_tasks,
            "Sum of individual queue tasks should equal total");
        assert_eq!(sum_failed, expected_total_failed,
            "Sum of individual queue failures should equal total");

        println!("Aggregation results: total_tasks={}, total_failed={}, queues={}",
            metrics.total_tasks, metrics.total_failed, metrics.queue_metrics.len());

        // Verify each queue's contribution
        for (queue_name, tasks) in &queue_configs {
            let queue_metrics = metrics.queue_metrics.get(*queue_name)
                .expect("Should have queue metrics");

            let expected_queue_tasks = tasks.len();
            let expected_queue_failed = tasks.iter().filter(|(_, success)| !success).count();

            assert_eq!(queue_metrics.tasks_processed, expected_queue_tasks,
                "Queue {} should have {} tasks", queue_name, expected_queue_tasks);
            assert_eq!(queue_metrics.tasks_failed, expected_queue_failed,
                "Queue {} should have {} failed tasks", queue_name, expected_queue_failed);

            println!("Queue {}: {} tasks, {} failed",
                queue_name, queue_metrics.tasks_processed, queue_metrics.tasks_failed);
        }

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Total task counting test passed");
    }

    // Test 2: Total failure counting across all queues
    {
        println!("Testing total failure counting across all queues");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        // Create queues with different failure patterns
        let failure_patterns = vec![
            ("reliable_queue", vec![true, true, true, true]),                    // 0% failure
            ("unreliable_queue", vec![false, false, false, false]),             // 100% failure
            ("mixed_queue", vec![true, false, true, false, true]),              // 40% failure
            ("mostly_good_queue", vec![true, true, true, false]),               // 25% failure
            ("mostly_bad_queue", vec![false, false, true]),                     // 66% failure
        ];

        let mut expected_total_tasks = 0;
        let mut expected_total_failed = 0;

        // Record tasks with different failure patterns
        for (queue_name, success_pattern) in &failure_patterns {
            println!("Recording failure pattern for {}", queue_name);

            for (i, success) in success_pattern.iter().enumerate() {
                let processing_time = 100.0 + (i as f64 * 10.0);
                monitor.record_task_processed(queue_name, processing_time, *success).await
                    .expect("Should record task processed");

                expected_total_tasks += 1;
                if !success {
                    expected_total_failed += 1;
                }

                sleep(Duration::from_millis(30)).await;
            }
        }

        // Allow time for aggregation
        sleep(Duration::from_millis(300)).await;

        // Verify failure aggregation
        let metrics = monitor.get_metrics().await.expect("Should get metrics");

        assert_eq!(metrics.total_tasks, expected_total_tasks,
            "Total tasks should be {}", expected_total_tasks);
        assert_eq!(metrics.total_failed, expected_total_failed,
            "Total failed should be {}", expected_total_failed);

        // Calculate overall failure rate
        let overall_failure_rate = (expected_total_failed as f64 / expected_total_tasks as f64) * 100.0;
        println!("Overall failure rate: {:.1}% ({} failed out of {} total)",
            overall_failure_rate, expected_total_failed, expected_total_tasks);

        // Verify individual queue failure rates
        for (queue_name, success_pattern) in &failure_patterns {
            let queue_metrics = metrics.queue_metrics.get(*queue_name)
                .expect("Should have queue metrics");

            let expected_failed = success_pattern.iter().filter(|&&success| !success).count();
            let failure_rate = (expected_failed as f64 / success_pattern.len() as f64) * 100.0;

            assert_eq!(queue_metrics.tasks_failed, expected_failed,
                "Queue {} should have {} failed tasks", queue_name, expected_failed);

            println!("Queue {}: {:.1}% failure rate ({}/{} failed)",
                queue_name, failure_rate, expected_failed, success_pattern.len());
        }

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Total failure counting test passed");
    }

    // Test 3: Global metrics calculation and updates
    {
        println!("Testing global metrics calculation and updates");

        let mut monitor = create_custom_queue_monitor(200, 200); // Faster polling for updates

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        // Test incremental global updates
        let incremental_operations = vec![
            ("queue_1", "task", 100.0, true),
            ("queue_2", "task", 150.0, false),
            ("queue_1", "task", 120.0, true),
            ("queue_3", "task", 200.0, true),
            ("queue_2", "task", 180.0, true),
            ("queue_1", "task", 110.0, false),
            ("queue_3", "task", 220.0, false),
        ];

        let mut running_total_tasks = 0;
        let mut running_total_failed = 0;

        // Apply operations incrementally and verify global updates
        for (i, (queue_name, _operation, processing_time, success)) in incremental_operations.iter().enumerate() {
            monitor.record_task_processed(queue_name, *processing_time, *success).await
                .expect("Should record task processed");

            running_total_tasks += 1;
            if !success {
                running_total_failed += 1;
            }

            sleep(Duration::from_millis(150)).await; // Allow time for background updates

            // Verify global metrics are updated
            let metrics = monitor.get_metrics().await.expect("Should get metrics");

            assert_eq!(metrics.total_tasks, running_total_tasks,
                "After operation {}: total tasks should be {}", i + 1, running_total_tasks);
            assert_eq!(metrics.total_failed, running_total_failed,
                "After operation {}: total failed should be {}", i + 1, running_total_failed);

            // Verify that last_updated timestamp is recent
            assert!(metrics.last_updated.elapsed().as_millis() < 1000,
                "Global metrics should have recent timestamp");

            println!("After operation {}: total_tasks={}, total_failed={}, queues={}",
                i + 1, metrics.total_tasks, metrics.total_failed, metrics.queue_metrics.len());
        }

        // Verify final global state consistency
        let final_metrics = monitor.get_metrics().await.expect("Should get final metrics");

        // Verify totals match sum of individual queues
        let calculated_total_tasks: usize = final_metrics.queue_metrics.values()
            .map(|q| q.tasks_processed).sum();
        let calculated_total_failed: usize = final_metrics.queue_metrics.values()
            .map(|q| q.tasks_failed).sum();

        assert_eq!(final_metrics.total_tasks, calculated_total_tasks,
            "Final total tasks should match calculated sum");
        assert_eq!(final_metrics.total_failed, calculated_total_failed,
            "Final total failed should match calculated sum");

        println!("Final global metrics: total_tasks={}, total_failed={}, consistency_verified=true",
            final_metrics.total_tasks, final_metrics.total_failed);

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Global metrics calculation and updates test passed");
    }

    println!("Queue metrics aggregation across all queues test passed");
}

/// Test queue metrics cleanup and maintenance
#[tokio::test]
async fn test_queue_metrics_cleanup_and_maintenance() {
    println!("Starting queue metrics cleanup and maintenance test");

    // Test 1: Cleanup of inactive queues (simulated through monitoring loop behavior)
    {
        println!("Testing cleanup behavior during monitoring operations");

        let mut monitor = create_custom_queue_monitor(200, 200); // Fast polling for testing

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        // Create multiple queues with different activity patterns
        let queue_names = vec!["active_queue", "inactive_queue", "periodic_queue"];

        // Initialize all queues
        for queue_name in &queue_names {
            monitor.update_queue_metrics(queue_name, 1).await
                .expect("Should update queue metrics");
            sleep(Duration::from_millis(50)).await;
        }

        // Verify all queues exist
        let initial_metrics = monitor.get_metrics().await.expect("Should get initial metrics");
        assert_eq!(initial_metrics.queue_metrics.len(), queue_names.len(),
            "Should have {} queues initially", queue_names.len());

        // Simulate different activity patterns
        for cycle in 0..5 {
            println!("Activity cycle {}", cycle + 1);

            // Active queue gets regular updates
            monitor.update_queue_metrics("active_queue", cycle + 2).await
                .expect("Should update active queue");
            monitor.record_task_processed("active_queue", 100.0 + (cycle as f64 * 10.0), true).await
                .expect("Should record task for active queue");

            // Periodic queue gets occasional updates
            if cycle % 2 == 0 {
                monitor.update_queue_metrics("periodic_queue", cycle + 1).await
                    .expect("Should update periodic queue");
            }

            // Inactive queue gets no updates after initial creation

            sleep(Duration::from_millis(300)).await; // Allow monitoring loop to run
        }

        // Verify queue states after activity simulation
        let final_metrics = monitor.get_metrics().await.expect("Should get final metrics");

        // All queues should still exist (the current implementation doesn't auto-cleanup)
        assert_eq!(final_metrics.queue_metrics.len(), queue_names.len(),
            "Should still have {} queues", queue_names.len());

        // Verify active queue has been updated
        let active_queue = final_metrics.queue_metrics.get("active_queue")
            .expect("Should have active queue");
        assert!(active_queue.tasks_processed > 0, "Active queue should have processed tasks");
        assert!(active_queue.length > 1, "Active queue should have updated length");

        // Verify inactive queue remains unchanged
        let inactive_queue = final_metrics.queue_metrics.get("inactive_queue")
            .expect("Should have inactive queue");
        assert_eq!(inactive_queue.tasks_processed, 0, "Inactive queue should have no processed tasks");
        assert_eq!(inactive_queue.length, 1, "Inactive queue should have original length");

        println!("Queue states after activity simulation:");
        for queue_name in &queue_names {
            let queue_metrics = final_metrics.queue_metrics.get(*queue_name)
                .expect("Should have queue metrics");
            println!("  {}: length={}, tasks_processed={}, last_updated_ago={:?}",
                queue_name, queue_metrics.length, queue_metrics.tasks_processed,
                queue_metrics.last_updated.elapsed());
        }

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Cleanup behavior test passed");
    }

    // Test 2: Maintenance of queue history and metrics consistency
    {
        println!("Testing maintenance of queue history and metrics consistency");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_name = "history_queue";

        // Build up a history of operations
        let operations = vec![
            ("length", 5, 0.0, true),
            ("task", 0, 100.0, true),
            ("length", 10, 0.0, true),
            ("task", 0, 150.0, false),
            ("length", 8, 0.0, true),
            ("task", 0, 120.0, true),
            ("length", 15, 0.0, true),
            ("task", 0, 200.0, true),
            ("length", 12, 0.0, true),
            ("task", 0, 180.0, false),
        ];

        let mut expected_tasks_processed = 0;
        let mut expected_tasks_failed = 0;
        let mut expected_max_length = 0;
        let mut processing_times = Vec::new();

        // Apply operations and verify consistency at each step
        for (i, (operation, length, processing_time, success)) in operations.iter().enumerate() {
            match *operation {
                "length" => {
                    monitor.update_queue_metrics(queue_name, *length).await
                        .expect("Should update queue metrics");
                    expected_max_length = expected_max_length.max(*length);
                }
                "task" => {
                    monitor.record_task_processed(queue_name, *processing_time, *success).await
                        .expect("Should record task processed");
                    expected_tasks_processed += 1;
                    if !success {
                        expected_tasks_failed += 1;
                    }
                    processing_times.push(*processing_time);
                }
                _ => {}
            }

            sleep(Duration::from_millis(100)).await;

            // Verify consistency after each operation
            let metrics = monitor.get_metrics().await.expect("Should get metrics");
            let queue_metrics = metrics.queue_metrics.get(queue_name)
                .expect("Should have queue metrics");

            // Verify task counts are consistent
            assert_eq!(queue_metrics.tasks_processed, expected_tasks_processed,
                "After operation {}: tasks_processed should be {}", i + 1, expected_tasks_processed);
            assert_eq!(queue_metrics.tasks_failed, expected_tasks_failed,
                "After operation {}: tasks_failed should be {}", i + 1, expected_tasks_failed);

            // Verify max_length is maintained correctly
            assert_eq!(queue_metrics.max_length, expected_max_length,
                "After operation {}: max_length should be {}", i + 1, expected_max_length);

            // Verify average processing time is consistent
            if !processing_times.is_empty() {
                let expected_avg = processing_times.iter().sum::<f64>() / processing_times.len() as f64;
                assert!((queue_metrics.avg_processing_time_ms - expected_avg).abs() < 0.1,
                    "After operation {}: avg_processing_time should be {:.2}", i + 1, expected_avg);
            }

            println!("After operation {}: length={}, max_length={}, tasks_processed={}, tasks_failed={}",
                i + 1, queue_metrics.length, queue_metrics.max_length,
                queue_metrics.tasks_processed, queue_metrics.tasks_failed);
        }

        monitor.stop().await.expect("Queue monitor should stop");
        println!("Queue history maintenance test passed");
    }

    // Test 3: Resource cleanup during monitor shutdown
    {
        println!("Testing resource cleanup during monitor shutdown");

        let mut monitor = create_default_queue_monitor();

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        // Create multiple queues with various data
        let cleanup_queues = vec![
            ("cleanup_queue_1", 10, vec![(100.0, true), (150.0, false)]),
            ("cleanup_queue_2", 5, vec![(200.0, true), (250.0, true), (180.0, false)]),
            ("cleanup_queue_3", 15, vec![(300.0, false)]),
        ];

        // Populate queues with data
        for (queue_name, length, tasks) in &cleanup_queues {
            monitor.update_queue_metrics(queue_name, *length).await
                .expect("Should update queue metrics");

            for (processing_time, success) in tasks {
                monitor.record_task_processed(queue_name, *processing_time, *success).await
                    .expect("Should record task processed");
                sleep(Duration::from_millis(50)).await;
            }
        }

        // Verify queues are populated before shutdown
        let pre_shutdown_metrics = monitor.get_metrics().await.expect("Should get pre-shutdown metrics");
        assert_eq!(pre_shutdown_metrics.queue_metrics.len(), cleanup_queues.len(),
            "Should have {} queues before shutdown", cleanup_queues.len());

        let total_tasks_before = pre_shutdown_metrics.total_tasks;
        let total_failed_before = pre_shutdown_metrics.total_failed;

        println!("Before shutdown: {} queues, {} total tasks, {} total failed",
            pre_shutdown_metrics.queue_metrics.len(), total_tasks_before, total_failed_before);

        // Perform graceful shutdown
        let shutdown_start = Instant::now();
        monitor.stop().await.expect("Queue monitor should stop gracefully");
        let shutdown_duration = shutdown_start.elapsed();

        println!("Shutdown completed in {:?}", shutdown_duration);

        // Verify shutdown was reasonably fast (should be under 1 second for graceful shutdown)
        assert!(shutdown_duration.as_secs() < 5, "Shutdown should complete within 5 seconds");

        // After shutdown, the monitor should be in a clean state
        // Note: We can't easily verify internal cleanup without access to private fields,
        // but we can verify that the monitor behaves correctly after restart

        // Restart the monitor to verify clean state
        monitor.start().await.expect("Queue monitor should restart after cleanup");
        sleep(Duration::from_millis(100)).await;

        // Verify clean restart state
        let post_restart_metrics = monitor.get_metrics().await.expect("Should get post-restart metrics");

        // After restart, queues should still exist with their data
        // (The current implementation preserves queue data across start/stop cycles)
        assert_eq!(post_restart_metrics.queue_metrics.len(), cleanup_queues.len(),
            "Should have {} queues after restart", cleanup_queues.len());
        assert_eq!(post_restart_metrics.total_tasks, total_tasks_before,
            "Total tasks should be preserved after restart");
        assert_eq!(post_restart_metrics.total_failed, total_failed_before,
            "Total failed should be preserved after restart");

        println!("After restart: {} queues, {} total tasks, {} total failed",
            post_restart_metrics.queue_metrics.len(), post_restart_metrics.total_tasks,
            post_restart_metrics.total_failed);

        // Final cleanup
        monitor.stop().await.expect("Queue monitor should stop after test");
        println!("Resource cleanup test passed");
    }

    // Test 4: Memory and resource management during long-running operations
    {
        println!("Testing memory and resource management during long-running operations");

        let mut monitor = create_custom_queue_monitor(100, 100); // Fast polling

        // Import QueueMonitoring trait locally
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::QueueMonitoring;

        monitor.start().await.expect("Queue monitor should start");
        sleep(Duration::from_millis(100)).await;

        let queue_name = "resource_test_queue";

        // Simulate a long-running scenario with many operations
        let num_operations = 50;
        let mut total_tasks = 0;

        for i in 0..num_operations {
            // Vary the operations to test different code paths
            match i % 4 {
                0 => {
                    monitor.update_queue_metrics(queue_name, i % 20).await
                        .expect("Should update queue metrics");
                }
                1 | 2 | 3 => {
                    let processing_time = 100.0 + (i as f64 * 5.0);
                    let success = i % 3 != 0; // ~67% success rate
                    monitor.record_task_processed(queue_name, processing_time, success).await
                        .expect("Should record task processed");
                    total_tasks += 1;
                }
                _ => {}
            }

            // Short delay to allow background processing
            sleep(Duration::from_millis(20)).await;

            // Periodically verify the monitor is still responsive
            if i % 10 == 0 {
                let metrics = monitor.get_metrics().await.expect("Should get metrics during long run");
                assert!(metrics.queue_metrics.contains_key(queue_name),
                    "Queue should still exist during long run at iteration {}", i);

                // Verify timestamps are being updated (indicates active monitoring)
                assert!(metrics.last_updated.elapsed().as_millis() < 2000,
                    "Metrics should be recently updated at iteration {}", i);
            }
        }

        // Final verification after long-running operations
        let final_metrics = monitor.get_metrics().await.expect("Should get final metrics");
        let queue_metrics = final_metrics.queue_metrics.get(queue_name)
            .expect("Should have queue metrics after long run");

        assert_eq!(queue_metrics.tasks_processed, total_tasks,
            "Should have processed {} tasks after long run", total_tasks);

        // Verify the monitor is still responsive and functional
        monitor.update_queue_metrics(queue_name, 999).await
            .expect("Should still be able to update metrics after long run");

        let responsive_metrics = monitor.get_metrics().await.expect("Should get responsive metrics");
        let responsive_queue = responsive_metrics.queue_metrics.get(queue_name)
            .expect("Should have responsive queue metrics");
        assert_eq!(responsive_queue.length, 999, "Should respond to updates after long run");

        println!("Long-running operations completed: {} tasks processed, monitor remains responsive",
            total_tasks);

        monitor.stop().await.expect("Queue monitor should stop after long run");
        println!("Memory and resource management test passed");
    }

    println!("Queue metrics cleanup and maintenance test passed");
}

// ===== Queue Monitor Lifecycle Tests =====

/// Test queue monitor initialization and configuration
#[tokio::test]
async fn test_queue_monitor_initialization_and_configuration() {
    println!("Starting Queue Monitor initialization and configuration test");

    // Test 1: Default configuration initialization
    {
        println!("Testing QueueMonitor initialization with default configuration");
        let monitor = create_default_queue_monitor();

        // Verify monitor was created successfully
        assert_eq!(monitor.get_name(), "QueueMonitor", "Monitor should have correct name");

        // Test that monitor can provide status
        let status_result = monitor.get_status().await;
        assert!(status_result.is_ok(), "Monitor should be able to provide status after creation");

        let status = status_result.unwrap();
        assert_eq!(status, "Stopped", "Monitor should be stopped initially");

        // Test that monitor can provide metrics
        let metrics_result = monitor.get_metrics().await;
        assert!(metrics_result.is_ok(), "Monitor should be able to provide metrics");

        let metrics = metrics_result.unwrap();
        assert_eq!(metrics.queue_metrics.len(), 0, "Should have no queue metrics initially");
        assert_eq!(metrics.total_tasks, 0, "Should have no total tasks initially");
        assert_eq!(metrics.total_failed, 0, "Should have no failed tasks initially");

        // Test that monitor can provide metrics as JSON
        let metrics_json_result = monitor.get_metrics_json().await;
        assert!(metrics_json_result.is_ok(), "Monitor should be able to provide metrics as JSON");

        let metrics_json = metrics_json_result.unwrap();
        assert!(!metrics_json.is_empty(), "Metrics JSON should not be empty");
        assert!(metrics_json.contains("queues"), "Metrics JSON should contain queues field");
        assert!(metrics_json.contains("total_tasks"), "Metrics JSON should contain total_tasks field");

        println!("Default configuration initialization test passed");
    }

    // Test 2: Custom configuration initialization
    {
        println!("Testing QueueMonitor initialization with custom configuration");
        let monitor = create_custom_queue_monitor(500, 750);

        // Verify monitor was created successfully
        assert_eq!(monitor.get_name(), "QueueMonitor", "Monitor should have correct name");

        // Test that monitor can provide status
        let status_result = monitor.get_status().await;
        assert!(status_result.is_ok(), "Monitor should be able to provide status after creation");

        let status = status_result.unwrap();
        assert_eq!(status, "Stopped", "Monitor should be stopped initially");

        // Test that monitor can provide metrics
        let metrics_result = monitor.get_metrics().await;
        assert!(metrics_result.is_ok(), "Monitor should be able to provide metrics");

        println!("Custom configuration initialization test passed");
    }

    // Test 3: Fast polling configuration for testing
    {
        println!("Testing QueueMonitor initialization with fast polling configuration");
        let monitor = create_fast_queue_monitor();

        // Verify monitor was created successfully
        assert_eq!(monitor.get_name(), "QueueMonitor", "Monitor should have correct name");

        // Test that monitor can provide status
        let status_result = monitor.get_status().await;
        assert!(status_result.is_ok(), "Monitor should be able to provide status after creation");

        println!("Fast polling configuration initialization test passed");
    }

    // Test 4: Custom task history configuration
    {
        println!("Testing QueueMonitor initialization with custom task history");
        let monitor = create_queue_monitor_with_history(50);

        // Verify monitor was created successfully
        assert_eq!(monitor.get_name(), "QueueMonitor", "Monitor should have correct name");

        // Test that monitor can provide status
        let status_result = monitor.get_status().await;
        assert!(status_result.is_ok(), "Monitor should be able to provide status after creation");

        println!("Custom task history configuration initialization test passed");
    }

    println!("Queue Monitor initialization and configuration test passed");
}

/// Test queue monitoring task management
#[tokio::test]
async fn test_queue_monitoring_task_management() {
    println!("Starting Queue Monitor task management test");

    // Test 1: Queue metrics creation and management
    {
        println!("Testing queue metrics creation and management");
        let mut monitor = create_fast_queue_monitor();

        // Initially no queues should exist
        let initial_metrics = monitor.get_metrics().await.expect("Should get initial metrics");
        assert_eq!(initial_metrics.queue_metrics.len(), 0, "Should have no queues initially");

        // Update metrics for a new queue
        let queue_name = "test_queue_1";
        let update_result = monitor.update_queue_metrics(queue_name, 5).await;
        assert!(update_result.is_ok(), "Should be able to update queue metrics");

        // Verify queue was created
        let metrics_after_update = monitor.get_metrics().await.expect("Should get metrics after update");
        assert_eq!(metrics_after_update.queue_metrics.len(), 1, "Should have one queue after update");
        assert!(metrics_after_update.queue_metrics.contains_key(queue_name), "Should contain the test queue");

        let queue_metrics = metrics_after_update.queue_metrics.get(queue_name).unwrap();
        assert_eq!(queue_metrics.length, 5, "Queue length should be 5");
        assert_eq!(queue_metrics.max_length, 5, "Max length should be 5");
        assert_eq!(queue_metrics.tasks_processed, 0, "No tasks processed yet");
        assert_eq!(queue_metrics.tasks_failed, 0, "No tasks failed yet");

        println!("Queue metrics creation test passed");
    }

    // Test 2: Multiple queue management
    {
        println!("Testing multiple queue management");
        let mut monitor = create_fast_queue_monitor();

        let queue_names = vec!["queue_a", "queue_b", "queue_c"];
        let queue_lengths = vec![3, 7, 12];

        // Create multiple queues
        for (queue_name, length) in queue_names.iter().zip(queue_lengths.iter()) {
            let update_result = monitor.update_queue_metrics(queue_name, *length).await;
            assert!(update_result.is_ok(), "Should be able to update queue metrics for {}", queue_name);
        }

        // Verify all queues were created
        let metrics = monitor.get_metrics().await.expect("Should get metrics");
        assert_eq!(metrics.queue_metrics.len(), 3, "Should have three queues");

        for (queue_name, expected_length) in queue_names.iter().zip(queue_lengths.iter()) {
            assert!(metrics.queue_metrics.contains_key(*queue_name), "Should contain queue {}", queue_name);
            let queue_metrics = metrics.queue_metrics.get(*queue_name).unwrap();
            assert_eq!(queue_metrics.length, *expected_length, "Queue {} should have length {}", queue_name, expected_length);
        }

        println!("Multiple queue management test passed");
    }

    // Test 3: Task processing recording
    {
        println!("Testing task processing recording");
        let mut monitor = create_fast_queue_monitor();

        let queue_name = "processing_queue";
        
        // Create queue and record successful task processing
        monitor.update_queue_metrics(queue_name, 2).await.expect("Should update queue metrics");
        
        let record_result = monitor.record_task_processed(queue_name, 150.0, true).await;
        assert!(record_result.is_ok(), "Should be able to record successful task processing");

        // Verify metrics were updated
        let metrics = monitor.get_metrics().await.expect("Should get metrics");
        let queue_metrics = metrics.queue_metrics.get(queue_name).unwrap();
        assert_eq!(queue_metrics.tasks_processed, 1, "Should have one processed task");
        assert_eq!(queue_metrics.tasks_failed, 0, "Should have no failed tasks");
        assert_eq!(queue_metrics.avg_processing_time_ms, 150.0, "Average processing time should be 150ms");
        assert_eq!(metrics.total_tasks, 1, "Total tasks should be 1");
        assert_eq!(metrics.total_failed, 0, "Total failed should be 0");

        // Record a failed task
        let record_failed_result = monitor.record_task_processed(queue_name, 200.0, false).await;
        assert!(record_failed_result.is_ok(), "Should be able to record failed task processing");

        // Verify metrics were updated for failure
        let metrics_after_failure = monitor.get_metrics().await.expect("Should get metrics after failure");
        let queue_metrics_after_failure = metrics_after_failure.queue_metrics.get(queue_name).unwrap();
        assert_eq!(queue_metrics_after_failure.tasks_processed, 2, "Should have two processed tasks");
        assert_eq!(queue_metrics_after_failure.tasks_failed, 1, "Should have one failed task");
        assert_eq!(queue_metrics_after_failure.avg_processing_time_ms, 175.0, "Average processing time should be 175ms");
        assert_eq!(metrics_after_failure.total_tasks, 2, "Total tasks should be 2");
        assert_eq!(metrics_after_failure.total_failed, 1, "Total failed should be 1");

        println!("Task processing recording test passed");
    }

    // Test 4: Queue length tracking with max length
    {
        println!("Testing queue length tracking with max length");
        let mut monitor = create_fast_queue_monitor();

        let queue_name = "length_tracking_queue";
        
        // Update queue length multiple times
        let lengths = vec![1, 5, 3, 8, 2, 10, 4];
        for length in lengths {
            monitor.update_queue_metrics(queue_name, length).await.expect("Should update queue metrics");
        }

        // Verify max length was tracked correctly
        let metrics = monitor.get_metrics().await.expect("Should get metrics");
        let queue_metrics = metrics.queue_metrics.get(queue_name).unwrap();
        assert_eq!(queue_metrics.length, 4, "Current length should be 4 (last update)");
        assert_eq!(queue_metrics.max_length, 10, "Max length should be 10");

        println!("Queue length tracking test passed");
    }

    println!("Queue Monitor task management test passed");
}

/// Test queue monitor start/stop operations
#[tokio::test]
async fn test_queue_monitor_start_stop_operations() {
    println!("Starting Queue Monitor start/stop operations test");

    // Test 1: Basic start/stop cycle
    {
        println!("Testing basic start/stop cycle");
        let mut monitor = create_fast_queue_monitor();

        // Initially monitor should be stopped
        let initial_status = monitor.get_status().await.expect("Should get initial status");
        assert_eq!(initial_status, "Stopped", "Monitor should be stopped initially");

        // Start the monitor
        let start_result = monitor.start().await;
        assert!(start_result.is_ok(), "Should be able to start monitor");

        // Verify monitor is running
        let running_status = monitor.get_status().await.expect("Should get running status");
        assert_eq!(running_status, "Running", "Monitor should be running after start");

        // Allow some time for monitoring loop to run
        sleep(Duration::from_millis(250)).await;

        // Stop the monitor
        let stop_result = monitor.stop().await;
        assert!(stop_result.is_ok(), "Should be able to stop monitor");

        // Verify monitor is stopped
        let stopped_status = monitor.get_status().await.expect("Should get stopped status");
        assert_eq!(stopped_status, "Stopped", "Monitor should be stopped after stop");

        println!("Basic start/stop cycle test passed");
    }

    // Test 2: Multiple start/stop cycles
    {
        println!("Testing multiple start/stop cycles");
        let mut monitor = create_fast_queue_monitor();

        for cycle in 1..=3 {
            println!("Starting cycle {}", cycle);

            // Start the monitor
            let start_result = monitor.start().await;
            assert!(start_result.is_ok(), "Should be able to start monitor in cycle {}", cycle);

            // Verify monitor is running
            let running_status = monitor.get_status().await.expect("Should get running status");
            assert_eq!(running_status, "Running", "Monitor should be running in cycle {}", cycle);

            // Allow some monitoring time
            sleep(Duration::from_millis(150)).await;

            // Stop the monitor
            let stop_result = monitor.stop().await;
            assert!(stop_result.is_ok(), "Should be able to stop monitor in cycle {}", cycle);

            // Verify monitor is stopped
            let stopped_status = monitor.get_status().await.expect("Should get stopped status");
            assert_eq!(stopped_status, "Stopped", "Monitor should be stopped in cycle {}", cycle);

            // Brief pause between cycles
            sleep(Duration::from_millis(50)).await;
        }

        println!("Multiple start/stop cycles test passed");
    }

    // Test 3: Start when already running
    {
        println!("Testing start when already running");
        let mut monitor = create_fast_queue_monitor();

        // Start the monitor
        let first_start_result = monitor.start().await;
        assert!(first_start_result.is_ok(), "Should be able to start monitor first time");

        // Try to start again (should succeed but log warning)
        let second_start_result = monitor.start().await;
        assert!(second_start_result.is_ok(), "Should handle starting already running monitor gracefully");

        // Verify monitor is still running
        let status = monitor.get_status().await.expect("Should get status");
        assert_eq!(status, "Running", "Monitor should still be running");

        // Stop the monitor
        let stop_result = monitor.stop().await;
        assert!(stop_result.is_ok(), "Should be able to stop monitor");

        println!("Start when already running test passed");
    }

    // Test 4: Stop when already stopped
    {
        println!("Testing stop when already stopped");
        let mut monitor = create_fast_queue_monitor();

        // Monitor should be stopped initially
        let initial_status = monitor.get_status().await.expect("Should get initial status");
        assert_eq!(initial_status, "Stopped", "Monitor should be stopped initially");

        // Try to stop (should succeed but log warning)
        let stop_result = monitor.stop().await;
        assert!(stop_result.is_ok(), "Should handle stopping already stopped monitor gracefully");

        // Verify monitor is still stopped
        let status = monitor.get_status().await.expect("Should get status");
        assert_eq!(status, "Stopped", "Monitor should still be stopped");

        println!("Stop when already stopped test passed");
    }

    // Test 5: Monitoring loop functionality
    {
        println!("Testing monitoring loop functionality");
        let mut monitor = create_fast_queue_monitor();

        // Add some queue data before starting
        monitor.update_queue_metrics("test_queue", 3).await.expect("Should update queue metrics");
        monitor.record_task_processed("test_queue", 100.0, true).await.expect("Should record task");

        // Get initial metrics timestamp
        let initial_metrics = monitor.get_metrics().await.expect("Should get initial metrics");
        let initial_timestamp = initial_metrics.last_updated;

        // Start the monitor
        monitor.start().await.expect("Should start monitor");

        // Wait for monitoring loop to run
        sleep(Duration::from_millis(300)).await;

        // Get updated metrics
        let updated_metrics = monitor.get_metrics().await.expect("Should get updated metrics");
        
        // Verify that the monitoring loop has updated the timestamp
        assert!(updated_metrics.last_updated > initial_timestamp, 
            "Monitoring loop should have updated the timestamp");

        // Stop the monitor
        monitor.stop().await.expect("Should stop monitor");

        println!("Monitoring loop functionality test passed");
    }

    println!("Queue Monitor start/stop operations test passed");
}

/// Test queue monitor error handling and recovery
#[tokio::test]
async fn test_queue_monitor_error_handling_and_recovery() {
    println!("Starting Queue Monitor error handling and recovery test");

    // Test 1: Invalid queue operations
    {
        println!("Testing invalid queue operations");
        let mut monitor = create_fast_queue_monitor();

        // Try to record task processing for non-existent queue (should create queue)
        let record_result = monitor.record_task_processed("non_existent_queue", 100.0, true).await;
        assert!(record_result.is_ok(), "Should handle recording task for non-existent queue by creating it");

        // Verify queue was created
        let metrics = monitor.get_metrics().await.expect("Should get metrics");
        assert!(metrics.queue_metrics.contains_key("non_existent_queue"), "Queue should have been created");

        println!("Invalid queue operations test passed");
    }

    // Test 2: Concurrent operations
    {
        println!("Testing concurrent operations");
        let mut monitor = create_fast_queue_monitor();

        // Start the monitor
        monitor.start().await.expect("Should start monitor");

        // Perform concurrent operations
        let queue_name = "concurrent_queue";
        
        // Spawn multiple tasks that update metrics concurrently
        let mut handles = vec![];
        
        for i in 0..5 {
            let mut monitor_clone = create_fast_queue_monitor();
            let queue_name_clone = queue_name.to_string();
            
            let handle = tokio::spawn(async move {
                // Update queue metrics
                monitor_clone.update_queue_metrics(&queue_name_clone, i + 1).await.expect("Should update metrics");
                
                // Record task processing
                monitor_clone.record_task_processed(&queue_name_clone, (i + 1) as f64 * 50.0, true).await.expect("Should record task");
            });
            
            handles.push(handle);
        }

        // Wait for all concurrent operations to complete
        for handle in handles {
            handle.await.expect("Concurrent operation should complete");
        }

        // Allow some time for processing
        sleep(Duration::from_millis(200)).await;

        // Stop the monitor
        monitor.stop().await.expect("Should stop monitor");

        println!("Concurrent operations test passed");
    }

    // Test 3: Recovery after errors
    {
        println!("Testing recovery after errors");
        let mut monitor = create_fast_queue_monitor();

        // Start monitor
        monitor.start().await.expect("Should start monitor");

        // Perform normal operations
        let queue_name = "recovery_queue";
        monitor.update_queue_metrics(queue_name, 5).await.expect("Should update metrics");
        monitor.record_task_processed(queue_name, 100.0, true).await.expect("Should record task");

        // Verify normal operation
        let metrics_before = monitor.get_metrics().await.expect("Should get metrics before");
        assert!(metrics_before.queue_metrics.contains_key(queue_name), "Queue should exist before recovery test");

        // Simulate recovery by stopping and restarting
        monitor.stop().await.expect("Should stop monitor");
        sleep(Duration::from_millis(100)).await;
        monitor.start().await.expect("Should restart monitor");

        // Verify monitor can still operate after restart
        let status_after_restart = monitor.get_status().await.expect("Should get status after restart");
        assert_eq!(status_after_restart, "Running", "Monitor should be running after restart");

        // Perform operations after restart
        monitor.update_queue_metrics(queue_name, 8).await.expect("Should update metrics after restart");
        monitor.record_task_processed(queue_name, 150.0, true).await.expect("Should record task after restart");

        // Verify operations work after restart
        let metrics_after = monitor.get_metrics().await.expect("Should get metrics after restart");
        let queue_metrics = metrics_after.queue_metrics.get(queue_name).unwrap();
        assert_eq!(queue_metrics.length, 8, "Queue length should be updated after restart");
        assert_eq!(queue_metrics.tasks_processed, 2, "Should have two processed tasks after restart");

        // Stop monitor
        monitor.stop().await.expect("Should stop monitor");

        println!("Recovery after errors test passed");
    }

    // Test 3: Stress testing with rapid operations
    {
        println!("Testing stress conditions with rapid operations");
        let mut monitor = create_fast_queue_monitor();

        // Start monitor
        monitor.start().await.expect("Should start monitor");

        let queue_name = "stress_queue";
        
        // Perform rapid operations
        for i in 0..50 {
            // Update queue length
            monitor.update_queue_metrics(queue_name, i % 10).await.expect("Should update metrics under stress");
            
            // Record task processing
            let success = i % 3 != 0; // Some failures
            monitor.record_task_processed(queue_name, (i as f64) * 10.0, success).await.expect("Should record task under stress");
            
            // Brief pause to avoid overwhelming
            if i % 10 == 0 {
                sleep(Duration::from_millis(10)).await;
            }
        }

        // Allow processing time
        sleep(Duration::from_millis(200)).await;

        // Verify final state
        let final_metrics = monitor.get_metrics().await.expect("Should get final metrics under stress");
        let queue_metrics = final_metrics.queue_metrics.get(queue_name).unwrap();
        assert_eq!(queue_metrics.tasks_processed, 50, "Should have processed 50 tasks under stress");
        assert!(queue_metrics.tasks_failed > 0, "Should have some failed tasks under stress");
        assert_eq!(final_metrics.total_tasks, 50, "Total tasks should be 50 under stress");

        // Stop monitor
        monitor.stop().await.expect("Should stop monitor under stress");

        println!("Stress testing test passed");
    }

    // Test 4: JSON serialization error handling
    {
        println!("Testing JSON serialization robustness");
        let mut monitor = create_fast_queue_monitor();

        // Add various types of data
        let queue_names = vec!["queue_1", "queue_2", "queue_with_special_chars_!@#"];
        
        for queue_name in &queue_names {
            monitor.update_queue_metrics(queue_name, 5).await.expect("Should update metrics");
            monitor.record_task_processed(queue_name, 123.456, true).await.expect("Should record task");
        }

        // Test JSON serialization
        let json_result = monitor.get_metrics_json().await;
        assert!(json_result.is_ok(), "Should be able to serialize metrics to JSON");

        let json_string = json_result.unwrap();
        assert!(!json_string.is_empty(), "JSON string should not be empty");
        assert!(json_string.contains("queue_1"), "JSON should contain queue_1");
        assert!(json_string.contains("queue_2"), "JSON should contain queue_2");
        assert!(json_string.contains("queue_with_special_chars"), "JSON should contain special chars queue");

        // Verify JSON is valid by attempting to parse it
        let parse_result = serde_json::from_str::<serde_json::Value>(&json_string);
        assert!(parse_result.is_ok(), "Generated JSON should be valid and parseable");

        println!("JSON serialization robustness test passed");
    }

    println!("Queue Monitor error handling and recovery test passed");
}

/// Test Queue Monitor integration with task execution systems
#[tokio::test]
async fn test_queue_monitor_task_execution_integration() {
    println!("Starting Queue Monitor task execution integration test");

    // Test 1: Integration with simulated task execution pipeline
    {
        println!("Testing integration with task execution pipeline");
        let mut monitor = create_fast_queue_monitor();
        
        // Import traits locally for this test block
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{Monitorable, QueueMonitoring};
        
        // Start monitor
        monitor.start().await.expect("Should start monitor");
        
        // Simulate a complete task execution pipeline
        let queue_names = vec!["high_priority", "medium_priority", "low_priority"];
        let task_counts = vec![10, 15, 20];
        
        // Phase 1: Tasks being queued (increasing queue lengths)
        for (queue_name, task_count) in queue_names.iter().zip(task_counts.iter()) {
            for i in 1..=*task_count {
                monitor.update_queue_metrics(queue_name, i).await.expect("Should update queue metrics");
                sleep(Duration::from_millis(10)).await; // Simulate queuing delay
            }
        }
        
        // Verify queues are populated
        let metrics_after_queuing = monitor.get_metrics().await.expect("Should get metrics after queuing");
        for (queue_name, expected_count) in queue_names.iter().zip(task_counts.iter()) {
            let queue_metrics = metrics_after_queuing.queue_metrics.get(*queue_name).unwrap();
            assert_eq!(queue_metrics.length, *expected_count, "Queue {} should have {} tasks", queue_name, expected_count);
        }
        
        // Phase 2: Tasks being processed (decreasing queue lengths, recording processing)
        for (queue_name, task_count) in queue_names.iter().zip(task_counts.iter()) {
            for i in (0..*task_count).rev() {
                // Simulate task processing time
                let processing_time = match *queue_name {
                    "high_priority" => 50.0 + (i as f64 * 5.0), // Faster processing
                    "medium_priority" => 100.0 + (i as f64 * 10.0), // Medium processing
                    "low_priority" => 200.0 + (i as f64 * 15.0), // Slower processing
                    _ => 100.0,
                };
                
                // Update queue length (task removed)
                monitor.update_queue_metrics(queue_name, i).await.expect("Should update queue metrics");
                
                // Record task completion
                let success = i % 7 != 0; // Some failures for realism
                monitor.record_task_processed(queue_name, processing_time, success).await.expect("Should record task processed");
                
                sleep(Duration::from_millis(5)).await; // Simulate processing delay
            }
        }
        
        // Allow final processing
        sleep(Duration::from_millis(200)).await;
        
        // Phase 3: Verify final execution metrics
        let final_metrics = monitor.get_metrics().await.expect("Should get final metrics");
        
        // Verify all queues are empty
        for queue_name in &queue_names {
            let queue_metrics = final_metrics.queue_metrics.get(*queue_name).unwrap();
            assert_eq!(queue_metrics.length, 0, "Queue {} should be empty after processing", queue_name);
        }
        
        // Verify processing metrics
        let total_expected_tasks: usize = task_counts.iter().sum();
        assert_eq!(final_metrics.total_tasks, total_expected_tasks, "Should have processed all tasks");
        assert!(final_metrics.total_failed > 0, "Should have some failed tasks for realism");
        
        // Verify priority-based processing times
        let high_priority_metrics = final_metrics.queue_metrics.get("high_priority").unwrap();
        let low_priority_metrics = final_metrics.queue_metrics.get("low_priority").unwrap();
        assert!(high_priority_metrics.avg_processing_time_ms < low_priority_metrics.avg_processing_time_ms,
            "High priority tasks should process faster than low priority");
        
        monitor.stop().await.expect("Should stop monitor");
        println!("Task execution pipeline integration test passed");
    }
    
    // Test 2: Integration with concurrent task execution
    {
        println!("Testing integration with concurrent task execution");
        let mut monitor = create_fast_queue_monitor();
        
        // Import traits locally for this test block
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{Monitorable, QueueMonitoring};
        
        monitor.start().await.expect("Should start monitor");
        
        let queue_name = "concurrent_queue";
        let concurrent_tasks = 5;
        let tasks_per_worker = 8;
        
        // Simulate concurrent workers processing tasks
        let mut handles = vec![];
        
        for worker_id in 0..concurrent_tasks {
            let mut worker_monitor = create_fast_queue_monitor();
            worker_monitor.start().await.expect("Should start worker monitor");
            
            let handle = tokio::spawn(async move {
                for task_id in 0..tasks_per_worker {
                    // Simulate variable processing times
                    let processing_time = 50.0 + (worker_id as f64 * 20.0) + (task_id as f64 * 10.0);
                    let success = (worker_id + task_id) % 5 != 0; // Some failures
                    
                    worker_monitor.record_task_processed(queue_name, processing_time, success).await.expect("Should record task");
                    sleep(Duration::from_millis(20)).await;
                }
                worker_monitor.stop().await.expect("Should stop worker monitor");
            });
            
            handles.push(handle);
        }
        
        // Wait for all workers to complete
        for handle in handles {
            handle.await.expect("Worker should complete successfully");
        }
        
        // Record tasks in main monitor for comparison
        for i in 0..(concurrent_tasks * tasks_per_worker) {
            let processing_time = 100.0 + (i as f64 * 5.0);
            let success = i % 4 != 0;
            monitor.record_task_processed(queue_name, processing_time, success).await.expect("Should record task");
        }
        
        sleep(Duration::from_millis(100)).await;
        
        let final_metrics = monitor.get_metrics().await.expect("Should get final metrics");
        let queue_metrics = final_metrics.queue_metrics.get(queue_name).unwrap();
        
        assert_eq!(queue_metrics.tasks_processed, concurrent_tasks * tasks_per_worker,
            "Should have processed all concurrent tasks");
        assert!(queue_metrics.avg_processing_time_ms > 0.0, "Should have valid average processing time");
        
        monitor.stop().await.expect("Should stop monitor");
        println!("Concurrent task execution integration test passed");
    }
    
    println!("Queue Monitor task execution integration test passed");
}

/// Test Queue Monitor correlation with system resources
#[tokio::test]
async fn test_queue_monitor_system_resource_correlation() {
    println!("Starting Queue Monitor system resource correlation test");
    
    // Test 1: Correlation between queue metrics and simulated system load
    {
        println!("Testing correlation with simulated system load");
        let mut monitor = create_fast_queue_monitor();
        
        // Import traits locally for this test block
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{Monitorable, QueueMonitoring};
        
        monitor.start().await.expect("Should start monitor");
        
        // Simulate different system load scenarios
        let scenarios = vec![
            ("low_load", 5, 50.0, "Low system load scenario"),
            ("medium_load", 15, 150.0, "Medium system load scenario"),
            ("high_load", 30, 300.0, "High system load scenario"),
        ];
        
        for (queue_name, queue_length, base_processing_time, description) in scenarios {
            println!("Testing {}", description);
            
            // Simulate queue building up under load
            monitor.update_queue_metrics(queue_name, queue_length).await.expect("Should update queue metrics");
            
            // Simulate tasks with processing times correlated to system load
            for i in 0..queue_length {
                let load_factor = 1.0 + (queue_length as f64 / 10.0); // Higher load = slower processing
                let processing_time = base_processing_time * load_factor + (i as f64 * 5.0);
                let success = i % 8 != 0; // Some failures under load
                
                monitor.record_task_processed(queue_name, processing_time, success).await.expect("Should record task");
                
                // Update queue length as tasks are processed
                let remaining = queue_length - i - 1;
                monitor.update_queue_metrics(queue_name, remaining).await.expect("Should update queue metrics");
                
                sleep(Duration::from_millis(10)).await;
            }
        }
        
        sleep(Duration::from_millis(200)).await;
        
        // Verify correlation between load and processing times
        let final_metrics = monitor.get_metrics().await.expect("Should get final metrics");
        
        let low_load_metrics = final_metrics.queue_metrics.get("low_load").unwrap();
        let medium_load_metrics = final_metrics.queue_metrics.get("medium_load").unwrap();
        let high_load_metrics = final_metrics.queue_metrics.get("high_load").unwrap();
        
        // Verify processing times increase with load
        assert!(low_load_metrics.avg_processing_time_ms < medium_load_metrics.avg_processing_time_ms,
            "Medium load should have higher processing times than low load");
        assert!(medium_load_metrics.avg_processing_time_ms < high_load_metrics.avg_processing_time_ms,
            "High load should have higher processing times than medium load");
        
        // Verify max queue lengths correlate with load
        assert!(low_load_metrics.max_length < medium_load_metrics.max_length,
            "Medium load should have higher max queue length than low load");
        assert!(medium_load_metrics.max_length < high_load_metrics.max_length,
            "High load should have higher max queue length than medium load");
        
        monitor.stop().await.expect("Should stop monitor");
        println!("System load correlation test passed");
    }
    
    // Test 2: Resource-aware queue prioritization simulation
    {
        println!("Testing resource-aware queue prioritization");
        let mut monitor = create_fast_queue_monitor();
        
        // Import traits locally for this test block
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{Monitorable, QueueMonitoring};
        
        monitor.start().await.expect("Should start monitor");
        
        // Simulate different resource types with different characteristics
        let resource_queues = vec![
            ("cpu_intensive", 20, 80.0, 0.95), // High success rate, moderate time
            ("memory_intensive", 15, 120.0, 0.90), // Good success rate, longer time
            ("io_intensive", 25, 200.0, 0.85), // Lower success rate, longest time
            ("network_intensive", 18, 150.0, 0.88), // Moderate success rate and time
        ];
        
        for (queue_name, task_count, base_time, success_rate) in resource_queues {
            println!("Processing {} queue with {} tasks", queue_name, task_count);
            
            // Set initial queue length
            monitor.update_queue_metrics(queue_name, task_count).await.expect("Should update queue metrics");
            
            // Process tasks with resource-specific characteristics
            for i in 0..task_count {
                let processing_time = base_time + (i as f64 * 10.0);
                let success = (i as f64 / task_count as f64) < success_rate;
                
                monitor.record_task_processed(queue_name, processing_time, success).await.expect("Should record task");
                
                // Update remaining queue length
                let remaining = task_count - i - 1;
                monitor.update_queue_metrics(queue_name, remaining).await.expect("Should update queue metrics");
                
                sleep(Duration::from_millis(8)).await;
            }
        }
        
        sleep(Duration::from_millis(150)).await;
        
        // Verify resource-specific patterns
        let final_metrics = monitor.get_metrics().await.expect("Should get final metrics");
        
        let cpu_metrics = final_metrics.queue_metrics.get("cpu_intensive").unwrap();
        let io_metrics = final_metrics.queue_metrics.get("io_intensive").unwrap();
        
        // CPU intensive should be faster than IO intensive
        assert!(cpu_metrics.avg_processing_time_ms < io_metrics.avg_processing_time_ms,
            "CPU intensive tasks should be faster than IO intensive tasks");
        
        // CPU intensive should have fewer failures than IO intensive
        let cpu_failure_rate = cpu_metrics.tasks_failed as f64 / cpu_metrics.tasks_processed as f64;
        let io_failure_rate = io_metrics.tasks_failed as f64 / io_metrics.tasks_processed as f64;
        assert!(cpu_failure_rate < io_failure_rate,
            "CPU intensive tasks should have lower failure rate than IO intensive tasks");
        
        monitor.stop().await.expect("Should stop monitor");
        println!("Resource-aware queue prioritization test passed");
    }
    
    println!("Queue Monitor system resource correlation test passed");
}

/// Test Queue Monitor performance under load
#[tokio::test]
async fn test_queue_monitor_performance_under_load() {
    println!("Starting Queue Monitor performance under load test");
    
    // Test 1: High-frequency task processing performance
    {
        println!("Testing high-frequency task processing performance");
        let mut monitor = create_fast_queue_monitor();
        
        // Import traits locally for this test block
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{Monitorable, QueueMonitoring};
        
        monitor.start().await.expect("Should start monitor");
        
        let queue_name = "high_frequency_queue";
        let total_tasks = 1000;
        let batch_size = 50;
        
        let start_time = std::time::Instant::now();
        
        // Process tasks in batches to simulate high load
        for batch in 0..(total_tasks / batch_size) {
            let batch_start = batch * batch_size;
            let batch_end = (batch + 1) * batch_size;
            
            // Update queue length for this batch
            monitor.update_queue_metrics(queue_name, batch_end).await.expect("Should update queue metrics");
            
            // Process batch of tasks rapidly
            for i in batch_start..batch_end {
                let processing_time = 10.0 + (i % 20) as f64; // Variable but fast processing
                let success = i % 10 != 0; // 90% success rate
                
                monitor.record_task_processed(queue_name, processing_time, success).await.expect("Should record task");
                
                // Minimal delay to simulate high frequency
                if i % 10 == 0 {
                    sleep(Duration::from_millis(1)).await;
                }
            }
            
            // Update queue length after batch processing
            let remaining = total_tasks - batch_end;
            monitor.update_queue_metrics(queue_name, remaining).await.expect("Should update queue metrics");
        }
        
        let processing_duration = start_time.elapsed();
        
        // Allow final metrics collection
        sleep(Duration::from_millis(100)).await;
        
        let final_metrics = monitor.get_metrics().await.expect("Should get final metrics");
        let queue_metrics = final_metrics.queue_metrics.get(queue_name).unwrap();
        
        // Verify performance metrics
        assert_eq!(queue_metrics.tasks_processed, total_tasks, "Should have processed all tasks");
        assert!(queue_metrics.avg_processing_time_ms > 0.0, "Should have valid average processing time");
        assert!(queue_metrics.avg_processing_time_ms < 50.0, "Average processing time should be reasonable under load");
        
        // Verify throughput (tasks per second)
        let throughput = total_tasks as f64 / processing_duration.as_secs_f64();
        assert!(throughput > 100.0, "Should achieve reasonable throughput: {} tasks/sec", throughput);
        
        println!("Processed {} tasks in {:.2}s (throughput: {:.1} tasks/sec)", 
            total_tasks, processing_duration.as_secs_f64(), throughput);
        
        monitor.stop().await.expect("Should stop monitor");
        println!("High-frequency task processing performance test passed");
    }
    
    // Test 2: Sustained load over time
    {
        println!("Testing sustained load over time");
        let mut monitor = create_fast_queue_monitor();
        
        // Import traits locally for this test block
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{Monitorable, QueueMonitoring};
        
        monitor.start().await.expect("Should start monitor");
        
        let queue_name = "sustained_load_queue";
        let duration_seconds = 3;
        let tasks_per_second = 50;
        let total_expected_tasks = duration_seconds * tasks_per_second;
        
        let start_time = std::time::Instant::now();
        let mut task_counter = 0;
        
        // Simulate sustained load for specified duration
        while start_time.elapsed().as_secs() < duration_seconds {
            let current_second = start_time.elapsed().as_secs();
            
            // Process tasks for this second
            for _ in 0..tasks_per_second {
                task_counter += 1;
                
                // Simulate varying queue length
                let queue_length = 10 + (task_counter % 20);
                monitor.update_queue_metrics(queue_name, queue_length).await.expect("Should update queue metrics");
                
                // Simulate task processing with some variation
                let processing_time = 15.0 + (current_second as f64 * 2.0) + (task_counter % 10) as f64;
                let success = task_counter % 12 != 0; // ~92% success rate
                
                monitor.record_task_processed(queue_name, processing_time, success).await.expect("Should record task");
                
                // Small delay to maintain target rate
                sleep(Duration::from_millis(20)).await;
            }
        }
        
        let actual_duration = start_time.elapsed();
        
        // Allow final processing
        sleep(Duration::from_millis(200)).await;
        
        let final_metrics = monitor.get_metrics().await.expect("Should get final metrics");
        let queue_metrics = final_metrics.queue_metrics.get(queue_name).unwrap();
        
        // Verify sustained performance
         assert!(queue_metrics.tasks_processed >= ((total_expected_tasks * 80 / 100) as usize), 
             "Should process at least 80% of expected tasks under sustained load");
        
        let actual_throughput = queue_metrics.tasks_processed as f64 / actual_duration.as_secs_f64();
        assert!(actual_throughput > 30.0, "Should maintain reasonable throughput under sustained load");
        
        println!("Sustained load: processed {} tasks in {:.2}s (throughput: {:.1} tasks/sec)", 
            queue_metrics.tasks_processed, actual_duration.as_secs_f64(), actual_throughput);
        
        monitor.stop().await.expect("Should stop monitor");
        println!("Sustained load over time test passed");
    }
    
    // Test 3: Memory efficiency under load
    {
        println!("Testing memory efficiency under load");
        let mut monitor = create_fast_queue_monitor();
        
        // Import traits locally for this test block
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{Monitorable, QueueMonitoring};
        
        monitor.start().await.expect("Should start monitor");
        
        let queue_name = "memory_efficiency_queue";
        let cycles = 5;
        let tasks_per_cycle = 200;
        
        // Run multiple cycles to test memory stability
        for cycle in 0..cycles {
            println!("Running memory efficiency cycle {}/{}", cycle + 1, cycles);
            
            // Process a large number of tasks in this cycle
            for i in 0..tasks_per_cycle {
                let task_id = cycle * tasks_per_cycle + i;
                
                // Simulate varying queue lengths
                let queue_length = 50 + (task_id % 30);
                monitor.update_queue_metrics(queue_name, queue_length).await.expect("Should update queue metrics");
                
                // Process task with realistic timing
                let processing_time = 25.0 + (task_id % 15) as f64;
                let success = task_id % 15 != 0; // ~93% success rate
                
                monitor.record_task_processed(queue_name, processing_time, success).await.expect("Should record task");
                
                // Minimal delay
                if i % 20 == 0 {
                    sleep(Duration::from_millis(5)).await;
                }
            }
            
            // Check metrics after each cycle
            let cycle_metrics = monitor.get_metrics().await.expect("Should get cycle metrics");
            let queue_metrics = cycle_metrics.queue_metrics.get(queue_name).unwrap();
            
            let expected_tasks = (cycle + 1) * tasks_per_cycle;
            assert_eq!(queue_metrics.tasks_processed, expected_tasks, 
                "Should have correct task count after cycle {}", cycle + 1);
            
            // Brief pause between cycles
            sleep(Duration::from_millis(50)).await;
        }
        
        let final_metrics = monitor.get_metrics().await.expect("Should get final metrics");
        let queue_metrics = final_metrics.queue_metrics.get(queue_name).unwrap();
        
        let total_expected = cycles * tasks_per_cycle;
        assert_eq!(queue_metrics.tasks_processed, total_expected, 
            "Should have processed all tasks across all cycles");
        assert!(queue_metrics.avg_processing_time_ms > 0.0, "Should maintain valid metrics");
        
        println!("Memory efficiency test: processed {} tasks across {} cycles", 
            total_expected, cycles);
        
        monitor.stop().await.expect("Should stop monitor");
        println!("Memory efficiency under load test passed");
    }
    
    println!("Queue Monitor performance under load test passed");
}

/// Test Queue Monitor scalability with many queues
#[tokio::test]
async fn test_queue_monitor_scalability_many_queues() {
    println!("Starting Queue Monitor scalability with many queues test");
    
    // Test 1: Managing many queues simultaneously
    {
        println!("Testing management of many queues simultaneously");
        let mut monitor = create_fast_queue_monitor();
        
        // Import traits locally for this test block
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{Monitorable, QueueMonitoring};
        
        monitor.start().await.expect("Should start monitor");
        
        let num_queues = 50;
        let tasks_per_queue = 20;
        
        // Create and populate many queues
        for queue_id in 0..num_queues {
            let queue_name = format!("queue_{:03}", queue_id);
            
            // Set initial queue length
            monitor.update_queue_metrics(&queue_name, tasks_per_queue).await.expect("Should update queue metrics");
            
            // Process tasks for this queue
            for task_id in 0..tasks_per_queue {
                let processing_time = 30.0 + (queue_id as f64 * 2.0) + (task_id as f64 * 1.5);
                let success = (queue_id + task_id) % 11 != 0; // ~91% success rate
                
                monitor.record_task_processed(&queue_name, processing_time, success).await.expect("Should record task");
                
                // Update queue length
                let remaining = tasks_per_queue - task_id - 1;
                monitor.update_queue_metrics(&queue_name, remaining).await.expect("Should update queue metrics");
                
                // Small delay every few tasks
                if task_id % 5 == 0 {
                    sleep(Duration::from_millis(2)).await;
                }
            }
        }
        
        // Allow final processing
        sleep(Duration::from_millis(300)).await;
        
        let final_metrics = monitor.get_metrics().await.expect("Should get final metrics");
        
        // Verify all queues are tracked
        assert_eq!(final_metrics.queue_metrics.len(), num_queues, 
            "Should track all {} queues", num_queues);
        
        // Verify total task processing
        let total_expected_tasks = num_queues * tasks_per_queue;
        assert_eq!(final_metrics.total_tasks, total_expected_tasks, 
            "Should have processed all tasks across all queues");
        
        // Verify individual queue metrics
        for queue_id in 0..num_queues {
            let queue_name = format!("queue_{:03}", queue_id);
            let queue_metrics = final_metrics.queue_metrics.get(&queue_name)
                .expect(&format!("Should have metrics for {}", queue_name));
            
            assert_eq!(queue_metrics.tasks_processed, tasks_per_queue, 
                "Queue {} should have processed {} tasks", queue_name, tasks_per_queue);
            assert_eq!(queue_metrics.length, 0, 
                "Queue {} should be empty after processing", queue_name);
            assert!(queue_metrics.avg_processing_time_ms > 0.0, 
                "Queue {} should have valid average processing time", queue_name);
        }
        
        println!("Successfully managed {} queues with {} tasks each", num_queues, tasks_per_queue);
        
        monitor.stop().await.expect("Should stop monitor");
        println!("Many queues management test passed");
    }
    
    // Test 2: Dynamic queue creation and removal
    {
        println!("Testing dynamic queue creation and removal");
        let mut monitor = create_fast_queue_monitor();
        
        // Import traits locally for this test block
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{Monitorable, QueueMonitoring};
        
        monitor.start().await.expect("Should start monitor");
        
        let max_concurrent_queues = 30;
        let queue_lifecycle_cycles = 3;
        let tasks_per_queue_cycle = 15;
        
        for cycle in 0..queue_lifecycle_cycles {
            println!("Running dynamic queue cycle {}/{}", cycle + 1, queue_lifecycle_cycles);
            
            // Create queues for this cycle
            for queue_id in 0..max_concurrent_queues {
                let queue_name = format!("dynamic_queue_{}_{}", cycle, queue_id);
                
                // Initialize queue with some tasks
                monitor.update_queue_metrics(&queue_name, tasks_per_queue_cycle).await.expect("Should update queue metrics");
                
                // Process tasks
                for task_id in 0..tasks_per_queue_cycle {
                    let processing_time = 20.0 + (cycle as f64 * 10.0) + (task_id as f64 * 2.0);
                    let success = (cycle + queue_id + task_id) % 13 != 0; // ~92% success rate
                    
                    monitor.record_task_processed(&queue_name, processing_time, success).await.expect("Should record task");
                    
                    // Update queue length
                    let remaining = tasks_per_queue_cycle - task_id - 1;
                    monitor.update_queue_metrics(&queue_name, remaining).await.expect("Should update queue metrics");
                }
            }
            
            // Check metrics after this cycle
            let cycle_metrics = monitor.get_metrics().await.expect("Should get cycle metrics");
            
            // Verify queue count includes all cycles so far
            let expected_queue_count = (cycle + 1) * max_concurrent_queues;
            assert_eq!(cycle_metrics.queue_metrics.len(), expected_queue_count, 
                "Should have {} queues after cycle {}", expected_queue_count, cycle + 1);
            
            sleep(Duration::from_millis(50)).await;
        }
        
        let final_metrics = monitor.get_metrics().await.expect("Should get final metrics");
        
        // Verify final state
        let total_expected_queues = queue_lifecycle_cycles * max_concurrent_queues;
        let total_expected_tasks = total_expected_queues * tasks_per_queue_cycle;
        
        assert_eq!(final_metrics.queue_metrics.len(), total_expected_queues, 
            "Should have created {} total queues", total_expected_queues);
        assert_eq!(final_metrics.total_tasks, total_expected_tasks, 
            "Should have processed {} total tasks", total_expected_tasks);
        
        println!("Dynamic queue test: created {} queues across {} cycles", 
            total_expected_queues, queue_lifecycle_cycles);
        
        monitor.stop().await.expect("Should stop monitor");
        println!("Dynamic queue creation and removal test passed");
    }
    
    // Test 3: Queue priority and load balancing simulation
    {
        println!("Testing queue priority and load balancing simulation");
        let mut monitor = create_fast_queue_monitor();
        
        // Import traits locally for this test block
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{Monitorable, QueueMonitoring};
        
        monitor.start().await.expect("Should start monitor");
        
        // Define different priority levels with different characteristics
        let priority_configs = vec![
            ("critical", 5, 10, 25.0), // 5 queues, 10 tasks each, fast processing
            ("high", 10, 15, 50.0),    // 10 queues, 15 tasks each, medium processing
            ("normal", 15, 20, 75.0),  // 15 queues, 20 tasks each, normal processing
            ("low", 20, 25, 100.0),    // 20 queues, 25 tasks each, slow processing
        ];
        
        // Process all priority levels
        for (priority, queue_count, tasks_per_queue, base_processing_time) in priority_configs {
            println!("Processing {} priority queues", priority);
            
            for queue_id in 0..queue_count {
                let queue_name = format!("{}_priority_queue_{:02}", priority, queue_id);
                
                // Set initial queue length
                monitor.update_queue_metrics(&queue_name, tasks_per_queue).await.expect("Should update queue metrics");
                
                // Process tasks with priority-based characteristics
                for task_id in 0..tasks_per_queue {
                    let processing_time = base_processing_time + (task_id as f64 * 3.0);
                    let success_rate = match priority {
                        "critical" => 0.98, // Very high success rate
                        "high" => 0.95,     // High success rate
                        "normal" => 0.92,   // Good success rate
                        "low" => 0.88,      // Lower success rate
                        _ => 0.90,
                    };
                    let success = (task_id as f64 / tasks_per_queue as f64) < success_rate;
                    
                    monitor.record_task_processed(&queue_name, processing_time, success).await.expect("Should record task");
                    
                    // Update queue length
                    let remaining = tasks_per_queue - task_id - 1;
                    monitor.update_queue_metrics(&queue_name, remaining).await.expect("Should update queue metrics");
                    
                    // Priority-based processing delay
                    let delay_ms = match priority {
                        "critical" => 1,
                        "high" => 2,
                        "normal" => 3,
                        "low" => 5,
                        _ => 3,
                    };
                    sleep(Duration::from_millis(delay_ms)).await;
                }
            }
        }
        
        // Allow final processing
        sleep(Duration::from_millis(400)).await;
        
        let final_metrics = monitor.get_metrics().await.expect("Should get final metrics");
        
        // Verify priority-based performance characteristics
        let critical_queues: Vec<_> = final_metrics.queue_metrics.iter()
            .filter(|(name, _)| name.starts_with("critical_"))
            .collect();
        let low_queues: Vec<_> = final_metrics.queue_metrics.iter()
            .filter(|(name, _)| name.starts_with("low_"))
            .collect();
        
        assert!(!critical_queues.is_empty(), "Should have critical priority queues");
        assert!(!low_queues.is_empty(), "Should have low priority queues");
        
        // Calculate average processing times by priority
        let critical_avg_time: f64 = critical_queues.iter()
            .map(|(_, metrics)| metrics.avg_processing_time_ms)
            .sum::<f64>() / critical_queues.len() as f64;
        let low_avg_time: f64 = low_queues.iter()
            .map(|(_, metrics)| metrics.avg_processing_time_ms)
            .sum::<f64>() / low_queues.len() as f64;
        
        assert!(critical_avg_time < low_avg_time, 
            "Critical priority queues should have faster processing times than low priority");
        
        // Verify total queue count
        let expected_total_queues = 5 + 10 + 15 + 20; // Sum of all priority queue counts
        assert_eq!(final_metrics.queue_metrics.len(), expected_total_queues, 
            "Should have {} total queues across all priorities", expected_total_queues);
        
        println!("Priority and load balancing test: managed {} queues across 4 priority levels", 
            expected_total_queues);
        
        monitor.stop().await.expect("Should stop monitor");
        println!("Queue priority and load balancing simulation test passed");
    }
    
    println!("Queue Monitor scalability with many queues test passed");
}

// =================================================================================================
// Task Metrics Collection Tests
// =================================================================================================

/// Test task creation and lifecycle tracking
#[tokio::test]
async fn test_task_creation_and_lifecycle_tracking() {
    println!("Starting task creation and lifecycle tracking test");
    
    // Import traits locally
    {
        use prisma_ai::prisma::prisma_engine::monitor::prisma::TaskMonitor;
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::TaskMonitoring;
        use prisma_ai::prisma::prisma_engine::types::{TaskId, TaskCategory, TaskPriority};
        use prisma_ai::prisma::prisma_engine::monitor::prisma::types::{TaskMetrics, TaskStatus};
        
        let config = PrismaMonitorConfig {
            task_poll_interval_ms: 100,
            queue_poll_interval_ms: 100,
            max_task_history: 100,
            enable_detailed_task_tracking: true,
        };
        
        let mut task_monitor = TaskMonitor::new(config);
        task_monitor.start().await.expect("Task monitor should start");
        
        // Test 1: Task creation tracking
        println!("Testing task creation tracking");
        let task_id_1 = TaskId::new();
        let task_metrics_1 = TaskMetrics::new(task_id_1.clone(), TaskCategory::FileProcessing, TaskPriority::High);
        
        task_monitor.record_task_created(task_metrics_1.clone()).await
            .expect("Should record task creation");
        
        let metrics = task_monitor.get_metrics().await.unwrap();
        assert_eq!(metrics.total_tasks, 1, "Should have 1 total task");
        assert_eq!(metrics.active_tasks.len(), 1, "Should have 1 active task");
        assert!(metrics.active_tasks.contains_key(&task_id_1), "Should contain the created task");
        
        let tracked_task = &metrics.active_tasks[&task_id_1];
        assert_eq!(tracked_task.status, TaskStatus::Queued, "Task should be in Queued status");
        assert_eq!(tracked_task.category, TaskCategory::FileProcessing, "Task category should match");
        assert_eq!(tracked_task.priority, TaskPriority::High, "Task priority should match");
        
        // Test 2: Task lifecycle progression
        println!("Testing task lifecycle progression");
        
        // Start task execution
        task_monitor.record_task_started(&task_id_1, "processing_queue").await
            .expect("Should record task start");
        
        let metrics = task_monitor.get_metrics().await.unwrap();
        let tracked_task = &metrics.active_tasks[&task_id_1];
        assert_eq!(tracked_task.status, TaskStatus::Processing, "Task should be in Processing status");
        assert!(tracked_task.started_at.is_some(), "Task should have start time");
        assert_eq!(tracked_task.queue_name, Some("processing_queue".to_string()), "Queue name should be set");
        
        // Complete task successfully
        task_monitor.record_task_completed(&task_id_1, true, None).await
            .expect("Should record task completion");
        
        let metrics = task_monitor.get_metrics().await.unwrap();
        assert_eq!(metrics.active_tasks.len(), 0, "Should have no active tasks");
        assert_eq!(metrics.completed_tasks.len(), 1, "Should have 1 completed task");
        assert_eq!(metrics.successful_tasks, 1, "Should have 1 successful task");
        assert_eq!(metrics.failed_tasks, 0, "Should have 0 failed tasks");
        
        let completed_task = &metrics.completed_tasks[0];
        assert_eq!(completed_task.status, TaskStatus::Completed, "Task should be completed");
        assert!(completed_task.completed_at.is_some(), "Task should have completion time");
        assert!(completed_task.processing_time.is_some(), "Task should have processing time");
        
        // Test 3: Multiple task lifecycle tracking
        println!("Testing multiple task lifecycle tracking");
        
        let task_id_2 = TaskId::new();
        let task_id_3 = TaskId::new();
        
        let task_metrics_2 = TaskMetrics::new(task_id_2.clone(), TaskCategory::LLMInference, TaskPriority::Normal);
        let task_metrics_3 = TaskMetrics::new(task_id_3.clone(), TaskCategory::Internal, TaskPriority::Low);
        
        // Create multiple tasks
        task_monitor.record_task_created(task_metrics_2).await.expect("Should record task 2");
        task_monitor.record_task_created(task_metrics_3).await.expect("Should record task 3");
        
        let metrics = task_monitor.get_metrics().await.unwrap();
        assert_eq!(metrics.total_tasks, 3, "Should have 3 total tasks");
        assert_eq!(metrics.active_tasks.len(), 2, "Should have 2 active tasks");
        
        // Start and complete task 2 successfully
        task_monitor.record_task_started(&task_id_2, "inference_queue").await.expect("Should start task 2");
        task_monitor.record_task_completed(&task_id_2, true, None).await.expect("Should complete task 2");
        
        // Start and fail task 3
        task_monitor.record_task_started(&task_id_3, "maintenance_queue").await.expect("Should start task 3");
        task_monitor.record_task_completed(&task_id_3, false, Some("Test error".to_string())).await
            .expect("Should fail task 3");
        
        let final_metrics = task_monitor.get_metrics().await.unwrap();
        assert_eq!(final_metrics.total_tasks, 3, "Should have 3 total tasks");
        assert_eq!(final_metrics.successful_tasks, 2, "Should have 2 successful tasks");
        assert_eq!(final_metrics.failed_tasks, 1, "Should have 1 failed task");
        assert_eq!(final_metrics.completed_tasks.len(), 3, "Should have 3 completed tasks");
        assert_eq!(final_metrics.active_tasks.len(), 0, "Should have no active tasks");
        
        // Verify failed task has error message
        let failed_task = final_metrics.completed_tasks.iter()
            .find(|t| t.task_id == task_id_3)
            .expect("Should find failed task");
        assert_eq!(failed_task.status, TaskStatus::Failed, "Task should be failed");
        assert_eq!(failed_task.error_message, Some("Test error".to_string()), "Should have error message");
        
        task_monitor.stop().await.expect("Task monitor should stop");
        println!("Task creation and lifecycle tracking test passed");
    }
}

/// Test task execution time measurement
#[tokio::test]
async fn test_task_execution_time_measurement() {
    println!("Starting task execution time measurement test");
    
    // Import traits locally
    {
        use prisma_ai::prisma::prisma_engine::monitor::prisma::TaskMonitor;
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::TaskMonitoring;
        use prisma_ai::prisma::prisma_engine::types::{TaskId, TaskCategory, TaskPriority};
        use prisma_ai::prisma::prisma_engine::monitor::prisma::types::TaskMetrics;
        use tokio::time::{sleep, Duration};
        
        let config = PrismaMonitorConfig {
            task_poll_interval_ms: 50,
            queue_poll_interval_ms: 50,
            max_task_history: 100,
            enable_detailed_task_tracking: true,
        };
        
        let mut task_monitor = TaskMonitor::new(config);
        task_monitor.start().await.expect("Task monitor should start");
        
        // Test 1: Short execution time measurement
        println!("Testing short execution time measurement");
        let task_id_short = TaskId::new();
        let task_metrics_short = TaskMetrics::new(task_id_short.clone(), TaskCategory::FileProcessing, TaskPriority::High);
        
        task_monitor.record_task_created(task_metrics_short).await.expect("Should create short task");
        task_monitor.record_task_started(&task_id_short, "fast_queue").await.expect("Should start short task");
        
        // Simulate short processing time
        sleep(Duration::from_millis(100)).await;
        
        task_monitor.record_task_completed(&task_id_short, true, None).await.expect("Should complete short task");
        
        let metrics = task_monitor.get_metrics().await.unwrap();
        let short_task = &metrics.completed_tasks[0];
        let processing_time = short_task.processing_time.expect("Should have processing time");
        
        assert!(processing_time >= Duration::from_millis(90), "Processing time should be at least 90ms");
        assert!(processing_time <= Duration::from_millis(200), "Processing time should be at most 200ms");
        
        // Test 2: Long execution time measurement
        println!("Testing long execution time measurement");
        let task_id_long = TaskId::new();
        let task_metrics_long = TaskMetrics::new(task_id_long.clone(), TaskCategory::LLMInference, TaskPriority::Normal);
        
        task_monitor.record_task_created(task_metrics_long).await.expect("Should create long task");
        task_monitor.record_task_started(&task_id_long, "slow_queue").await.expect("Should start long task");
        
        // Simulate longer processing time
        sleep(Duration::from_millis(300)).await;
        
        task_monitor.record_task_completed(&task_id_long, true, None).await.expect("Should complete long task");
        
        let metrics = task_monitor.get_metrics().await.unwrap();
        let long_task = metrics.completed_tasks.iter()
            .find(|t| t.task_id == task_id_long)
            .expect("Should find long task");
        let long_processing_time = long_task.processing_time.expect("Should have processing time");
        
        assert!(long_processing_time >= Duration::from_millis(290), "Long processing time should be at least 290ms");
        assert!(long_processing_time <= Duration::from_millis(400), "Long processing time should be at most 400ms");
        
        // Verify long task took longer than short task
        assert!(long_processing_time > processing_time, "Long task should take longer than short task");
        
        // Test 3: Average execution time calculation
        println!("Testing average execution time calculation");
        
        // Create several more tasks with varying execution times
        for i in 0..5 {
            let task_id = TaskId::new();
            let task_metrics = TaskMetrics::new(task_id.clone(), TaskCategory::FileProcessing, TaskPriority::Low);
            
            task_monitor.record_task_created(task_metrics).await.expect("Should create task");
            task_monitor.record_task_started(&task_id, "test_queue").await.expect("Should start task");
            
            // Vary execution times: 50ms, 100ms, 150ms, 200ms, 250ms
            let execution_time = 50 + (i * 50);
            sleep(Duration::from_millis(execution_time)).await;
            
            task_monitor.record_task_completed(&task_id, true, None).await.expect("Should complete task");
        }
        
        let final_metrics = task_monitor.get_metrics().await.unwrap();
        assert_eq!(final_metrics.completed_tasks.len(), 7, "Should have 7 completed tasks");
        
        // Verify average processing time is reasonable
        assert!(final_metrics.avg_processing_time_ms > 0.0, "Average processing time should be positive");
        assert!(final_metrics.avg_processing_time_ms < 1000.0, "Average processing time should be reasonable");
        
        // Verify all tasks have processing times
        for task in &final_metrics.completed_tasks {
            assert!(task.processing_time.is_some(), "All completed tasks should have processing time");
            assert!(task.processing_time.unwrap() > Duration::from_millis(0), "Processing time should be positive");
        }
        
        task_monitor.stop().await.expect("Task monitor should stop");
        println!("Task execution time measurement test passed");
    }
}

/// Test task queue time calculation
#[tokio::test]
async fn test_task_queue_time_calculation() {
    println!("Starting task queue time calculation test");
    
    // Import traits locally
    {
        use prisma_ai::prisma::prisma_engine::monitor::prisma::TaskMonitor;
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::TaskMonitoring;
        use prisma_ai::prisma::prisma_engine::types::{TaskId, TaskCategory, TaskPriority};
        use prisma_ai::prisma::prisma_engine::monitor::prisma::types::TaskMetrics;
        use tokio::time::{sleep, Duration};
        
        let config = PrismaMonitorConfig {
            task_poll_interval_ms: 50,
            queue_poll_interval_ms: 50,
            max_task_history: 100,
            enable_detailed_task_tracking: true,
        };
        
        let mut task_monitor = TaskMonitor::new(config);
        task_monitor.start().await.expect("Task monitor should start");
        
        // Test 1: Short queue time
        println!("Testing short queue time calculation");
        let task_id_1 = TaskId::new();
        let task_metrics_1 = TaskMetrics::new(task_id_1.clone(), TaskCategory::FileProcessing, TaskPriority::High);
        
        task_monitor.record_task_created(task_metrics_1).await.expect("Should create task");
        
        // Short wait in queue
        sleep(Duration::from_millis(50)).await;
        
        task_monitor.record_task_started(&task_id_1, "priority_queue").await.expect("Should start task");
        task_monitor.record_task_completed(&task_id_1, true, None).await.expect("Should complete task");
        
        let metrics = task_monitor.get_metrics().await.unwrap();
        let task_1 = &metrics.completed_tasks[0];
        let queue_time_1 = task_1.queue_time.expect("Should have queue time");
        
        assert!(queue_time_1 >= Duration::from_millis(40), "Queue time should be at least 40ms");
        assert!(queue_time_1 <= Duration::from_millis(100), "Queue time should be at most 100ms");
        
        // Test 2: Long queue time
        println!("Testing long queue time calculation");
        let task_id_2 = TaskId::new();
        let task_metrics_2 = TaskMetrics::new(task_id_2.clone(), TaskCategory::LLMInference, TaskPriority::Low);
        
        task_monitor.record_task_created(task_metrics_2).await.expect("Should create task");
        
        // Longer wait in queue
        sleep(Duration::from_millis(200)).await;
        
        task_monitor.record_task_started(&task_id_2, "slow_queue").await.expect("Should start task");
        task_monitor.record_task_completed(&task_id_2, true, None).await.expect("Should complete task");
        
        let metrics = task_monitor.get_metrics().await.unwrap();
        let task_2 = metrics.completed_tasks.iter()
            .find(|t| t.task_id == task_id_2)
            .expect("Should find task 2");
        let queue_time_2 = task_2.queue_time.expect("Should have queue time");
        
        assert!(queue_time_2 >= Duration::from_millis(190), "Long queue time should be at least 190ms");
        assert!(queue_time_2 <= Duration::from_millis(250), "Long queue time should be at most 250ms");
        
        // Verify long queue time is longer than short queue time
        assert!(queue_time_2 > queue_time_1, "Long queue time should be longer than short queue time");
        
        // Test 3: Multiple tasks with varying queue times
        println!("Testing multiple tasks with varying queue times");
        
        let mut queue_times = Vec::new();
        
        for i in 0..4 {
            let task_id = TaskId::new();
            let task_metrics = TaskMetrics::new(task_id.clone(), TaskCategory::Internal, TaskPriority::Normal);
            
            task_monitor.record_task_created(task_metrics).await.expect("Should create task");
            
            // Varying queue times: 75ms, 125ms, 175ms, 225ms
            let queue_wait_time = 75 + (i * 50);
            sleep(Duration::from_millis(queue_wait_time)).await;
            
            task_monitor.record_task_started(&task_id, "variable_queue").await.expect("Should start task");
            task_monitor.record_task_completed(&task_id, true, None).await.expect("Should complete task");
            
            let current_metrics = task_monitor.get_metrics().await.unwrap();
            let current_task = current_metrics.completed_tasks.iter()
                .find(|t| t.task_id == task_id)
                .expect("Should find current task");
            
            let current_queue_time = current_task.queue_time.expect("Should have queue time");
            queue_times.push(current_queue_time);
        }
        
        // Verify queue times are in ascending order (approximately)
        for i in 1..queue_times.len() {
            assert!(queue_times[i] > queue_times[i-1], 
                "Queue time {} should be longer than queue time {}", i, i-1);
        }
        
        // Test 4: Average queue time calculation
        println!("Testing average queue time calculation");
        
        let final_metrics = task_monitor.get_metrics().await.unwrap();
        assert_eq!(final_metrics.completed_tasks.len(), 6, "Should have 6 completed tasks");
        
        // Verify average queue time is reasonable
        assert!(final_metrics.avg_queue_time_ms > 0.0, "Average queue time should be positive");
        assert!(final_metrics.avg_queue_time_ms < 500.0, "Average queue time should be reasonable");
        
        // Verify all tasks have queue times
        for task in &final_metrics.completed_tasks {
            assert!(task.queue_time.is_some(), "All completed tasks should have queue time");
            assert!(task.queue_time.unwrap() > Duration::from_millis(0), "Queue time should be positive");
        }
        
        task_monitor.stop().await.expect("Task monitor should stop");
        println!("Task queue time calculation test passed");
    }
}

/// Test task success/failure rate monitoring
#[tokio::test]
async fn test_task_success_failure_rate_monitoring() {
    println!("Starting task success/failure rate monitoring test");
    
    // Import traits locally
    {
        use prisma_ai::prisma::prisma_engine::monitor::prisma::TaskMonitor;
        use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::TaskMonitoring;
        use prisma_ai::prisma::prisma_engine::types::{TaskId, TaskCategory, TaskPriority};
        use prisma_ai::prisma::prisma_engine::monitor::prisma::types::{TaskMetrics, TaskStatus};
        
        let config = PrismaMonitorConfig {
            task_poll_interval_ms: 50,
            queue_poll_interval_ms: 50,
            max_task_history: 100,
            enable_detailed_task_tracking: true,
        };
        
        let mut task_monitor = TaskMonitor::new(config);
        task_monitor.start().await.expect("Task monitor should start");
        
        // Test 1: All successful tasks
        println!("Testing all successful tasks scenario");
        
        for i in 0..5 {
            let task_id = TaskId::new();
            let task_metrics = TaskMetrics::new(task_id.clone(), TaskCategory::FileProcessing, TaskPriority::High);
            
            task_monitor.record_task_created(task_metrics).await.expect("Should create task");
            task_monitor.record_task_started(&task_id, "success_queue").await.expect("Should start task");
            task_monitor.record_task_completed(&task_id, true, None).await.expect("Should complete task successfully");
        }
        
        let metrics = task_monitor.get_metrics().await.unwrap();
        assert_eq!(metrics.total_tasks, 5, "Should have 5 total tasks");
        assert_eq!(metrics.successful_tasks, 5, "Should have 5 successful tasks");
        assert_eq!(metrics.failed_tasks, 0, "Should have 0 failed tasks");
        
        // Verify success rate is 100%
        let success_rate = (metrics.successful_tasks as f64 / metrics.total_tasks as f64) * 100.0;
        assert_eq!(success_rate, 100.0, "Success rate should be 100%");
        
        // Test 2: All failed tasks
        println!("Testing all failed tasks scenario");
        
        for i in 0..3 {
            let task_id = TaskId::new();
            let task_metrics = TaskMetrics::new(task_id.clone(), TaskCategory::LLMInference, TaskPriority::Normal);
            
            task_monitor.record_task_created(task_metrics).await.expect("Should create task");
            task_monitor.record_task_started(&task_id, "failure_queue").await.expect("Should start task");
            
            let error_message = format!("Test error {}", i + 1);
            task_monitor.record_task_completed(&task_id, false, Some(error_message.clone())).await
                .expect("Should complete task with failure");
        }
        
        let metrics = task_monitor.get_metrics().await.unwrap();
        assert_eq!(metrics.total_tasks, 8, "Should have 8 total tasks");
        assert_eq!(metrics.successful_tasks, 5, "Should still have 5 successful tasks");
        assert_eq!(metrics.failed_tasks, 3, "Should have 3 failed tasks");
        
        // Verify failure rate
        let failure_rate = (metrics.failed_tasks as f64 / metrics.total_tasks as f64) * 100.0;
        assert_eq!(failure_rate, 37.5, "Failure rate should be 37.5%");
        
        // Verify failed tasks have error messages
        let failed_tasks: Vec<_> = metrics.completed_tasks.iter()
            .filter(|t| t.status == TaskStatus::Failed)
            .collect();
        assert_eq!(failed_tasks.len(), 3, "Should have 3 failed tasks in completed list");
        
        for (i, failed_task) in failed_tasks.iter().enumerate() {
            assert!(failed_task.error_message.is_some(), "Failed task should have error message");
            let error_msg = failed_task.error_message.as_ref().unwrap();
            assert!(error_msg.contains("Test error"), "Error message should contain 'Test error'");
        }
        
        // Test 3: Mixed success/failure scenario
        println!("Testing mixed success/failure scenario");
        
        // Add 10 more tasks with 70% success rate
        for i in 0..10 {
            let task_id = TaskId::new();
            let task_metrics = TaskMetrics::new(task_id.clone(), TaskCategory::Internal, TaskPriority::Low);
            
            task_monitor.record_task_created(task_metrics).await.expect("Should create task");
            task_monitor.record_task_started(&task_id, "mixed_queue").await.expect("Should start task");
            
            // 70% success rate: tasks 0,1,2,4,5,7,8 succeed, tasks 3,6,9 fail
            let should_succeed = ![3, 6, 9].contains(&i);
            
            if should_succeed {
                task_monitor.record_task_completed(&task_id, true, None).await
                    .expect("Should complete task successfully");
            } else {
                let error_message = format!("Mixed test error {}", i);
                task_monitor.record_task_completed(&task_id, false, Some(error_message)).await
                    .expect("Should complete task with failure");
            }
        }
        
        let final_metrics = task_monitor.get_metrics().await.unwrap();
        assert_eq!(final_metrics.total_tasks, 18, "Should have 18 total tasks");
        assert_eq!(final_metrics.successful_tasks, 12, "Should have 12 successful tasks"); // 5 + 7
        assert_eq!(final_metrics.failed_tasks, 6, "Should have 6 failed tasks"); // 3 + 3
        
        // Verify final success/failure rates
        let final_success_rate = (final_metrics.successful_tasks as f64 / final_metrics.total_tasks as f64) * 100.0;
        let final_failure_rate = (final_metrics.failed_tasks as f64 / final_metrics.total_tasks as f64) * 100.0;
        
        assert!((final_success_rate - 66.67).abs() < 0.1, "Success rate should be approximately 66.67%");
        assert!((final_failure_rate - 33.33).abs() < 0.1, "Failure rate should be approximately 33.33%");
        assert!((final_success_rate + final_failure_rate - 100.0).abs() < 0.1, "Success and failure rates should sum to 100%");
        
        // Test 4: Verify task status distribution
        println!("Testing task status distribution");
        
        let completed_tasks = &final_metrics.completed_tasks;
        assert_eq!(completed_tasks.len(), 18, "Should have 18 completed tasks");
        
        let successful_count = completed_tasks.iter().filter(|t| t.status == TaskStatus::Completed).count();
        let failed_count = completed_tasks.iter().filter(|t| t.status == TaskStatus::Failed).count();
        
        assert_eq!(successful_count, 12, "Should have 12 completed tasks with Completed status");
        assert_eq!(failed_count, 6, "Should have 6 completed tasks with Failed status");
        
        // Verify no tasks are still active
        assert_eq!(final_metrics.active_tasks.len(), 0, "Should have no active tasks");
        
        task_monitor.stop().await.expect("Task monitor should stop");
        println!("Task success/failure rate monitoring test passed");
    }
}
