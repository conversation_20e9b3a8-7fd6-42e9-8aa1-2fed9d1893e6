use std::time::Duration;
use tokio::time::sleep;

// Import types without conflicting traits
use prisma_ai::prisma::prisma_engine::monitor::system::{
    MemoryMonitor,
    DiskMonitor,
    NetworkMonitor,
    types::SystemMonitorConfig,
};

// Import Prisma monitor types and traits
use prisma_ai::prisma::prisma_engine::types::{TaskId, TaskCategory, TaskPriority};
use prisma_ai::prisma::prisma_engine::monitor::prisma::types::{TaskMetrics, TaskStatus};

// Import Queue monitor types
use prisma_ai::prisma::prisma_engine::monitor::prisma::{
    queue_monitor::QueueMonitor,
    task_monitor::TaskMonitor,
    types::PrismaMonitorConfig,
    traits::{Monitorable, QueueMonitoring, TaskMonitoring},
};

// =================================================================================================
// Helper Functions
// =================================================================================================

/// Creates a TaskMonitor with fast polling intervals for testing
fn create_fast_task_monitor() -> TaskMonitor {
    let config = PrismaMonitorConfig {
        queue_poll_interval_ms: 10,
        task_poll_interval_ms: 10,
        max_task_history: 100,
        enable_detailed_task_tracking: true,
    };
    TaskMonitor::new(config)
}

// =================================================================================================
// Task Status Management Tests
// =================================================================================================

#[tokio::test]
async fn test_task_status_transitions() {
        println!("Starting task status transitions test");
        
        // Create task monitor with real objects
        let mut task_monitor = create_fast_task_monitor();
        task_monitor.start().await.expect("Task monitor should start");
        
        let task_id = TaskId::new();
        
        // Test 1: Initial status - Queued
        println!("Testing initial task creation with Queued status");
        let task_metrics = TaskMetrics::new(task_id.clone(), TaskCategory::LLMInference, TaskPriority::High);
        assert_eq!(task_metrics.status, TaskStatus::Queued, "New task should have Queued status");
        
        task_monitor.record_task_created(task_metrics).await.expect("Should create task");
        
        let retrieved_metrics = task_monitor.get_task_metrics(&task_id).await.unwrap().unwrap();
        assert_eq!(retrieved_metrics.status, TaskStatus::Queued, "Task should remain in Queued status");
        
        // Test 2: Transition to Processing
        println!("Testing transition from Queued to Processing");
        task_monitor.update_task_status(&task_id, TaskStatus::Processing).await.expect("Should update status");
        
        let retrieved_metrics = task_monitor.get_task_metrics(&task_id).await.unwrap().unwrap();
        assert_eq!(retrieved_metrics.status, TaskStatus::Processing, "Task should be in Processing status");
        
        // Test 3: Transition to Completed
        println!("Testing transition from Processing to Completed");
        task_monitor.record_task_started(&task_id, "test_queue").await.expect("Should start task");
        task_monitor.record_task_completed(&task_id, true, None).await.expect("Should complete task");
        
        let retrieved_metrics = task_monitor.get_task_metrics(&task_id).await.unwrap().unwrap();
        assert_eq!(retrieved_metrics.status, TaskStatus::Completed, "Task should be in Completed status");
        
        // Test 4: Test Failed status transition
        println!("Testing transition to Failed status");
        let failed_task_id = TaskId::new();
        let failed_task_metrics = TaskMetrics::new(failed_task_id.clone(), TaskCategory::DatabaseQuery, TaskPriority::Normal);
        
        task_monitor.record_task_created(failed_task_metrics).await.expect("Should create failed task");
        task_monitor.record_task_started(&failed_task_id, "failure_queue").await.expect("Should start failed task");
        task_monitor.record_task_completed(&failed_task_id, false, Some("Test failure".to_string())).await
            .expect("Should complete task with failure");
        
        let failed_metrics = task_monitor.get_task_metrics(&failed_task_id).await.unwrap().unwrap();
        assert_eq!(failed_metrics.status, TaskStatus::Failed, "Task should be in Failed status");
        assert!(failed_metrics.error_message.is_some(), "Failed task should have error message");
        assert_eq!(failed_metrics.error_message.unwrap(), "Test failure", "Error message should match");
        
        // Test 5: Test Cancelled status
        println!("Testing Cancelled status transition");
        let cancelled_task_id = TaskId::new();
        let cancelled_task_metrics = TaskMetrics::new(cancelled_task_id.clone(), TaskCategory::FileProcessing, TaskPriority::Low);
        
        task_monitor.record_task_created(cancelled_task_metrics).await.expect("Should create cancelled task");
        task_monitor.update_task_status(&cancelled_task_id, TaskStatus::Cancelled).await.expect("Should cancel task");
        
        let cancelled_metrics = task_monitor.get_task_metrics(&cancelled_task_id).await.unwrap().unwrap();
        assert_eq!(cancelled_metrics.status, TaskStatus::Cancelled, "Task should be in Cancelled status");
        
        task_monitor.stop().await.expect("Task monitor should stop");
        println!("Task status transitions test passed");
    }

#[tokio::test]
async fn test_task_status_updates_and_synchronization() {
        println!("Starting task status updates and synchronization test");
        
        // Create task monitor with real objects
        let mut task_monitor = create_fast_task_monitor();
        task_monitor.start().await.expect("Task monitor should start");
        
        // Test 1: Concurrent status updates
        println!("Testing concurrent status updates");
        let mut task_ids = Vec::new();
        
        // Create multiple tasks concurrently
        for i in 0..10 {
            let task_id = TaskId::new();
            let category = match i % 4 {
                0 => TaskCategory::LLMInference,
                1 => TaskCategory::DatabaseQuery,
                2 => TaskCategory::FileProcessing,
                _ => TaskCategory::NetworkRequest,
            };
            let priority = match i % 3 {
                0 => TaskPriority::High,
                1 => TaskPriority::Normal,
                _ => TaskPriority::Low,
            };
            
            let task_metrics = TaskMetrics::new(task_id.clone(), category, priority);
            task_monitor.record_task_created(task_metrics).await.expect("Should create task");
            task_ids.push(task_id);
        }
        
        // Update all tasks to Processing status concurrently
        for task_id in &task_ids {
            let task_id_clone = task_id.clone();
            
            // Update status to Processing
            task_monitor.update_task_status(&task_id_clone, TaskStatus::Processing).await
                .expect("Should update to Processing");
        }
        
        // Verify all tasks are in Processing status
        for task_id in &task_ids {
            let metrics = task_monitor.get_task_metrics(task_id).await.unwrap().unwrap();
            assert_eq!(metrics.status, TaskStatus::Processing, "Task should be in Processing status");
        }
        
        // Test 2: Status update ordering and consistency
        println!("Testing status update ordering and consistency");
        let test_task_id = task_ids[0].clone();
        
        // Record task started
        task_monitor.record_task_started(&test_task_id, "sync_test_queue").await.expect("Should start task");
        
        let metrics_after_start = task_monitor.get_task_metrics(&test_task_id).await.unwrap().unwrap();
        assert_eq!(metrics_after_start.status, TaskStatus::Processing, "Task should remain in Processing after start");
        assert!(metrics_after_start.started_at.is_some(), "Task should have started_at timestamp");
        assert!(metrics_after_start.queue_time.is_some(), "Task should have queue_time calculated");
        
        // Complete the task
        task_monitor.record_task_completed(&test_task_id, true, None).await.expect("Should complete task");
        
        let final_metrics = task_monitor.get_task_metrics(&test_task_id).await.unwrap().unwrap();
        assert_eq!(final_metrics.status, TaskStatus::Completed, "Task should be Completed");
        assert!(final_metrics.completed_at.is_some(), "Task should have completed_at timestamp");
        assert!(final_metrics.processing_time.is_some(), "Task should have processing_time calculated");
        
        // Test 3: Verify task is moved from active to completed
        println!("Testing task movement from active to completed");
        let monitor_metrics = task_monitor.get_metrics().await.unwrap();
        
        // The completed task should not be in active tasks
        assert!(!monitor_metrics.active_tasks.contains_key(&test_task_id), "Completed task should not be in active tasks");
        
        // The completed task should be in completed tasks
        let completed_task_found = monitor_metrics.completed_tasks.iter()
            .any(|t| t.task_id == test_task_id && t.status == TaskStatus::Completed);
        assert!(completed_task_found, "Completed task should be in completed tasks list");
        
        // Test 4: Verify metrics consistency
        println!("Testing metrics consistency after status updates");
        assert_eq!(monitor_metrics.active_tasks.len(), 9, "Should have 9 active tasks remaining");
        assert_eq!(monitor_metrics.completed_tasks.len(), 1, "Should have 1 completed task");
        assert_eq!(monitor_metrics.total_tasks, 10, "Should have 10 total tasks");
        assert_eq!(monitor_metrics.successful_tasks, 1, "Should have 1 successful task");
        assert_eq!(monitor_metrics.failed_tasks, 0, "Should have 0 failed tasks");
        
        task_monitor.stop().await.expect("Task monitor should stop");
        println!("Task status updates and synchronization test passed");
    }

#[tokio::test]
async fn test_task_status_error_handling() {
        println!("Starting task status error handling test");
        
        // Create task monitor with real objects
        let mut task_monitor = create_fast_task_monitor();
        task_monitor.start().await.expect("Task monitor should start");
        
        // Test 1: Update status of non-existent task
        println!("Testing status update of non-existent task");
        let non_existent_task_id = TaskId::new();
        
        let result = task_monitor.update_task_status(&non_existent_task_id, TaskStatus::Processing).await;
        assert!(result.is_err(), "Updating non-existent task should fail");
        
        // Test 2: Duplicate task creation
        println!("Testing duplicate task creation");
        let task_id = TaskId::new();
        let task_metrics = TaskMetrics::new(task_id.clone(), TaskCategory::LLMInference, TaskPriority::Normal);
        
        task_monitor.record_task_created(task_metrics.clone()).await.expect("First task creation should succeed");
        
        let duplicate_result = task_monitor.record_task_created(task_metrics).await;
        assert!(duplicate_result.is_err(), "Duplicate task creation should fail");
        
        // Test 3: Start non-existent task
        println!("Testing start of non-existent task");
        let non_existent_start_id = TaskId::new();
        
        let start_result = task_monitor.record_task_started(&non_existent_start_id, "test_queue").await;
        assert!(start_result.is_err(), "Starting non-existent task should fail");
        
        // Test 4: Complete non-existent task
        println!("Testing completion of non-existent task");
        let non_existent_complete_id = TaskId::new();
        
        let complete_result = task_monitor.record_task_completed(&non_existent_complete_id, true, None).await;
        assert!(complete_result.is_err(), "Completing non-existent task should fail");
        
        // Test 5: Get metrics for non-existent task
        println!("Testing get metrics for non-existent task");
        let non_existent_metrics_id = TaskId::new();
        
        let metrics_result = task_monitor.get_task_metrics(&non_existent_metrics_id).await.unwrap();
        assert!(metrics_result.is_none(), "Getting metrics for non-existent task should return None");
        
        // Test 6: Error handling during task completion with error message
        println!("Testing task completion with error handling");
        let error_task_id = TaskId::new();
        let error_task_metrics = TaskMetrics::new(error_task_id.clone(), TaskCategory::DatabaseQuery, TaskPriority::High);
        
        task_monitor.record_task_created(error_task_metrics).await.expect("Should create error task");
        task_monitor.record_task_started(&error_task_id, "error_queue").await.expect("Should start error task");
        
        // Complete with detailed error message
        let error_message = "Database connection timeout after 30 seconds";
        task_monitor.record_task_completed(&error_task_id, false, Some(error_message.to_string())).await
            .expect("Should complete task with error");
        
        let error_metrics = task_monitor.get_task_metrics(&error_task_id).await.unwrap().unwrap();
        assert_eq!(error_metrics.status, TaskStatus::Failed, "Task should be Failed");
        assert!(error_metrics.error_message.is_some(), "Failed task should have error message");
        assert_eq!(error_metrics.error_message.unwrap(), error_message, "Error message should match");
        
        // Test 7: Verify monitor state consistency after errors
        println!("Testing monitor state consistency after error scenarios");
        let final_metrics = task_monitor.get_metrics().await.unwrap();
        
        assert_eq!(final_metrics.active_tasks.len(), 1, "Should have 1 active task");
        assert_eq!(final_metrics.completed_tasks.len(), 1, "Should have 1 completed task");
        assert_eq!(final_metrics.total_tasks, 2, "Should have 2 total tasks");
        assert_eq!(final_metrics.successful_tasks, 0, "Should have 0 successful tasks");
        assert_eq!(final_metrics.failed_tasks, 1, "Should have 1 failed task");
        
        // Verify the failed task is properly recorded
        let failed_task_found = final_metrics.completed_tasks.iter()
            .any(|t| t.task_id == error_task_id && t.status == TaskStatus::Failed);
        assert!(failed_task_found, "Failed task should be in completed tasks list");
        
        task_monitor.stop().await.expect("Task monitor should stop");
        println!("Task status error handling test passed");
    }

#[tokio::test]
async fn test_task_status_edge_cases_and_validation() {
        println!("Starting task status edge cases and validation test");
        
        // Create task monitor with real objects
        let mut task_monitor = create_fast_task_monitor();
        task_monitor.start().await.expect("Task monitor should start");
        
        // Test 1: Rapid status transitions
        println!("Testing rapid status transitions");
        let rapid_task_id = TaskId::new();
        let rapid_task_metrics = TaskMetrics::new(rapid_task_id.clone(), TaskCategory::NetworkRequest, TaskPriority::Realtime);
        
        task_monitor.record_task_created(rapid_task_metrics).await.expect("Should create rapid task");
        
        // Rapid transitions: Queued -> Processing -> Completed
        task_monitor.update_task_status(&rapid_task_id, TaskStatus::Processing).await.expect("Should update to Processing");
        task_monitor.record_task_started(&rapid_task_id, "rapid_queue").await.expect("Should start rapid task");
        task_monitor.record_task_completed(&rapid_task_id, true, None).await.expect("Should complete rapid task");
        
        let rapid_metrics = task_monitor.get_task_metrics(&rapid_task_id).await.unwrap().unwrap();
        assert_eq!(rapid_metrics.status, TaskStatus::Completed, "Rapid task should be Completed");
        assert!(rapid_metrics.queue_time.is_some(), "Rapid task should have queue time");
        assert!(rapid_metrics.processing_time.is_some(), "Rapid task should have processing time");
        
        // Test 2: Task with different categories and priorities
        println!("Testing tasks with different categories and priorities");
        let categories = vec![
            TaskCategory::LLMInference,
            TaskCategory::EmbeddingGeneration,
            TaskCategory::DatabaseQuery,
            TaskCategory::FileProcessing,
            TaskCategory::NetworkRequest,
            TaskCategory::UICallback,
            TaskCategory::Internal,
            TaskCategory::Custom("CustomTask".to_string()),
        ];
        
        let priorities = vec![
            TaskPriority::Low,
            TaskPriority::Normal,
            TaskPriority::High,
            TaskPriority::Realtime,
        ];
        
        let mut category_priority_tasks = Vec::new();
        
        for (i, category) in categories.iter().enumerate() {
            let priority = priorities[i % priorities.len()];
            let task_id = TaskId::new();
            let task_metrics = TaskMetrics::new(task_id.clone(), category.clone(), priority);
            
            task_monitor.record_task_created(task_metrics).await.expect("Should create category/priority task");
            category_priority_tasks.push((task_id, category.clone(), priority));
        }
        
        // Verify all tasks are created with correct attributes
        for (task_id, expected_category, expected_priority) in &category_priority_tasks {
            let metrics = task_monitor.get_task_metrics(task_id).await.unwrap().unwrap();
            assert_eq!(metrics.category, *expected_category, "Task category should match");
            assert_eq!(metrics.priority, *expected_priority, "Task priority should match");
            assert_eq!(metrics.status, TaskStatus::Queued, "Task should start in Queued status");
        }
        
        // Test 3: Task lifecycle timing validation
        println!("Testing task lifecycle timing validation");
        let timing_task_id = TaskId::new();
        let timing_task_metrics = TaskMetrics::new(timing_task_id.clone(), TaskCategory::LLMInference, TaskPriority::High);
        
        task_monitor.record_task_created(timing_task_metrics).await.expect("Should create timing task");
        
        // Add small delay to ensure measurable time differences
        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        
        task_monitor.record_task_started(&timing_task_id, "timing_queue").await.expect("Should start timing task");
        
        // Add another small delay
        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        
        task_monitor.record_task_completed(&timing_task_id, true, None).await.expect("Should complete timing task");
        
        let timing_metrics = task_monitor.get_task_metrics(&timing_task_id).await.unwrap().unwrap();
        
        // Verify timing relationships
        assert!(timing_metrics.started_at.is_some(), "Task should have start time");
        assert!(timing_metrics.completed_at.is_some(), "Task should have completion time");
        assert!(timing_metrics.queue_time.is_some(), "Task should have queue time");
        assert!(timing_metrics.processing_time.is_some(), "Task should have processing time");
        
        // Verify queue time is reasonable (should be > 0 due to our delays)
        let queue_time = timing_metrics.queue_time.unwrap();
        assert!(queue_time.as_millis() >= 5, "Queue time should be at least 5ms due to delays");
        
        // Verify processing time is reasonable
        let processing_time = timing_metrics.processing_time.unwrap();
        assert!(processing_time.as_millis() >= 5, "Processing time should be at least 5ms due to delays");
        
        // Test 4: Monitor state after complex operations
        println!("Testing monitor state after complex operations");
        let final_metrics = task_monitor.get_metrics().await.unwrap();
        
        // We created: 1 rapid task + 8 category/priority tasks + 1 timing task = 10 total
        // Completed: 1 rapid task + 1 timing task = 2 completed
        // Active: 8 category/priority tasks
        assert_eq!(final_metrics.total_tasks, 10, "Should have 10 total tasks");
        assert_eq!(final_metrics.active_tasks.len(), 8, "Should have 8 active tasks");
        assert_eq!(final_metrics.completed_tasks.len(), 2, "Should have 2 completed tasks");
        assert_eq!(final_metrics.successful_tasks, 2, "Should have 2 successful tasks");
        assert_eq!(final_metrics.failed_tasks, 0, "Should have 0 failed tasks");
        
        // Verify average calculations are updated
        assert!(final_metrics.avg_queue_time_ms > 0.0, "Average queue time should be positive");
        assert!(final_metrics.avg_processing_time_ms > 0.0, "Average processing time should be positive");
        
        task_monitor.stop().await.expect("Task monitor should stop");
        println!("Task status edge cases and validation test passed");
    }



// =================================================================================================
// Task History Management Tests
// =================================================================================================

#[tokio::test]
async fn test_task_history_storage_and_retrieval() {
    use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::TaskMonitoring;
    
    println!("Starting task history storage and retrieval test");
    
    // Create task monitor with real objects
    let mut task_monitor = create_fast_task_monitor();
    task_monitor.start().await.expect("Task monitor should start");
    
    // Test 1: Store multiple tasks in history
    println!("Testing task history storage");
    let mut task_ids = Vec::new();
    
    for i in 0..5 {
        let task_id = TaskId::new();
        task_ids.push(task_id.clone());
        
        let task_metrics = TaskMetrics::new(
            task_id.clone(),
            TaskCategory::LLMInference,
            TaskPriority::Normal
        );
        
        task_monitor.record_task_created(task_metrics).await.expect("Should create task");
        task_monitor.record_task_started(&task_id, &format!("queue_{}", i)).await.expect("Should start task");
        
        // Add small delay to ensure different timestamps
        sleep(Duration::from_millis(10)).await;
        
        task_monitor.record_task_completed(&task_id, true, None).await.expect("Should complete task");
    }
    
    // Test 2: Retrieve tasks from history
    println!("Testing task history retrieval");
    let metrics = task_monitor.get_metrics().await.unwrap();
    
    assert_eq!(metrics.completed_tasks.len(), 5, "Should have 5 completed tasks in history");
    assert_eq!(metrics.total_tasks, 5, "Should have processed 5 total tasks");
    assert_eq!(metrics.successful_tasks, 5, "Should have 5 successful tasks");
    
    // Test 3: Verify task details are preserved in history
    println!("Testing task history detail preservation");
    for (i, completed_task) in metrics.completed_tasks.iter().enumerate() {
        assert_eq!(completed_task.category, TaskCategory::LLMInference, "Category should be preserved");
        assert_eq!(completed_task.priority, TaskPriority::Normal, "Priority should be preserved");
        assert_eq!(completed_task.status, TaskStatus::Completed, "Status should be Completed");
        assert!(completed_task.queue_name.is_some(), "Queue name should be preserved");
        assert_eq!(completed_task.queue_name.as_ref().unwrap(), &format!("queue_{}", i), "Queue name should match");
        assert!(completed_task.queue_time.is_some(), "Queue time should be recorded");
        assert!(completed_task.processing_time.is_some(), "Processing time should be recorded");
    }
    
    // Test 4: Retrieve specific task from history
    println!("Testing specific task retrieval from history");
    for task_id in &task_ids {
        let retrieved_task = task_monitor.get_task_metrics(task_id).await.unwrap();
        assert!(retrieved_task.is_some(), "Should be able to retrieve completed task from history");
        
        let task_metrics = retrieved_task.unwrap();
        assert_eq!(task_metrics.task_id, *task_id, "Retrieved task should have correct ID");
        assert_eq!(task_metrics.status, TaskStatus::Completed, "Retrieved task should be completed");
    }
    
    task_monitor.stop().await.expect("Task monitor should stop");
    println!("Task history storage and retrieval test passed");
}

#[tokio::test]
async fn test_task_history_cleanup_and_maintenance() {
    use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::TaskMonitoring;
    
    println!("Starting task history cleanup and maintenance test");
    
    // Create task monitor with small history limit for testing
    let config = PrismaMonitorConfig {
        queue_poll_interval_ms: 50,
        task_poll_interval_ms: 50,
        max_task_history: 3, // Small limit for testing cleanup
        enable_detailed_task_tracking: true,
    };
    let mut task_monitor = TaskMonitor::new(config);
    task_monitor.start().await.expect("Task monitor should start");
    
    // Test 1: Fill history beyond limit
    println!("Testing history cleanup when limit exceeded");
    let mut task_ids = Vec::new();
    
    for i in 0..6 {
        let task_id = TaskId::new();
        task_ids.push(task_id.clone());
        
        let task_metrics = TaskMetrics::new(
            task_id.clone(),
            TaskCategory::FileProcessing,
            TaskPriority::Low
        );
        
        task_monitor.record_task_created(task_metrics).await.expect("Should create task");
        task_monitor.record_task_started(&task_id, "cleanup_queue").await.expect("Should start task");
        
        // Add delay to ensure different timestamps
        sleep(Duration::from_millis(5)).await;
        
        task_monitor.record_task_completed(&task_id, true, None).await.expect("Should complete task");
    }
    
    // Test 2: Verify cleanup occurred
    println!("Testing that cleanup maintains history limit");
    let metrics = task_monitor.get_metrics().await.unwrap();
    
    assert_eq!(metrics.completed_tasks.len(), 3, "Should maintain max history limit of 3");
    assert_eq!(metrics.total_tasks, 6, "Total task count should still be accurate");
    assert_eq!(metrics.successful_tasks, 6, "Successful task count should still be accurate");
    
    // Test 3: Verify oldest tasks were removed (FIFO cleanup)
    println!("Testing FIFO cleanup behavior");
    let remaining_task_ids: Vec<TaskId> = metrics.completed_tasks.iter()
        .map(|task| task.task_id.clone())
        .collect();
    
    // Should contain the last 3 tasks (indices 3, 4, 5)
    for i in 3..6 {
        assert!(remaining_task_ids.contains(&task_ids[i]), 
            "Should contain recent task {}", i);
    }
    
    // Should not contain the first 3 tasks (indices 0, 1, 2)
    for i in 0..3 {
        assert!(!remaining_task_ids.contains(&task_ids[i]), 
            "Should not contain old task {}", i);
    }
    
    // Test 4: Verify removed tasks are no longer retrievable
    println!("Testing that cleaned up tasks are no longer retrievable");
    for i in 0..3 {
        let retrieved_task = task_monitor.get_task_metrics(&task_ids[i]).await.unwrap();
        assert!(retrieved_task.is_none(), "Old task {} should not be retrievable", i);
    }
    
    // Test 5: Verify recent tasks are still retrievable
    println!("Testing that recent tasks remain retrievable");
    for i in 3..6 {
        let retrieved_task = task_monitor.get_task_metrics(&task_ids[i]).await.unwrap();
        assert!(retrieved_task.is_some(), "Recent task {} should still be retrievable", i);
    }
    
    task_monitor.stop().await.expect("Task monitor should stop");
    println!("Task history cleanup and maintenance test passed");
}

#[tokio::test]
async fn test_task_history_size_limits_and_rotation() {
    use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::TaskMonitoring;
    
    println!("Starting task history size limits and rotation test");
    
    // Test 1: Very small history limit
    println!("Testing very small history limit (1 task)");
    let config = PrismaMonitorConfig {
        queue_poll_interval_ms: 50,
        task_poll_interval_ms: 50,
        max_task_history: 1,
        enable_detailed_task_tracking: true,
    };
    let mut task_monitor = TaskMonitor::new(config);
    task_monitor.start().await.expect("Task monitor should start");
    
    let mut task_ids = Vec::new();
    
    // Create 3 tasks with small history limit
    for i in 0..3 {
        let task_id = TaskId::new();
        task_ids.push(task_id.clone());
        
        let task_metrics = TaskMetrics::new(
            task_id.clone(),
            TaskCategory::Custom("ModelTraining".to_string()),
            TaskPriority::High
        );
        
        task_monitor.record_task_created(task_metrics).await.expect("Should create task");
        task_monitor.record_task_started(&task_id, "rotation_queue").await.expect("Should start task");
        
        sleep(Duration::from_millis(5)).await;
        
        task_monitor.record_task_completed(&task_id, true, None).await.expect("Should complete task");
        
        // Check history size after each completion
        let metrics = task_monitor.get_metrics().await.unwrap();
        assert!(metrics.completed_tasks.len() <= 1, "History should never exceed limit of 1");
    }
    
    // Verify only the last task remains
    let final_metrics = task_monitor.get_metrics().await.unwrap();
    assert_eq!(final_metrics.completed_tasks.len(), 1, "Should have exactly 1 task in history");
    assert_eq!(final_metrics.completed_tasks[0].task_id, task_ids[2], "Should contain the last task");
    
    task_monitor.stop().await.expect("Task monitor should stop");
    
    // Test 2: Medium history limit with mixed success/failure
    println!("Testing medium history limit with mixed results");
    let config = PrismaMonitorConfig {
        queue_poll_interval_ms: 50,
        task_poll_interval_ms: 50,
        max_task_history: 5,
        enable_detailed_task_tracking: true,
    };
    let mut task_monitor = TaskMonitor::new(config);
    task_monitor.start().await.expect("Task monitor should start");
    
    // Create 8 tasks with mixed success/failure
    for i in 0..8 {
        let task_id = TaskId::new();
        let success = i % 2 == 0; // Alternate success/failure
        let error_msg = if success { None } else { Some(format!("Error in task {}", i)) };
        
        let task_metrics = TaskMetrics::new(
            task_id.clone(),
            TaskCategory::FileProcessing,
            TaskPriority::Normal
        );
        
        task_monitor.record_task_created(task_metrics).await.expect("Should create task");
        task_monitor.record_task_started(&task_id, "mixed_queue").await.expect("Should start task");
        
        sleep(Duration::from_millis(5)).await;
        
        task_monitor.record_task_completed(&task_id, success, error_msg).await.expect("Should complete task");
    }
    
    // Verify history limit and mixed results
    let final_metrics = task_monitor.get_metrics().await.unwrap();
    assert_eq!(final_metrics.completed_tasks.len(), 5, "Should maintain history limit of 5");
    assert_eq!(final_metrics.total_tasks, 8, "Should track all 8 tasks");
    assert_eq!(final_metrics.successful_tasks, 4, "Should have 4 successful tasks");
    assert_eq!(final_metrics.failed_tasks, 4, "Should have 4 failed tasks");
    
    // Verify the last 5 tasks are preserved with correct status
    let expected_statuses = vec![
        TaskStatus::Failed,    // Task 3 (i=3, 3%2!=0, failure)
        TaskStatus::Completed, // Task 4 (i=4, 4%2==0, success)
        TaskStatus::Failed,    // Task 5 (i=5, 5%2!=0, failure)
        TaskStatus::Completed, // Task 6 (i=6, 6%2==0, success)
        TaskStatus::Failed,    // Task 7 (i=7, 7%2!=0, failure)
    ];
    
    for (i, task) in final_metrics.completed_tasks.iter().enumerate() {
        assert_eq!(task.status, expected_statuses[i], "Task {} should have correct status", i + 3);
        if task.status == TaskStatus::Failed {
            assert!(task.error_message.is_some(), "Failed task should have error message");
        }
    }
    
    task_monitor.stop().await.expect("Task monitor should stop");
    println!("Task history size limits and rotation test passed");
}

#[tokio::test]
async fn test_task_history_performance_optimization() {
    use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::TaskMonitoring;
    use std::time::Instant;
    
    println!("Starting task history performance optimization test");
    
    // Test 1: Large batch task processing performance
    println!("Testing large batch task processing performance");
    let config = PrismaMonitorConfig {
        queue_poll_interval_ms: 100, // Slower polling for performance test
        task_poll_interval_ms: 100,
        max_task_history: 100,
        enable_detailed_task_tracking: true,
    };
    let mut task_monitor = TaskMonitor::new(config);
    task_monitor.start().await.expect("Task monitor should start");
    
    let batch_size = 50;
    let start_time = Instant::now();
    
    // Create and complete a large batch of tasks
    for i in 0..batch_size {
        let task_id = TaskId::new();
        
        let task_metrics = TaskMetrics::new(
            task_id.clone(),
            TaskCategory::LLMInference,
            TaskPriority::Normal
        );
        
        task_monitor.record_task_created(task_metrics).await.expect("Should create task");
        task_monitor.record_task_started(&task_id, "perf_queue").await.expect("Should start task");
        task_monitor.record_task_completed(&task_id, true, None).await.expect("Should complete task");
        
        // Minimal delay to avoid overwhelming the system
        if i % 10 == 0 {
            sleep(Duration::from_millis(1)).await;
        }
    }
    
    let batch_duration = start_time.elapsed();
    println!("Processed {} tasks in {:?}", batch_size, batch_duration);
    
    // Verify all tasks were processed correctly
    let metrics = task_monitor.get_metrics().await.unwrap();
    assert_eq!(metrics.total_tasks, batch_size, "Should have processed all tasks");
    assert_eq!(metrics.successful_tasks, batch_size, "All tasks should be successful");
    assert_eq!(metrics.completed_tasks.len(), batch_size, "All tasks should be in history");
    
    // Performance assertion: should complete within reasonable time
    assert!(batch_duration.as_millis() < 5000, "Batch processing should complete within 5 seconds");
    
    // Test 2: History retrieval performance
    println!("Testing history retrieval performance");
    let retrieval_start = Instant::now();
    
    // Retrieve metrics multiple times to test caching/performance
    for _ in 0..10 {
        let _metrics = task_monitor.get_metrics().await.unwrap();
    }
    
    let retrieval_duration = retrieval_start.elapsed();
    println!("Retrieved metrics 10 times in {:?}", retrieval_duration);
    
    // Performance assertion: retrieval should be fast
    assert!(retrieval_duration.as_millis() < 100, "Metrics retrieval should be fast");
    
    // Test 3: Individual task lookup performance
    println!("Testing individual task lookup performance");
    let lookup_start = Instant::now();
    
    // Get a task ID from the completed tasks
    let sample_task_id = metrics.completed_tasks[25].task_id.clone(); // Middle task
    
    // Perform multiple lookups
    for _ in 0..20 {
        let _task = task_monitor.get_task_metrics(&sample_task_id).await.unwrap();
    }
    
    let lookup_duration = lookup_start.elapsed();
    println!("Performed 20 task lookups in {:?}", lookup_duration);
    
    // Performance assertion: individual lookups should be fast
    assert!(lookup_duration.as_millis() < 50, "Individual task lookups should be fast");
    
    // Test 4: Memory efficiency with history rotation
    println!("Testing memory efficiency with history rotation");
    let rotation_start = Instant::now();
    
    // Add more tasks to trigger history rotation
    for i in 0..60 { // This will cause rotation since max_task_history is 100
        let task_id = TaskId::new();
        
        let task_metrics = TaskMetrics::new(
            task_id.clone(),
            TaskCategory::FileProcessing,
            TaskPriority::Low
        );
        
        task_monitor.record_task_created(task_metrics).await.expect("Should create task");
        task_monitor.record_task_started(&task_id, "rotation_queue").await.expect("Should start task");
        task_monitor.record_task_completed(&task_id, true, None).await.expect("Should complete task");
        
        if i % 20 == 0 {
            sleep(Duration::from_millis(1)).await;
        }
    }
    
    let rotation_duration = rotation_start.elapsed();
    println!("Processed additional 60 tasks with rotation in {:?}", rotation_duration);
    
    // Verify history was properly rotated
    let final_metrics = task_monitor.get_metrics().await.unwrap();
    assert_eq!(final_metrics.completed_tasks.len(), 100, "Should maintain max history size");
    assert_eq!(final_metrics.total_tasks, batch_size + 60, "Should track all processed tasks");
    
    // Performance assertion: rotation should not significantly impact performance
    assert!(rotation_duration.as_millis() < 3000, "History rotation should be efficient");
    
    // Test 5: Average calculation performance
    println!("Testing average calculation performance");
    let avg_start = Instant::now();
    
    // Verify averages are calculated correctly and efficiently
    // Note: Queue time might be 0 if tasks are processed very quickly
    assert!(final_metrics.avg_queue_time_ms >= 0.0, "Average queue time should be non-negative");
    assert!(final_metrics.avg_processing_time_ms >= 0.0, "Average processing time should be non-negative");
    
    let avg_duration = avg_start.elapsed();
    println!("Average calculations completed in {:?}", avg_duration);
    println!("Average queue time: {:.2}ms, Average processing time: {:.2}ms", 
             final_metrics.avg_queue_time_ms, final_metrics.avg_processing_time_ms);
    
    // Performance assertion: average calculations should be fast
    assert!(avg_duration.as_millis() < 10, "Average calculations should be very fast");
    
    task_monitor.stop().await.expect("Task monitor should stop");
    println!("Task history performance optimization test passed");
}

// =================================================================================================
// Task Monitor Lifecycle Tests
// =================================================================================================

#[tokio::test]
async fn test_task_monitor_initialization_and_configuration() {
    use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::Monitorable;
    
    println!("Starting task monitor initialization and configuration test");
    
    // Test 1: Default configuration initialization
    println!("Testing default configuration initialization");
    let default_config = PrismaMonitorConfig::default();
    let mut default_monitor = TaskMonitor::new(default_config.clone());
    
    // Verify initial state
    assert_eq!(default_monitor.get_name(), "TaskMonitor", "Monitor name should be correct");
    let initial_status = default_monitor.get_status().await.expect("Should get status");
    assert_eq!(initial_status, "Stopped", "Monitor should initially be stopped");
    
    // Test 2: Custom configuration initialization
    println!("Testing custom configuration initialization");
    let custom_config = PrismaMonitorConfig {
        queue_poll_interval_ms: 50,
        task_poll_interval_ms: 25,
        max_task_history: 500,
        enable_detailed_task_tracking: true,
    };
    let mut custom_monitor = TaskMonitor::new(custom_config);
    
    // Verify custom monitor can be created
    assert_eq!(custom_monitor.get_name(), "TaskMonitor", "Custom monitor name should be correct");
    let custom_status = custom_monitor.get_status().await.expect("Should get custom status");
    assert_eq!(custom_status, "Stopped", "Custom monitor should initially be stopped");
    
    // Test 3: Configuration with extreme values
    println!("Testing configuration with extreme values");
    let extreme_config = PrismaMonitorConfig {
        queue_poll_interval_ms: 1, // Very fast polling
        task_poll_interval_ms: 1,
        max_task_history: 10, // Very small history
        enable_detailed_task_tracking: false,
    };
    let mut extreme_monitor = TaskMonitor::new(extreme_config);
    
    // Verify extreme configuration works
    let extreme_status = extreme_monitor.get_status().await.expect("Should get extreme status");
    assert_eq!(extreme_status, "Stopped", "Extreme monitor should initially be stopped");
    
    // Test 4: Initial metrics state
    println!("Testing initial metrics state");
    let initial_metrics = default_monitor.get_metrics().await.expect("Should get initial metrics");
    assert_eq!(initial_metrics.active_tasks.len(), 0, "Should have no active tasks initially");
    assert_eq!(initial_metrics.completed_tasks.len(), 0, "Should have no completed tasks initially");
    assert_eq!(initial_metrics.total_tasks, 0, "Should have zero total tasks initially");
    assert_eq!(initial_metrics.successful_tasks, 0, "Should have zero successful tasks initially");
    assert_eq!(initial_metrics.failed_tasks, 0, "Should have zero failed tasks initially");
    assert_eq!(initial_metrics.avg_queue_time_ms, 0.0, "Should have zero average queue time initially");
    assert_eq!(initial_metrics.avg_processing_time_ms, 0.0, "Should have zero average processing time initially");
    
    println!("Task monitor initialization and configuration test passed");
}

#[tokio::test]
async fn test_task_monitor_task_management() {
    use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::TaskMonitoring;
    
    println!("Starting task monitor task management test");
    
    let mut task_monitor = create_fast_task_monitor();
    task_monitor.start().await.expect("Task monitor should start");
    
    // Test 1: Task creation and tracking
    println!("Testing task creation and tracking");
    let task_id_1 = TaskId::new();
    let task_metrics_1 = TaskMetrics::new(task_id_1.clone(), TaskCategory::LLMInference, TaskPriority::High);
    
    task_monitor.record_task_created(task_metrics_1).await.expect("Should create task 1");
    
    let retrieved_task = task_monitor.get_task_metrics(&task_id_1).await.expect("Should get task metrics").expect("Task should exist");
    assert_eq!(retrieved_task.task_id, task_id_1, "Task ID should match");
    assert_eq!(retrieved_task.category, TaskCategory::LLMInference, "Task category should match");
    assert_eq!(retrieved_task.priority, TaskPriority::High, "Task priority should match");
    assert_eq!(retrieved_task.status, TaskStatus::Queued, "Task should be queued initially");
    
    // Test 2: Multiple task management
    println!("Testing multiple task management");
    let mut task_ids = Vec::new();
    for i in 0..5 {
        let task_id = TaskId::new();
        let category = match i % 3 {
            0 => TaskCategory::DatabaseQuery,
            1 => TaskCategory::FileProcessing,
            _ => TaskCategory::NetworkRequest,
        };
        let priority = match i % 3 {
            0 => TaskPriority::Low,
            1 => TaskPriority::Normal,
            _ => TaskPriority::High,
        };
        
        let task_metrics = TaskMetrics::new(task_id.clone(), category, priority);
        task_monitor.record_task_created(task_metrics).await.expect("Should create task");
        task_ids.push(task_id);
    }
    
    // Verify all tasks are tracked
    let metrics = task_monitor.get_metrics().await.expect("Should get metrics");
    assert_eq!(metrics.active_tasks.len(), 6, "Should have 6 active tasks"); // 1 + 5
    assert_eq!(metrics.total_tasks, 6, "Should have 6 total tasks");
    
    // Test 3: Task lifecycle management
    println!("Testing task lifecycle management");
    let lifecycle_task_id = task_ids[0].clone();
    
    // Start task
    task_monitor.record_task_started(&lifecycle_task_id, "test_queue").await.expect("Should start task");
    let started_task = task_monitor.get_task_metrics(&lifecycle_task_id).await.expect("Should get task").expect("Task should exist");
    assert_eq!(started_task.status, TaskStatus::Processing, "Task should be processing");
    assert!(started_task.queue_name.is_some(), "Task should have queue name");
    assert_eq!(started_task.queue_name.unwrap(), "test_queue", "Queue name should match");
    
    // Complete task successfully
    task_monitor.record_task_completed(&lifecycle_task_id, true, None).await.expect("Should complete task");
    let completed_task = task_monitor.get_task_metrics(&lifecycle_task_id).await.expect("Should get task").expect("Task should exist");
    assert_eq!(completed_task.status, TaskStatus::Completed, "Task should be completed");
    
    // Verify task moved to completed list
    let final_metrics = task_monitor.get_metrics().await.expect("Should get final metrics");
    assert_eq!(final_metrics.active_tasks.len(), 5, "Should have 5 active tasks remaining");
    assert_eq!(final_metrics.completed_tasks.len(), 1, "Should have 1 completed task");
    assert_eq!(final_metrics.successful_tasks, 1, "Should have 1 successful task");
    
    // Test 4: Task failure management
    println!("Testing task failure management");
    let failure_task_id = task_ids[1].clone();
    
    task_monitor.record_task_started(&failure_task_id, "failure_queue").await.expect("Should start failure task");
    task_monitor.record_task_completed(&failure_task_id, false, Some("Test error message".to_string())).await.expect("Should fail task");
    
    let failed_task = task_monitor.get_task_metrics(&failure_task_id).await.expect("Should get failed task").expect("Task should exist");
    assert_eq!(failed_task.status, TaskStatus::Failed, "Task should be failed");
    assert!(failed_task.error_message.is_some(), "Failed task should have error message");
    assert_eq!(failed_task.error_message.unwrap(), "Test error message", "Error message should match");
    
    let failure_metrics = task_monitor.get_metrics().await.expect("Should get failure metrics");
    assert_eq!(failure_metrics.failed_tasks, 1, "Should have 1 failed task");
    assert_eq!(failure_metrics.completed_tasks.len(), 2, "Should have 2 completed tasks total");
    
    task_monitor.stop().await.expect("Task monitor should stop");
    println!("Task monitor task management test passed");
}

#[tokio::test]
async fn test_task_monitor_start_stop_operations() {
    use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::{TaskMonitoring, Monitorable};
    
    println!("Starting task monitor start/stop operations test");
    
    let mut task_monitor = create_fast_task_monitor();
    
    // Test 1: Initial state verification
    println!("Testing initial state");
    let initial_status = task_monitor.get_status().await.expect("Should get initial status");
    assert_eq!(initial_status, "Stopped", "Monitor should initially be stopped");
    
    // Test 2: Start operation
    println!("Testing start operation");
    task_monitor.start().await.expect("Should start successfully");
    
    let running_status = task_monitor.get_status().await.expect("Should get running status");
    assert_eq!(running_status, "Running", "Monitor should be running after start");
    
    // Test 3: Double start (should not fail)
    println!("Testing double start operation");
    task_monitor.start().await.expect("Double start should not fail");
    
    let still_running_status = task_monitor.get_status().await.expect("Should get status after double start");
    assert_eq!(still_running_status, "Running", "Monitor should still be running after double start");
    
    // Test 4: Monitor functionality while running
    println!("Testing monitor functionality while running");
    let task_id = TaskId::new();
    let task_metrics = TaskMetrics::new(task_id.clone(), TaskCategory::LLMInference, TaskPriority::Normal);
    
    task_monitor.record_task_created(task_metrics).await.expect("Should create task while running");
    task_monitor.record_task_started(&task_id, "running_queue").await.expect("Should start task while running");
    
    // Allow some time for background monitoring
    sleep(Duration::from_millis(50)).await;
    
    let metrics = task_monitor.get_metrics().await.expect("Should get metrics while running");
    assert_eq!(metrics.active_tasks.len(), 1, "Should have 1 active task while running");
    
    // Test 5: Stop operation
    println!("Testing stop operation");
    task_monitor.stop().await.expect("Should stop successfully");
    
    let stopped_status = task_monitor.get_status().await.expect("Should get stopped status");
    assert_eq!(stopped_status, "Stopped", "Monitor should be stopped after stop");
    
    // Test 6: Double stop (should not fail)
    println!("Testing double stop operation");
    task_monitor.stop().await.expect("Double stop should not fail");
    
    let still_stopped_status = task_monitor.get_status().await.expect("Should get status after double stop");
    assert_eq!(still_stopped_status, "Stopped", "Monitor should still be stopped after double stop");
    
    // Test 7: Functionality after stop
    println!("Testing functionality after stop");
    let post_stop_task_id = TaskId::new();
    let post_stop_metrics = TaskMetrics::new(post_stop_task_id.clone(), TaskCategory::DatabaseQuery, TaskPriority::Low);
    
    // Task operations should still work even when monitor is stopped
    task_monitor.record_task_created(post_stop_metrics).await.expect("Should create task after stop");
    
    let final_metrics = task_monitor.get_metrics().await.expect("Should get metrics after stop");
    assert_eq!(final_metrics.active_tasks.len(), 2, "Should have 2 active tasks after stop");
    
    // Test 8: Restart operation
    println!("Testing restart operation");
    task_monitor.start().await.expect("Should restart successfully");
    
    let restarted_status = task_monitor.get_status().await.expect("Should get restarted status");
    assert_eq!(restarted_status, "Running", "Monitor should be running after restart");
    
    // Allow time for monitoring to resume
    sleep(Duration::from_millis(50)).await;
    
    // Clean stop
    task_monitor.stop().await.expect("Should stop after restart test");
    
    println!("Task monitor start/stop operations test passed");
}

#[tokio::test]
async fn test_task_monitor_error_handling_and_recovery() {
    use prisma_ai::prisma::prisma_engine::monitor::prisma::traits::TaskMonitoring;
    
    println!("Starting task monitor error handling and recovery test");
    
    let mut task_monitor = create_fast_task_monitor();
    task_monitor.start().await.expect("Task monitor should start");
    
    // Test 1: Duplicate task creation error
    println!("Testing duplicate task creation error handling");
    let duplicate_task_id = TaskId::new();
    let task_metrics_1 = TaskMetrics::new(duplicate_task_id.clone(), TaskCategory::LLMInference, TaskPriority::High);
    let task_metrics_2 = TaskMetrics::new(duplicate_task_id.clone(), TaskCategory::DatabaseQuery, TaskPriority::Low);
    
    // First creation should succeed
    task_monitor.record_task_created(task_metrics_1).await.expect("First task creation should succeed");
    
    // Second creation should fail
    let duplicate_result = task_monitor.record_task_created(task_metrics_2).await;
    assert!(duplicate_result.is_err(), "Duplicate task creation should fail");
    
    // Verify monitor is still functional after error
    let metrics_after_error = task_monitor.get_metrics().await.expect("Should get metrics after duplicate error");
    assert_eq!(metrics_after_error.active_tasks.len(), 1, "Should still have 1 active task after error");
    
    // Test 2: Non-existent task operations error
    println!("Testing non-existent task operations error handling");
    let non_existent_task_id = TaskId::new();
    
    // Update status of non-existent task should fail
    let update_result = task_monitor.update_task_status(&non_existent_task_id, TaskStatus::Processing).await;
    assert!(update_result.is_err(), "Updating non-existent task should fail");
    
    // Start non-existent task should fail
    let start_result = task_monitor.record_task_started(&non_existent_task_id, "test_queue").await;
    assert!(start_result.is_err(), "Starting non-existent task should fail");
    
    // Complete non-existent task should fail
    let complete_result = task_monitor.record_task_completed(&non_existent_task_id, true, None).await;
    assert!(complete_result.is_err(), "Completing non-existent task should fail");
    
    // Verify monitor is still functional after errors
    let metrics_after_non_existent = task_monitor.get_metrics().await.expect("Should get metrics after non-existent errors");
    assert_eq!(metrics_after_non_existent.active_tasks.len(), 1, "Should still have 1 active task after non-existent errors");
    
    // Test 3: Recovery after errors
    println!("Testing recovery after errors");
    let recovery_task_id = TaskId::new();
    let recovery_metrics = TaskMetrics::new(recovery_task_id.clone(), TaskCategory::FileProcessing, TaskPriority::Normal);
    
    // Normal operations should work after errors
    task_monitor.record_task_created(recovery_metrics).await.expect("Should create recovery task");
    task_monitor.record_task_started(&recovery_task_id, "recovery_queue").await.expect("Should start recovery task");
    task_monitor.record_task_completed(&recovery_task_id, true, None).await.expect("Should complete recovery task");
    
    let recovery_metrics = task_monitor.get_metrics().await.expect("Should get recovery metrics");
    assert_eq!(recovery_metrics.active_tasks.len(), 1, "Should have 1 active task after recovery");
    assert_eq!(recovery_metrics.completed_tasks.len(), 1, "Should have 1 completed task after recovery");
    assert_eq!(recovery_metrics.successful_tasks, 1, "Should have 1 successful task after recovery");
    
    // Test 4: Error handling with task failures
    println!("Testing error handling with task failures");
    let error_task_id = TaskId::new();
    let error_task_metrics = TaskMetrics::new(error_task_id.clone(), TaskCategory::NetworkRequest, TaskPriority::High);
    
    task_monitor.record_task_created(error_task_metrics).await.expect("Should create error task");
    task_monitor.record_task_started(&error_task_id, "error_queue").await.expect("Should start error task");
    
    // Simulate task failure with detailed error message
    let error_message = "Network timeout after 30 seconds - connection refused by remote server";
    task_monitor.record_task_completed(&error_task_id, false, Some(error_message.to_string())).await.expect("Should handle task failure");
    
    let error_task = task_monitor.get_task_metrics(&error_task_id).await.expect("Should get error task").expect("Error task should exist");
    assert_eq!(error_task.status, TaskStatus::Failed, "Error task should be failed");
    assert!(error_task.error_message.is_some(), "Error task should have error message");
    assert_eq!(error_task.error_message.unwrap(), error_message, "Error message should match");
    
    // Test 5: Monitor resilience under stress
    println!("Testing monitor resilience under stress with errors");
    let mut stress_task_ids = Vec::new();
    
    // Create multiple tasks with some intentional errors
    for i in 0..20 {
        let task_id = TaskId::new();
        let task_metrics = TaskMetrics::new(task_id.clone(), TaskCategory::LLMInference, TaskPriority::Normal);
        
        task_monitor.record_task_created(task_metrics).await.expect("Should create stress task");
        stress_task_ids.push(task_id);
        
        // Try to create duplicate (should fail but not break monitor)
        if i % 5 == 0 {
            let duplicate_metrics = TaskMetrics::new(task_id.clone(), TaskCategory::DatabaseQuery, TaskPriority::Low);
            let _ = task_monitor.record_task_created(duplicate_metrics).await; // Ignore error
        }
    }
    
    // Process tasks with some failures
    for (i, task_id) in stress_task_ids.iter().enumerate() {
        task_monitor.record_task_started(task_id, "stress_queue").await.expect("Should start stress task");
        
        let success = i % 4 != 0; // 75% success rate
        let error_msg = if success { None } else { Some(format!("Stress test error {}", i)) };
        
        task_monitor.record_task_completed(task_id, success, error_msg).await.expect("Should complete stress task");
    }
    
    // Verify monitor handled stress correctly
    let final_metrics = task_monitor.get_metrics().await.expect("Should get final metrics");
    assert_eq!(final_metrics.active_tasks.len(), 1, "Should have 1 remaining active task"); // Original task still active
    assert_eq!(final_metrics.completed_tasks.len(), 22, "Should have 22 completed tasks"); // 1 recovery + 1 error + 20 stress
    assert!(final_metrics.successful_tasks > 0, "Should have some successful tasks");
    assert!(final_metrics.failed_tasks > 0, "Should have some failed tasks");
    assert_eq!(final_metrics.successful_tasks + final_metrics.failed_tasks, 22, "Success + failed should equal completed");
    
    task_monitor.stop().await.expect("Task monitor should stop");
    println!("Task monitor error handling and recovery test passed");
}
