So I have the - /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor module and I would you to analyze it and implement tests in the - /Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/monitor_tests_v4.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration_tests.rs ( entry point) files. the test will NOT use mocks or placeholders but real methods/components. the following tests to implement will be - #### Performance and Scalability Tests
- Test monitor performance under high system load
- Test monitor memory usage and optimization
- Test monitor CPU overhead and efficiency
- Test monitor scalability with increasing metrics volume

Keep top-level imports minimal - Only types, no traits
Import traits locally - Within {} blocks where needed
Use helper functions - For creating monitors without trait dependencies
One trait per test block - Avoid mixing conflicting traits in same scope
Test-specific imports - Import only what each test actually uses




Follow the same pattern as the previous tests - /Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/decision_maker_tests.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/execution_strategies_tests.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/executor_tests.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/agent_manager_tests.rs
	





Do  a full analysis on the monitor module - /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/prisma/mod.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/prisma/queue_monitor.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/prisma/task_monitor.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/prisma/traits.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/prisma/types.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/cpu.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/disk.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/memory.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/mod.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/network.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/system_info.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/traits.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/system/types.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/generics.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/mod.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/monitor.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/traits.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor/types.rs -- and when completed, generate a comprhensive intergrated test plan in the /Users/<USER>/Documents/prisma_workspace/Instructions/tests.md. The tests will be done in /Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/monitor_tests.rs
	




